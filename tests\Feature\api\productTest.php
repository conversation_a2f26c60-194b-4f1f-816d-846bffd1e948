<?php

namespace Tests\Feature\api;


/***
"功能名稱":"產品 單位測試",
"資料表":"product",
"建立時間":"2024-10-28 08:23:48 ",
 ***/
//command "php artisan test --filter tests/Feature//productTest.php
class productTest extends baseTest {
  public $product;
  public function setUp(): void {
    parent::setUp();
    $this->kind = \App\Models\kind::factory()->create([
      //            'domain_id' => $this->domain->id,
    ]); //種類
    $this->product = \App\Models\product::factory()->create([
      'kind_id' => $this->kind->id,
    ]); //編輯日期
  }
  /**
   * 測試資料列表功能.
   *
   * @return void
   */
  public function test_列表index產品() {


    $datas = [

      'header' =>
      array(
        'CONTENT_TYPE' => 'application/json',
      ),
      'url' => '/api/product',
      'raw' =>
      array(
        'page' => 1,
        'pagesize' => 10,
        'searchname' =>  'kind_id',
        'search' => $this->product->kind_id,
      ),
      'post' => NULL,

    ];

    $response = $this->withHeaders($datas['header'])
      ->json('POST', $datas['url'], $datas['raw']);
    // echo $response->getStatusCode();
    //echo "response" . $response->getContent();

    $this->checkJson($response);

    //檢查資料表資料是否存在
    $this->assertDatabaseHas('product', [
      'id' => $this->product->id,
    ]);
    //檢查回傳資料是否存在
    $response
      ->assertStatus(200)
      ->assertJsonPath('data.data.0.kind_id', $this->product->kind_id);
    $jsonArray = $response->json();
    $this->assertGreaterThan(1, count($jsonArray['data']));
  }
  public function test_單筆顯示show產品() {

    $datas = [

      'header' =>
      array(
        'CONTENT_TYPE' => 'application/json',
      ),
      'url' => '/api/product/show',
      'raw' =>
      array(

        'id' => $this->product->id,

      ),
      'post' => NULL,

    ];

    $response = $this->withHeaders($datas['header'])
      ->json('POST', $datas['url'], $datas['raw']);
    // echo $response->getStatusCode();
    //echo "response" . $response->getContent();


    $this->checkJson($response);
    //檢查回傳資料是否存在
    $response
      ->assertStatus(200)
      ->assertJsonPath('data.id', $this->product->id);
    //->assertJsonPath('data.clesson.chapter.0.id', $this->chapter->id)




  }
}
