# TASK005 - 成團滑雪功能

## 任務描述

建立成團滑雪功能，讓平台可以發布滑雪團活動，會員可以報名參加滑雪團。此功能提供團體滑雪體驗，增加平台的服務多樣性。

### 主要功能內容：

1. 滑雪團列表展示頁面
2. 滑雪團詳細資訊頁面
3. 會員報名滑雪團功能
4. 滑雪團報名狀態管理
5. 滑雪團成團機制
6. 報名記錄查詢功能

## 狀態

-   [x] 計劃中
-   [ ] 測試單元編寫中
-   [ ] 開發中
-   [ ] 完成

## 驗收標準

-   [ ] 滑雪團列表頁面能正常顯示所有可報名的滑雪團
-   [ ] 滑雪團詳細頁面能完整展示活動資訊
-   [ ] 會員可以成功報名滑雪團
-   [ ] 滑雪團報名人數統計正確
-   [ ] 成團機制運作正常（達到最低人數自動成團）
-   [ ] 會員可以查看報名記錄
-   [ ] 滑雪團狀態更新正確（報名中、成團、已出發、已結束、已取消）
-   [ ] 報名截止時間控制正確
-   [ ] 響應式設計在各裝置正常顯示

## 注意事項

-   滑雪團資料存放在 skiTrip 資料表
-   報名記錄可以使用 booking 資料表（增加滑雪團類型）
-   滑雪團圖片存放在 `public/images/skitrip/` 目錄
-   滑雪團狀態：報名中、成團、已出發、已結束、已取消
-   需要設定最低成團人數和最高報名人數
-   已登入會員才能報名滑雪團
-   未登入使用者可以瀏覽滑雪團資訊
-   報名頁面放在 `membercenter` 目錄（需登入）
-   列表頁面可以放在根目錄（公開瀏覽）
