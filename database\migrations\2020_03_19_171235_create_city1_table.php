<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateCity1Table extends Migration {
    /**
     * Run the migrations.
     */
    public function up() {
        if (!Schema::hasTable('city1')) {
            Schema::create('city1', function (Blueprint $table) {
                $table->string('city1title', 50)->unique()->comment('縣市');
                $table->float('sortnum', 5, 3)->comment('排序號碼');
                $table->bigIncrements('city1id')->comment('自動編號');
                $table->integer('online')->comment('上下架');
                $table->string('entitle', 100)->comment('英文');
                $table->integer('partid')->comment('地區編號');
                $table->timestamps();
            });
            \DB::statement("ALTER TABLE city1 COMMENT '縣市'");
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down() {
        Schema::dropIfExists('city1');
    }
}
