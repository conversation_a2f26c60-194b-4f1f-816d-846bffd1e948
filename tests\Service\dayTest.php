<?php

namespace Tests\Service;

class dayTest extends baseTest {

    public $dayService;

    public function setUp(): void {
        parent::setUp();

        $this->dayService = new \App\Services\dayService();
    }


    public function test_day資料排程() {
        $logouttime = date('Y-m-d H:i:s', strtotime("-480 day"));
        $this->adminuserloginlog = \App\Models\adminuserloginlog::factory()->create([
            'logouttime' => $logouttime,
            'created_at' => $logouttime,

        ]);
        $this->dayService->day();
        $this->assertDatabaseMissing('adminuserloginlog', [
            'logouttime' => $logouttime,
        ]);
    }
}
