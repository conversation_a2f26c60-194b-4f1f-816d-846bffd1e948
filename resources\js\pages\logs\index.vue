<template>
    <div class="container my-4">
        <div class="form-group row m-2">
            <label class="col-md-6 sm-12">Today’s Console Logs</label>
            <div class="col-md-6 sm-12" align="right">
                <button class="btn btn-outline-danger btn-sm" @click="clearLogs">Clear Logs</button>
                &nbsp;&nbsp;
                <button class="btn btn-primary btn-sm" @click="uploadLogs">Upload to Server</button>
            </div>
        </div>

        <div class="card">
            <ul class="list-group list-group-flush">
                <li v-for="(log, index) in logs" :key="index" class="list-group-item d-flex align-items-start">
                    <span :class="getMethodClass(log.method)" class="badge me-3">
                        {{ log.method.toUpperCase() }}
                    </span>
                    <div class="flex-grow-1">
                        {{ log.url }}
                        <p class="mb-1" style="max-height: 100px; overflow: auto">
                            {{ log.message }}
                        </p>
                        <small class="text-muted">{{ log.timestamp }}</small>
                    </div>
                </li>
                <li v-if="logs.length === 0" class="list-group-item text-center text-muted">No logs for today yet.</li>
            </ul>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
const http = createHttp() //http套件
const { $consoleLogs } = useNuxtApp()
const logs = $consoleLogs

const getMethodClass = method => {
    const classes = {
        log: 'bg-primary',
        info: 'bg-success',
        warn: 'bg-warning text-dark',
        error: 'bg-danger',
        debug: 'bg-secondary',
        trace: 'bg-info',
        table: 'bg-dark',
        default: 'bg-light text-dark'
    }
    return `${classes[method] || classes.default} rounded-pill`
}

const uploadLogs = async (): Promise<void> => {
    try {
        let rep = await http.post('api/logs/store', { code: utils.getDateTime(), messages: JSON.stringify(logs.value) })

        if (rep.resultcode == '0') {
            utils.toast('上傳成功', 'success', 2000)
        } else {
            throw new Error(rep.resultmessage)
        }
    } catch (error: any) {
        utils.formElError(error)
        console.error(error)
    }
}

const clearLogs = () => {
    logs.value = []
}
</script>

<style scoped>
.list-group-item:hover {
    background-color: #f8f9fa;
    transition: background-color 0.2s;
}
</style>
