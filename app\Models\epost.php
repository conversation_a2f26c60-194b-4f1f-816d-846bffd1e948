<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use DB;
use PF;
//上稿系統
class epost extends baseModel
{
    

	public $tabletitle = '上稿系統';
    public $table = 'epost';
    public $primaryKey = 'epostid';
    
    //欄位必填
    public $rules = [
		'epostid' => 'required',

    ];
    public $fieldInfo = [
'id'=>['title'=>'自動編號','type'=>'int(10) unsigned'],//
'epostid'=>['title'=>'編號','type'=>'varchar(50)'],//
'eposttitle'=>['title'=>'標題','type'=>'varchar(100)'],//
'epostbody'=>['title'=>'本文','type'=>'text'],//
'alg'=>['title'=>'語系','type'=>'varchar(10)'],//
'adminuser_id'=>['title'=>'編輯人員','type'=>'int(11)'],//
'useraccount'=>['title'=>'編輯人員','type'=>'varchar(50)'],//
'created_at'=>['title'=>'建立時間','type'=>'timestamp'],//
'updated_at'=>['title'=>'編輯時間','type'=>'timestamp'],//
];
    //日期欄位的儲存格式。'Y-m-d' or 'U' or ...
    //protected $dateFormat = 'Y-m-d';
    
    protected $fillable = ['epostid','eposttitle','epostbody','alg','adminuser_id','useraccount','created_at','updated_at']; //可充許傳入的欄位
    protected $guarded =[];   //拒絶修改的欄位(fillable,guarded都設已fillable為準)
    protected $dates = ['created_at','updated_at'];    
  /*
  public function __construct($attr = array(), $exists = false) {
        $this->fillable = parent::getfillables();//接受$request->all();
        parent::__construct($attr, $exists);
        
        parent::setFieldInfo($this->fieldInfo);
  } 
*/        
  public static function boot()
    {
        parent::boot();
        
        static::creating(function ($model) {
            //$model->uuid = (string) Uuid::generate();
        });
        self::created(function ($model) {          
                //$model->uuid = (string) Uuid::generate();
        });
        static::updating(function ($model) {
            // do some logging
        });
        self::updated(function ($model) {          

        });
         static::deleting(function ($model) {
            //  DB::table('epost')->select()
            // ->myWhere('epostid|N', $model->epostid, "epostid", 'Y')
            // ->delete();



          
        });
        static::deleted(function ($model) {
            
        });

    }	


}
