<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateCity2Table extends Migration {
    /**
     * Run the migrations.
     */
    public function up() {
        if (!Schema::hasTable('city2')) {
            Schema::create('city2', function (Blueprint $table) {
                $table->bigIncrements('city2id')->unsigned();
                $table->string('city1title', 50)->index()->comment('縣市');
                $table->string('city2title', 50)->comment('鄉鎮');
                $table->string('postal', 5)->comment('郵遞區號');
                $table->integer('city1id')->comment('縣市編號');
                $table->timestamps();
            });
            \DB::statement("ALTER TABLE city2 COMMENT '鄉鎮'");
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down() {
        Schema::dropIfExists('city2');
    }
}
