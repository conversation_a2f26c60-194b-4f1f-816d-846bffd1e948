<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class baseMiddleware {
    /**
     * 處理傳入的請求。
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     *
     * @return mixed
     */
    public function handle(Request $request, Closure $next) {
        // 如果環境為開發環境，記錄請求資訊
        if (config('app.env') === 'dev') {

            Log::info('API Request:', [
                'header' => [
                    'HTTP_Authorization' => 'Bearer ' . $request->bearerToken(),
                    'CONTENT_TYPE' => 'application/json',
                ],
                'url' => Str::after($request->getRequestUri(), 'public/'),
                'post' =>  $request->all(),
                'raw' => $request->getContent() ? json_decode($request->getContent(), true) : null,
            ]);
        }

        if ($request->input('encryptDataJson') != "") {
            $encryptedBase64 = $request->input('encryptDataJson');
            // 定義與前端相同的密鑰與 IV
            $key = \config('encrypted.key'); // 32字元
            $iv  = $request->input('iv') != "" ? $request->input('iv') : \config('encrypted.iv');                  // 16字元
            // $key = 'Abc!2345XyZ@6789QwErTyUiOpAsDfGh'; // 32 字符
            // $iv  = '0Fp0u1uSG5Bj4LuT';                // 16 字符


            // 先將 Base64 解碼為原始二進位資料
            $encryptedData = base64_decode($encryptedBase64);
            // 使用 openssl_decrypt 進行解密
            // 注意：OPENSSL_RAW_DATA 表示輸入是原始二進位資料
            $decrypted = openssl_decrypt($encryptedData, 'AES-256-CBC', $key, OPENSSL_RAW_DATA, $iv);
            if ($decrypted != "") {
                $json = \PF::json_decode($decrypted, true); //ture=>可以用$json['yyy'];false=>可以直接update
                $request->merge($json);
            }
        }


        // 繼續處理請求
        $response = $next($request);

        // 設置安全頭部
        // 設置 Referrer-Policy 頭部，避免降級時洩露來源資訊
        $response->headers->set('Referrer-Policy', 'no-referrer-when-downgrade');
        // 設置 X-Content-Type-Options 頭部，防止瀏覽器嗅探 MIME 類型
        $response->headers->set('X-Content-Type-Options', 'nosniff');
        // 設置 X-XSS-Protection 頭部，開啟 XSS 攻擊保護
        $response->headers->set('X-XSS-Protection', '1; mode=block');
        // 設置 X-Frame-Options 頭部，防止點擊劫持攻擊
        //$response->headers->set('X-Frame-Options', 'SAMEORIGIN');
        // 如果需要允許從特定來源嵌入，可以使用下面的註釋代碼
        // $response->headers->set('X-Frame-Options', 'ALLOW-FROM https://www.facebook.com/');
        // 設置 Strict-Transport-Security 頭部，強制使用 HTTPS
        $response->headers->set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');


        return $response;
    }

    public function minify($input) {
        $search = [
            '/\>\s+/s',
            '/\s+</s',
        ];

        $replace = [
            '> ',
            ' <',
        ];

        return preg_replace($search, $replace, $input);
    }
}
