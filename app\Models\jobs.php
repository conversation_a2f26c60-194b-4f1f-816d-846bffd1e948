<?php
namespace App\Models;
use DB;
use PF;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/*swagger api document start*/
/**
 * @OA\Schema(
 *   schema="jobs",
 *      allOf={
 *         @OA\Schema( @OA\Property(property="id", type="integer",description="自動編號", example=""  )),
*         @OA\Schema( @OA\Property(property="queue", type="string",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="payload", type="string",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="attempts", type="integer",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="reserved_at", type="integer",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="available_at", type="integer",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="created_at", type="integer",description="建立時間", example=""  )),

 *      }
 *)
 */
/*swagger api document end*/
class jobs extends baseModel
{
    //use SoftDeletes;//軟刪除
    use HasFactory;
    public $timestamps = true;
    public $table = 'jobs';
    public $primaryKey = 'id';
    //public $incrementing = false;//取消自動編號
    protected $casts = [
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];
      //欄位必填
    public $rules = [
		'id' => 'required',

    ];
    //欄位屬於檔案類型
    public $fieldFiles = [
        
       // 'imgs' => 'public/images/jobs',
    ];
    public $fieldInfo = [
'id'=>['title'=>'自動編號','type'=>'bigint(20) unsigned'],//
'queue'=>['title'=>'','type'=>'varchar(190)'],//
'payload'=>['title'=>'','type'=>'longtext'],//
'attempts'=>['title'=>'','type'=>'tinyint(3) unsigned'],//
'reserved_at'=>['title'=>'','type'=>'int(10) unsigned'],//
'available_at'=>['title'=>'','type'=>'int(10) unsigned'],//
'created_at'=>['title'=>'建立時間','type'=>'int(10) unsigned'],//
];
    //public $timestamps = false;//不使用 timestamps 相關字段
    //日期欄位的儲存格式。'Y-m-d' or 'U' or ...
    //protected $dateFormat = 'Y-m-d';
    protected $fillable = ['queue','payload','attempts','reserved_at','available_at','created_at']; //可充許傳入的欄位
    protected $guarded =[];   //拒絶修改的欄位(fillable,guarded都設已fillable為準)
    protected $dates = [];

    // public function orderdetail() //父對子 一對多 ordergroup.php
    // {
    //     return $this->hasMany('App\Models\orderdetail');
    // }
    // public function ordergroup() //子對父 多對一 orderdetail.php
    // {
    //     return $this->belongsTo(ordergroup::class, 'ordergroup_id');
    // }
    // public function product() {//父對子  一對一 orderdetail.php
    //     return $this->hasOne(product::class, 'id', 'product_id');
    // }
    public static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            //$model->uuid = (string) Uuid::generate();
        });
        self::created(function ($model) {
                //$model->uuid = (string) Uuid::generate();
        });
        static::updating(function ($model) {
            // do some logging
        });
        self::updated(function ($model) {
            // $model->getChanges() 可以獲取到本次更新的欄位名稱和新值。
            // if ($model->isDirty('欄位名稱') && $model->欄位名稱 == "1") {// '欄位名稱' 的值已被修改
            //     $model->getOriginal('欄位名稱');   //原來的資料
            //     $model->toArray();
            // }
        });
        static::deleting(function ($model) {
                parent::delFieldFile($model); //刪fieldFiles定義的欄位檔案
                // $rows = \App\Models\jobsitem::selectRaw('id');
                // $rows->where('jobs_id', $model->id);
                // $rows->chunk(200, function ($rows) {
                // foreach ($rows as $rs) {
                //     $rs->delete();
                // }
        });
        static::deleted(function ($model) {

        });
    }

}