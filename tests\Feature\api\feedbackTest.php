<?php

namespace Tests\Feature\api;


/***
"功能名稱":"與我聯絡 單位測試",
"資料表":"feedback",
"建立時間":"2024-10-28 08:24:13 ",
 ***/
//command "php artisan test --filter tests/Feature//feedbackTest.php
class feedbackTest extends baseTest {
  public $feedback;
  public function setUp(): void {
    parent::setUp();

    $this->feedback = \App\Models\feedback::factory()->create([
      //'member_id' => $this->member->id,
    ]); //編輯日期
  }
  public function test_新增store與我聯絡() {
    //\Mail::fake();





    $datas = [

      'header' =>
      array(
        'HTTP_Authorization' => 'Bearer ' . $this->adminuser->api_token,
        'CONTENT_TYPE' => 'application/json',
      ),
      'url' => '/api/feedback/store',
      'raw' =>
      array(
        'name' => $this->myFaker->getName(), //姓名-
        'email' => $this->myFaker->getEmail(), //返回一個隨機郵箱,//電子信箱-
        'tel' => $this->myFaker->getTel(), //電話-
        'mobile' => $this->myFaker->getMobile(), //行動電話-
        'memo' => $this->faker->realText(200), //詢問及意見回饋-
        'retitle' => $this->faker->realText(20), //回覆標題-
        'rebody' => $this->faker->realText(200), //回覆訊息-
        'redate' => $this->myFaker->getDate(), //回覆日期-
        'member_id' => $this->member->id,
        //會員編號-
        'alg' => $this->faker->realText(20), //語系-
        'adminuser_id' => '1', //編輯人員-
        'adminuser_name' => 'admin', //編輯人員-
        'created_at' => now(), //建立日期-

      ),
      'post' => NULL,

    ];
    $response = $this->withHeaders($datas['header'])
      ->json('POST', $datas['url'], $datas['raw']);

    // echo $response->getStatusCode();
    //echo "response" . $response->getContent();

    $this->checkJson($response);
    //檢查資料表資料是否存在
    $this->assertDatabaseHas('feedback', [
      'name'       => $datas['raw']['name'],
      'email'       => $datas['raw']['email'],
      'tel'       => $datas['raw']['tel'],
      'mobile'       => $datas['raw']['mobile'],


    ]);






    //\Mail::assertSent(\App\Mail\feedbackMail::class);
  }
}
