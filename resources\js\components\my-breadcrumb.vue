<template>
    <el-breadcrumb separator="/" style="padding-bottom: 10px">
        <el-breadcrumb-item>位置 </el-breadcrumb-item>
        <el-breadcrumb-item>首頁</el-breadcrumb-item>
        <el-breadcrumb-item v-if="ctitle != ''">{{ ctitle }}</el-breadcrumb-item>

        <el-breadcrumb-item v-if="$slots.default">
            <slot></slot>
        </el-breadcrumb-item>
    </el-breadcrumb>
</template>

<script setup lang="ts">
import { ref, watch, defineEmits, onMounted, getCurrentInstance, onErrorCaptured } from 'vue'
import { useDataStore } from '@/stores'
//const { proxy } = getCurrentInstance();

const props = defineProps({
    id: String,
    title: String || ''
})
const ctitle = ref<string>('') //要加.value

const store = useDataStore()

watch(
    () => props.id,
    async newValue => {
        try {
            ctitle.value = store.adminuser.navs[newValue]
        } catch (error) {}
    },
    { deep: true, immediate: true } //, immediate: true
)
onMounted(async () => {
    if (!utils.isEmpty(props.title)) {
        ctitle.value = props.title
    }
})
</script>

<style scoped></style>
