<?php

namespace App\Http\Controllers\api;

use OpenApi\Annotations as OA;
use Illuminate\Routing\Controller as BaseController;



/**
 * @OA\Info(
 *      title="API",
 *      version="1.0.0",
 *      description="API 文件"
 * )
 */

/**
 * @OA\Servers(
 *     @OA\Server(url="https://golfiotv1.masterplan.tw/",description="正式"),
 *     @OA\Server(url="https://allennb.com.tw:442/golfiotv1/public/",description="本機"),
 * ),
 * @OA\SecurityScheme(securityScheme="bearerAuth",type="http",scheme="bearer")
 */


class Controller extends BaseController {
  public $jsondata;


  public function __construct() {
    $this->jsondata['resultcode'] = 0;
    $this->jsondata['resultmessage'] = '';
    // if ('production' == \config('app.env')) {
    //   header('Access-Control-Allow-Origin: ' . $_SERVER['HTTP_ORIGIN']);
    //   header('Access-Control-Allow-Credentials: true');
    //   header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
    //   header('Access-Control-Allow-Headers: X-Requested-With, Content-Type, Accept');
    //   header('P3P: CP=ALL ADM DEV PSAi COM OUR OTRo STP IND ONL');
    // }
    //PF::printr(4);
    //PF::printr($this->limit);
  }
  public function apiResponse($data, $code = 200) {

    return response()->json($data, $code, ['Content-Type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);
  }
}
