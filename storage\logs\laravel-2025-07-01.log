[2025-07-01 10:35:46] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:129.22]
  
[2025-07-01 10:35:46] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:0.86]
  
[2025-07-01 10:35:46] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.87]
  
[2025-07-01 10:35:46] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.74]
  
[2025-07-01 10:35:46] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:0.85]
  
[2025-07-01 10:35:46] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:1.38]
  
[2025-07-01 10:35:46] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:0.7]
  
[2025-07-01 10:35:46] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:0.75]
  
[2025-07-01 10:35:46] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:0.91]
  
[2025-07-01 10:35:46] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.85]
  
[2025-07-01 10:35:46] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:0.93]
  
[2025-07-01 10:35:46] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:0.7]
  
[2025-07-01 10:35:46] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:0.63]
  
[2025-07-01 10:35:46] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:0.65]
  
[2025-07-01 10:36:07] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:30.69]
  
[2025-07-01 10:36:07] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:0.67]
  
[2025-07-01 10:36:07] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.6]
  
[2025-07-01 10:36:07] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.71]
  
[2025-07-01 10:36:07] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:1.1]
  
[2025-07-01 10:36:07] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:0.76]
  
[2025-07-01 10:36:07] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:0.62]
  
[2025-07-01 10:36:07] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:0.74]
  
[2025-07-01 10:36:07] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:0.67]
  
[2025-07-01 10:36:07] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.9]
  
[2025-07-01 10:36:07] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:0.97]
  
[2025-07-01 10:36:07] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:0.88]
  
[2025-07-01 10:36:07] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:0.89]
  
[2025-07-01 10:36:07] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:0.8]
  
[2025-07-01 10:36:27] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:19.32]
  
[2025-07-01 10:36:28] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:0.9]
  
[2025-07-01 10:36:28] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:1.17]
  
[2025-07-01 10:36:28] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:1.02]
  
[2025-07-01 10:36:28] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:0.95]
  
[2025-07-01 10:36:28] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:0.91]
  
[2025-07-01 10:36:28] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:0.73]
  
[2025-07-01 10:36:28] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:0.91]
  
[2025-07-01 10:36:28] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:0.84]
  
[2025-07-01 10:36:28] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.98]
  
[2025-07-01 10:36:28] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:1]
  
[2025-07-01 10:36:28] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:1.11]
  
[2025-07-01 10:36:28] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:0.97]
  
[2025-07-01 10:36:28] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:1.07]
  
[2025-07-01 10:36:46] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:7.53]
  
[2025-07-01 10:36:46] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:1.31]
  
[2025-07-01 10:36:46] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.87]
  
[2025-07-01 10:36:46] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.96]
  
[2025-07-01 10:36:46] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:0.77]
  
[2025-07-01 10:36:46] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:1.07]
  
[2025-07-01 10:36:46] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:1.13]
  
[2025-07-01 10:36:46] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:0.89]
  
[2025-07-01 10:36:46] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:1.02]
  
[2025-07-01 10:36:46] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.99]
  
[2025-07-01 10:36:46] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:0.98]
  
[2025-07-01 10:36:46] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:1.23]
  
[2025-07-01 10:36:46] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:1.25]
  
[2025-07-01 10:36:46] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:0.87]
  
[2025-07-01 10:37:05] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:24.76]
  
[2025-07-01 10:37:05] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:0.97]
  
[2025-07-01 10:37:05] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.88]
  
[2025-07-01 10:37:05] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:1.02]
  
[2025-07-01 10:37:05] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:1.92]
  
[2025-07-01 10:37:05] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:0.7]
  
[2025-07-01 10:37:05] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:0.95]
  
[2025-07-01 10:37:05] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:1.13]
  
[2025-07-01 10:37:05] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:0.87]
  
[2025-07-01 10:37:05] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.81]
  
[2025-07-01 10:37:05] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:0.98]
  
[2025-07-01 10:37:05] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:1.01]
  
[2025-07-01 10:37:05] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:0.93]
  
[2025-07-01 10:37:05] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:0.67]
  
[2025-07-01 10:37:24] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:19.95]
  
[2025-07-01 10:37:24] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:0.95]
  
[2025-07-01 10:37:24] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.77]
  
[2025-07-01 10:37:24] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.64]
  
[2025-07-01 10:37:24] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:0.75]
  
[2025-07-01 10:37:24] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:1.62]
  
[2025-07-01 10:37:24] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:1.18]
  
[2025-07-01 10:37:24] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:0.76]
  
[2025-07-01 10:37:24] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:0.77]
  
[2025-07-01 10:37:24] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.89]
  
[2025-07-01 10:37:24] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:0.94]
  
[2025-07-01 10:37:24] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:0.66]
  
[2025-07-01 10:37:24] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:2.02]
  
[2025-07-01 10:37:24] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:0.76]
  
[2025-07-01 10:37:42] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:25.62]
  
[2025-07-01 10:37:42] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:0.89]
  
[2025-07-01 10:37:42] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.99]
  
[2025-07-01 10:37:42] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.92]
  
[2025-07-01 10:37:42] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:0.67]
  
[2025-07-01 10:37:42] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:0.97]
  
[2025-07-01 10:37:42] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:0.91]
  
[2025-07-01 10:37:42] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:0.99]
  
[2025-07-01 10:37:42] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:0.76]
  
[2025-07-01 10:37:42] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:1.01]
  
[2025-07-01 10:37:42] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:1.05]
  
[2025-07-01 10:37:42] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:1]
  
[2025-07-01 10:37:42] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:0.82]
  
[2025-07-01 10:37:42] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:1.11]
  
[2025-07-01 10:37:48] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:24.79]
  
[2025-07-01 10:37:48] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:1.21]
  
[2025-07-01 10:37:48] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:1.22]
  
[2025-07-01 10:37:48] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:1.28]
  
[2025-07-01 10:37:48] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:1.5]
  
[2025-07-01 10:37:48] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:1.19]
  
[2025-07-01 10:37:48] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:1.22]
  
[2025-07-01 10:37:48] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:1.21]
  
[2025-07-01 10:37:48] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:1.47]
  
[2025-07-01 10:37:48] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:1.04]
  
[2025-07-01 10:37:48] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:1.36]
  
[2025-07-01 10:37:49] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:1.04]
  
[2025-07-01 10:37:49] dev.INFO: 
select * from `members` limit 1
  [執行超過10秒:1.17]
  
[2025-07-01 10:37:49] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:1.49]
  
[2025-07-01 10:37:49] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:1.46]
  
[2025-07-01 10:38:00] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:21.33]
  
[2025-07-01 10:38:00] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:0.86]
  
[2025-07-01 10:38:00] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.76]
  
[2025-07-01 10:38:00] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.93]
  
[2025-07-01 10:38:00] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:1.37]
  
[2025-07-01 10:38:00] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:1.01]
  
[2025-07-01 10:38:00] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:0.96]
  
[2025-07-01 10:38:00] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:1.58]
  
[2025-07-01 10:38:00] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:1.5]
  
[2025-07-01 10:38:00] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:0.88]
  
[2025-07-01 10:38:00] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:1.25]
  
[2025-07-01 10:38:00] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:0.93]
  
[2025-07-01 10:38:00] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.71]
  
[2025-07-01 10:38:00] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:1.22]
  
[2025-07-01 10:38:00] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:1.28]
  
[2025-07-01 10:38:00] dev.INFO: 
select * from `members` limit 1
  [執行超過10秒:1.02]
  
[2025-07-01 10:38:00] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:1.06]
  
[2025-07-01 10:38:00] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:1.17]
  
[2025-07-01 10:38:00] dev.INFO: 
select * from `reviews` limit 1
  [執行超過10秒:1.11]
  
[2025-07-01 10:38:25] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:26.83]
  
[2025-07-01 10:38:25] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:1.11]
  
[2025-07-01 10:38:25] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:1.14]
  
[2025-07-01 10:38:25] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:1.05]
  
[2025-07-01 10:38:25] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:1.17]
  
[2025-07-01 10:38:25] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:1.36]
  
[2025-07-01 10:38:25] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:0.93]
  
[2025-07-01 10:38:25] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:0.67]
  
[2025-07-01 10:38:25] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:1.02]
  
[2025-07-01 10:38:25] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:1.09]
  
[2025-07-01 10:38:25] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:1.31]
  
[2025-07-01 10:38:25] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:1.17]
  
[2025-07-01 10:38:25] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.73]
  
[2025-07-01 10:38:25] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:1.1]
  
[2025-07-01 10:38:25] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:0.79]
  
[2025-07-01 10:38:25] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:1.33]
  
[2025-07-01 10:38:25] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:1.69]
  
[2025-07-01 10:38:25] dev.INFO: 
select * from `reviews` limit 1
  [執行超過10秒:1.19]
  
[2025-07-01 10:42:07] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:20.95]
  
[2025-07-01 10:42:07] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:0.68]
  
[2025-07-01 10:42:07] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.79]
  
[2025-07-01 10:42:07] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.81]
  
[2025-07-01 10:42:07] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:1.05]
  
[2025-07-01 10:42:07] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:0.91]
  
[2025-07-01 10:42:07] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:0.73]
  
[2025-07-01 10:42:07] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:0.66]
  
[2025-07-01 10:42:07] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:0.93]
  
[2025-07-01 10:42:07] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:0.81]
  
[2025-07-01 10:42:07] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:0.71]
  
[2025-07-01 10:42:07] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:0.93]
  
[2025-07-01 10:42:07] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.72]
  
[2025-07-01 10:42:07] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:0.88]
  
[2025-07-01 10:42:07] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:0.86]
  
[2025-07-01 10:42:07] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:0.98]
  
[2025-07-01 10:42:07] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:0.87]
  
[2025-07-01 10:42:08] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:21.91]
  
[2025-07-01 10:42:08] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:1.67]
  
[2025-07-01 10:42:08] dev.INFO: 
select `migration` from `migrations`
 order by `batch` asc, `migration` asc
  [執行超過10秒:0.77]
  
[2025-07-01 10:42:08] dev.INFO: 
select `migration` from `migrations`
 order by `batch` asc, `migration` asc
  [執行超過10秒:0.7]
  
[2025-07-01 10:42:08] dev.INFO: 
select max(`batch`) as aggregate from `migrations`
  [執行超過10秒:1.35]
  
[2025-07-01 10:42:08] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:2.06]
  
[2025-07-01 10:42:26] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:22.85]
  
[2025-07-01 10:42:26] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:0.64]
  
[2025-07-01 10:42:26] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.96]
  
[2025-07-01 10:42:26] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.86]
  
[2025-07-01 10:42:26] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:0.98]
  
[2025-07-01 10:42:26] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:0.96]
  
[2025-07-01 10:42:26] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:0.87]
  
[2025-07-01 10:42:26] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:0.73]
  
[2025-07-01 10:42:26] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:1.22]
  
[2025-07-01 10:42:26] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:1.04]
  
[2025-07-01 10:42:26] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:0.91]
  
[2025-07-01 10:42:26] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:1.35]
  
[2025-07-01 10:42:26] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.8]
  
[2025-07-01 10:42:26] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:0.65]
  
[2025-07-01 10:42:26] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:0.73]
  
[2025-07-01 10:42:26] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:0.95]
  
[2025-07-01 10:42:26] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:0.62]
  
[2025-07-01 10:42:36] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:19.31]
  
[2025-07-01 10:42:36] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:0.86]
  
[2025-07-01 10:42:36] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.96]
  
[2025-07-01 10:42:36] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.44]
  
[2025-07-01 10:42:36] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:0.58]
  
[2025-07-01 10:42:36] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:1.02]
  
[2025-07-01 10:42:36] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:0.84]
  
[2025-07-01 10:42:36] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:0.41]
  
[2025-07-01 10:42:36] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:0.8]
  
[2025-07-01 10:42:36] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:0.96]
  
[2025-07-01 10:42:36] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:1.5]
  
[2025-07-01 10:42:36] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:0.66]
  
[2025-07-01 10:42:36] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.66]
  
[2025-07-01 10:42:36] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:1.13]
  
[2025-07-01 10:42:36] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:0.89]
  
[2025-07-01 10:42:36] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:0.67]
  
[2025-07-01 10:42:36] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:0.41]
  
[2025-07-01 10:42:44] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:24.64]
  
[2025-07-01 10:42:44] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:0.75]
  
[2025-07-01 10:42:44] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.93]
  
[2025-07-01 10:42:44] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.76]
  
[2025-07-01 10:42:44] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:1.1]
  
[2025-07-01 10:42:44] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:0.83]
  
[2025-07-01 10:42:44] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:0.99]
  
[2025-07-01 10:42:44] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:0.72]
  
[2025-07-01 10:42:44] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:0.81]
  
[2025-07-01 10:42:44] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:1.05]
  
[2025-07-01 10:42:44] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:0.64]
  
[2025-07-01 10:42:44] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:0.79]
  
[2025-07-01 10:42:44] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.73]
  
[2025-07-01 10:42:44] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:0.81]
  
[2025-07-01 10:42:44] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:0.58]
  
[2025-07-01 10:42:44] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:0.66]
  
[2025-07-01 10:42:44] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:0.75]
  
[2025-07-01 10:42:49] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:24.01]
  
[2025-07-01 10:42:49] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:0.7]
  
[2025-07-01 10:42:49] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.97]
  
[2025-07-01 10:42:49] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.87]
  
[2025-07-01 10:42:49] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:1.1]
  
[2025-07-01 10:42:49] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:0.62]
  
[2025-07-01 10:42:49] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:1]
  
[2025-07-01 10:42:50] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:0.73]
  
[2025-07-01 10:42:50] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:0.89]
  
[2025-07-01 10:42:50] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:0.69]
  
[2025-07-01 10:42:50] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:1.14]
  
[2025-07-01 10:42:50] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:0.58]
  
[2025-07-01 10:42:50] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.7]
  
[2025-07-01 10:42:50] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:0.8]
  
[2025-07-01 10:42:50] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:0.72]
  
[2025-07-01 10:42:50] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:0.52]
  
[2025-07-01 10:42:50] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:0.83]
  
[2025-07-01 10:42:50] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:21.06]
  
[2025-07-01 10:42:50] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:2.38]
  
[2025-07-01 10:42:50] dev.INFO: 
select `migration` from `migrations`
 order by `batch` asc, `migration` asc
  [執行超過10秒:0.61]
  
[2025-07-01 10:42:50] dev.INFO: 
select `migration` from `migrations`
 order by `batch` asc, `migration` asc
  [執行超過10秒:0.87]
  
[2025-07-01 10:42:50] dev.INFO: 
select max(`batch`) as aggregate from `migrations`
  [執行超過10秒:0.45]
  
[2025-07-01 10:42:50] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:1.37]
  
[2025-07-01 10:42:50] dev.INFO: 
create table `member` (`id` int unsigned not null auto_increment primary key comment '會員編號', `email` varchar(100) not null comment '電子信箱', `password` varchar(190) not null comment '密碼', `name` varchar(50) not null comment '姓名', `phone` varchar(20) null comment '手機號碼', `birth` date null comment '生日', `gender` varchar(10) null comment '性別', `member_type` tinyint not null default '1' comment '會員類型', `status` tinyint not null default '1' comment '會員狀態', `bio` text null comment '個人簡介', `avatar` varchar(190) null comment '頭像', `wallet_balance` decimal(10, 2) not null default '0' comment '錢包餘額', `email_verified_at` datetime null comment '信箱驗證時間', `remember_token` varchar(190) null comment '記住我令牌', `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci'
  [執行超過10秒:40.74]
  
[2025-07-01 10:42:50] dev.INFO: 
alter table `member` add index `member_email_index`(`email`)
  [執行超過10秒:24.23]
  
[2025-07-01 10:42:50] dev.INFO: 
alter table `member` add index `member_member_type_index`(`member_type`)
  [執行超過10秒:22.67]
  
[2025-07-01 10:42:50] dev.INFO: 
alter table `member` add index `member_status_index`(`status`)
  [執行超過10秒:19.89]
  
[2025-07-01 10:42:50] dev.INFO: 
alter table `member` add unique `member_email_unique`(`email`)
  [執行超過10秒:22.23]
  
[2025-07-01 10:42:50] dev.INFO: 
alter table `member` auto_increment = 10000
  [執行超過10秒:10.79]
  
[2025-07-01 10:42:50] dev.INFO: 
ALTER TABLE member COMMENT 'XX'
  [執行超過10秒:11.2]
  
[2025-07-01 10:42:50] dev.INFO: 
insert into `migrations` (`migration`, `batch`) values ('2025_07_01_103539_create_member_table', '4')
  [執行超過10秒:3.2]
  
[2025-07-01 10:42:50] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:2.11]
  
[2025-07-01 10:42:50] dev.INFO: 
create table `coach` (`id` int unsigned not null auto_increment primary key comment '教練編號', `member_id` int unsigned not null comment '會員編號', `license_number` varchar(50) null comment '證照號碼', `certification_type` tinyint not null comment '認證類型', `coach_status` tinyint not null default '0' comment '教練狀態', `experience` text null comment '教學經驗', `speciality` text null comment '專長項目', `hourly_rate` decimal(8, 2) not null default '0' comment '時薪', `years_experience` tinyint not null default '0' comment '教學年資', `certification_documents` text null comment '認證文件', `certification_date` date null comment '認證日期', `certification_expiry` date null comment '認證到期日', `rating` decimal(3, 2) not null default '0' comment '評分', `total_reviews` int unsigned not null default '0' comment '評價總數', `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci'
  [執行超過10秒:41.23]
  
[2025-07-01 10:42:50] dev.INFO: 
alter table `coach` add index `coach_member_id_index`(`member_id`)
  [執行超過10秒:21.51]
  
[2025-07-01 10:42:50] dev.INFO: 
alter table `coach` add index `coach_certification_type_index`(`certification_type`)
  [執行超過10秒:18.94]
  
[2025-07-01 10:42:50] dev.INFO: 
alter table `coach` add index `coach_coach_status_index`(`coach_status`)
  [執行超過10秒:20.88]
  
[2025-07-01 10:42:50] dev.INFO: 
alter table `coach` add index `coach_rating_index`(`rating`)
  [執行超過10秒:20.87]
  
[2025-07-01 10:42:50] dev.INFO: 
alter table `coach` auto_increment = 10000
  [執行超過10秒:8.82]
  
[2025-07-01 10:42:50] dev.INFO: 
ALTER TABLE coach COMMENT 'XX'
  [執行超過10秒:8.89]
  
[2025-07-01 10:42:50] dev.INFO: 
insert into `migrations` (`migration`, `batch`) values ('2025_07_01_103547_create_coach_table', '4')
  [執行超過10秒:3.09]
  
[2025-07-01 10:42:50] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:1.72]
  
[2025-07-01 10:42:50] dev.INFO: 
create table `booking` (`id` int unsigned not null auto_increment primary key comment '預約編號', `member_id` int unsigned not null comment '會員編號', `course_id` int unsigned not null comment '課程編號', `coach_id` int unsigned not null comment '教練編號', `booking_number` varchar(20) not null comment '預約單號', `booking_date` datetime not null comment '預約日期時間', `start_time` datetime not null comment '開始時間', `end_time` datetime not null comment '結束時間', `booking_status` tinyint not null default '1' comment '預約狀態', `total_amount` decimal(8, 2) not null comment '總金額', `paid_amount` decimal(8, 2) not null default '0' comment '已付金額', `payment_status` tinyint not null default '0' comment '付款狀態', `payment_method` varchar(20) null comment '付款方式', `payment_transaction_id` varchar(50) null comment '付款交易號', `special_requests` text null comment '特殊需求', `cancellation_reason` text null comment '取消原因', `cancelled_at` datetime null comment '取消時間', `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci'
  [執行超過10秒:40.68]
  
[2025-07-01 10:42:50] dev.INFO: 
alter table `booking` add index `booking_member_id_index`(`member_id`)
  [執行超過10秒:23.03]
  
[2025-07-01 10:42:50] dev.INFO: 
alter table `booking` add index `booking_course_id_index`(`course_id`)
  [執行超過10秒:23.54]
  
[2025-07-01 10:42:50] dev.INFO: 
alter table `booking` add index `booking_coach_id_index`(`coach_id`)
  [執行超過10秒:23.91]
  
[2025-07-01 10:42:50] dev.INFO: 
alter table `booking` add index `booking_booking_status_index`(`booking_status`)
  [執行超過10秒:23.67]
  
[2025-07-01 10:42:50] dev.INFO: 
alter table `booking` add index `booking_booking_date_index`(`booking_date`)
  [執行超過10秒:20.74]
  
[2025-07-01 10:42:50] dev.INFO: 
alter table `booking` add index `booking_booking_number_index`(`booking_number`)
  [執行超過10秒:21.59]
  
[2025-07-01 10:42:50] dev.INFO: 
alter table `booking` add unique `booking_booking_number_unique`(`booking_number`)
  [執行超過10秒:20.45]
  
[2025-07-01 10:42:50] dev.INFO: 
alter table `booking` auto_increment = 10000
  [執行超過10秒:10.35]
  
[2025-07-01 10:42:50] dev.INFO: 
ALTER TABLE booking COMMENT 'XX'
  [執行超過10秒:9.32]
  
[2025-07-01 10:42:50] dev.INFO: 
insert into `migrations` (`migration`, `batch`) values ('2025_07_01_103552_create_booking_table', '4')
  [執行超過10秒:3.12]
  
[2025-07-01 10:42:50] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:1.51]
  
[2025-07-01 10:42:50] dev.INFO: 
create table `review` (`id` int unsigned not null auto_increment primary key comment '評價編號', `member_id` int unsigned not null comment '會員編號', `booking_id` int unsigned not null comment '預約編號', `course_id` int unsigned not null comment '課程編號', `coach_id` int unsigned not null comment '教練編號', `rating` tinyint not null comment '評分', `comment` text null comment '評價內容', `review_images` varchar(190) null comment '評價圖片', `is_anonymous` tinyint not null default '0' comment '是否匿名', `status` tinyint not null default '1' comment '狀態', `reviewed_at` datetime not null comment '評價時間', `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci'
  [執行超過10秒:54.38]
  
[2025-07-01 10:42:50] dev.INFO: 
alter table `review` add index `review_member_id_index`(`member_id`)
  [執行超過10秒:20.94]
  
[2025-07-01 10:42:50] dev.INFO: 
alter table `review` add index `review_booking_id_index`(`booking_id`)
  [執行超過10秒:20.45]
  
[2025-07-01 10:42:50] dev.INFO: 
alter table `review` add index `review_course_id_index`(`course_id`)
  [執行超過10秒:19.63]
  
[2025-07-01 10:42:51] dev.INFO: 
alter table `review` add index `review_coach_id_index`(`coach_id`)
  [執行超過10秒:21.9]
  
[2025-07-01 10:42:51] dev.INFO: 
alter table `review` add index `review_rating_index`(`rating`)
  [執行超過10秒:23.14]
  
[2025-07-01 10:42:51] dev.INFO: 
alter table `review` add index `review_status_index`(`status`)
  [執行超過10秒:22.06]
  
[2025-07-01 10:42:51] dev.INFO: 
alter table `review` add index `review_reviewed_at_index`(`reviewed_at`)
  [執行超過10秒:22.52]
  
[2025-07-01 10:42:51] dev.INFO: 
alter table `review` add unique `review_booking_id_unique`(`booking_id`)
  [執行超過10秒:25.56]
  
[2025-07-01 10:42:51] dev.INFO: 
alter table `review` auto_increment = 10000
  [執行超過10秒:10.26]
  
[2025-07-01 10:42:51] dev.INFO: 
ALTER TABLE review COMMENT 'XX'
  [執行超過10秒:10.98]
  
[2025-07-01 10:42:51] dev.INFO: 
insert into `migrations` (`migration`, `batch`) values ('2025_07_01_103556_create_review_table', '4')
  [執行超過10秒:3.4]
  
[2025-07-01 10:42:51] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:2.15]
  
[2025-07-01 10:42:51] dev.INFO: 
create table `course` (`id` int unsigned not null auto_increment primary key comment '課程編號', `coach_id` int unsigned not null comment '教練編號', `title` varchar(100) not null comment '課程標題', `description` text null comment '課程描述', `difficulty_level` tinyint not null comment '課程難度', `course_status` tinyint not null default '0' comment '課程狀態', `price` decimal(8, 2) not null comment '課程價格', `duration_hours` tinyint not null comment '課程時數', `max_students` tinyint not null default '1' comment '最大學員數', `location` varchar(100) null comment '上課地點', `equipment_required` text null comment '需要設備', `course_outline` text null comment '課程大綱', `images` varchar(190) null comment '課程圖片', `rating` decimal(3, 2) not null default '0' comment '課程評分', `total_bookings` int unsigned not null default '0' comment '預約總數', `created_at` timestamp not null default CURRENT_TIMESTAMP comment '建立時間', `updated_at` timestamp null on update CURRENT_TIMESTAMP comment '更新時間') default character set utf8mb4 collate 'utf8mb4_unicode_ci'
  [執行超過10秒:35.03]
  
[2025-07-01 10:42:51] dev.INFO: 
alter table `course` add index `course_coach_id_index`(`coach_id`)
  [執行超過10秒:24.41]
  
[2025-07-01 10:42:51] dev.INFO: 
alter table `course` add index `course_difficulty_level_index`(`difficulty_level`)
  [執行超過10秒:21.5]
  
[2025-07-01 10:42:51] dev.INFO: 
alter table `course` add index `course_course_status_index`(`course_status`)
  [執行超過10秒:21.08]
  
[2025-07-01 10:42:51] dev.INFO: 
alter table `course` add index `course_price_index`(`price`)
  [執行超過10秒:21.23]
  
[2025-07-01 10:42:51] dev.INFO: 
alter table `course` add index `course_rating_index`(`rating`)
  [執行超過10秒:21.62]
  
[2025-07-01 10:42:51] dev.INFO: 
alter table `course` auto_increment = 10000
  [執行超過10秒:10.58]
  
[2025-07-01 10:42:51] dev.INFO: 
ALTER TABLE course COMMENT 'XX'
  [執行超過10秒:10.47]
  
[2025-07-01 10:42:51] dev.INFO: 
insert into `migrations` (`migration`, `batch`) values ('2025_07_01_103601_create_course_table', '4')
  [執行超過10秒:2.83]
  
[2025-07-01 10:49:40] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:29.92]
  
[2025-07-01 10:49:40] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:1.35]
  
[2025-07-01 10:49:40] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:1.41]
  
[2025-07-01 10:49:40] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:1.62]
  
[2025-07-01 10:49:40] dev.INFO: 
select * from `booking` limit 1
  [執行超過10秒:2.55]
  
[2025-07-01 10:49:40] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:1.12]
  
[2025-07-01 10:49:40] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:1.47]
  
[2025-07-01 10:49:40] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:1.24]
  
[2025-07-01 10:49:40] dev.INFO: 
select * from `coach` limit 1
  [執行超過10秒:2.5]
  
[2025-07-01 10:49:40] dev.INFO: 
select * from `course` limit 1
  [執行超過10秒:3.18]
  
[2025-07-01 10:49:40] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:0.91]
  
[2025-07-01 10:49:40] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:1.32]
  
[2025-07-01 10:49:40] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:1.03]
  
[2025-07-01 10:49:40] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:1.33]
  
[2025-07-01 10:49:40] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:1.3]
  
[2025-07-01 10:49:40] dev.INFO: 
select * from `member` limit 1
  [執行超過10秒:3.65]
  
[2025-07-01 10:49:40] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:1.38]
  
[2025-07-01 10:49:40] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:0.94]
  
[2025-07-01 10:49:40] dev.INFO: 
select * from `review` limit 1
  [執行超過10秒:3.05]
  
[2025-07-01 10:53:42] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:26.32]
  
[2025-07-01 10:53:42] dev.INFO: 
select `migration` from `migrations`
 order by `batch` asc, `migration` asc
  [執行超過10秒:1.3]
  
[2025-07-01 10:53:42] dev.INFO: 
select `batch`, `migration` from `migrations`
 order by `batch` asc, `migration` asc
  [執行超過10秒:2.05]
  
[2025-07-01 10:53:53] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:26.8]
  
[2025-07-01 10:53:53] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:2.06]
  
[2025-07-01 10:53:53] dev.INFO: 
select `migration` from `migrations`
 order by `batch` asc, `migration` asc
  [執行超過10秒:1.2]
  
[2025-07-01 10:53:53] dev.INFO: 
select `migration` from `migrations`
 order by `batch` asc, `migration` asc
  [執行超過10秒:1.11]
  
[2025-07-01 10:53:58] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:29.33]
  
[2025-07-01 10:53:58] dev.INFO: 
select `migration` from `migrations`
 order by `batch` asc, `migration` asc
  [執行超過10秒:1.29]
  
[2025-07-01 10:53:58] dev.INFO: 
select `batch`, `migration` from `migrations`
 order by `batch` asc, `migration` asc
  [執行超過10秒:0.94]
  
[2025-07-01 10:55:11] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:59.72]
  
[2025-07-01 10:55:11] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:2.25]
  
[2025-07-01 10:55:11] dev.INFO: 
select `migration` from `migrations`
 order by `batch` asc, `migration` asc
  [執行超過10秒:1.14]
  
[2025-07-01 10:55:11] dev.INFO: 
select `migration` from `migrations`
 order by `batch` asc, `migration` asc
  [執行超過10秒:1.11]
  
[2025-07-01 10:55:32] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:42.56]
  
[2025-07-01 10:55:32] dev.INFO: 
SET FOREIGN_KEY_CHECKS=0;
  [執行超過10秒:1.14]
  
[2025-07-01 10:55:32] dev.INFO: 
drop table `adminuser`,`adminuserloginlog`,`board`,`booking`,`bookings`,`city1`,`city2`,`coach`,`coaches`,`coach_certifications`,`course`,`courses`,`epost`,`failed_jobs`,`formquerylog`,`jobs`,`kind`,`member`,`members`,`migrations`,`personal_access_tokens`,`referrals`,`review`,`reviews`,`ski_trips`,`snow_coin_transactions`
  [執行超過10秒:480.35]
  
[2025-07-01 10:55:32] dev.INFO: 
SET FOREIGN_KEY_CHECKS=1;
  [執行超過10秒:0.49]
  
[2025-07-01 10:55:32] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:2.4]
  
[2025-07-01 10:55:32] dev.INFO: 
create table `migrations` (`id` int unsigned not null auto_increment primary key, `migration` varchar(190) not null, `batch` int not null) default character set utf8mb4 collate 'utf8mb4_unicode_ci'
  [執行超過10秒:35.68]
  
[2025-07-01 10:55:32] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:3.57]
  
[2025-07-01 10:55:32] dev.INFO: 
select `migration` from `migrations`
 order by `batch` asc, `migration` asc
  [執行超過10秒:0.82]
  
[2025-07-01 10:55:32] dev.INFO: 
select `migration` from `migrations`
 order by `batch` asc, `migration` asc
  [執行超過10秒:1.68]
  
[2025-07-01 10:55:32] dev.INFO: 
select max(`batch`) as aggregate from `migrations`
  [執行超過10秒:0.93]
  
[2025-07-01 10:55:32] dev.INFO: 
create table `personal_access_tokens` (`id` bigint unsigned not null auto_increment primary key, `tokenable_type` varchar(190) not null, `tokenable_id` bigint unsigned not null, `name` varchar(190) not null, `token` varchar(64) not null, `abilities` text null, `last_used_at` timestamp null, `expires_at` timestamp null, `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci'
  [執行超過10秒:38.87]
  
[2025-07-01 10:55:32] dev.INFO: 
alter table `personal_access_tokens` add index `personal_access_tokens_tokenable_type_tokenable_id_index`(`tokenable_type`, `tokenable_id`)
  [執行超過10秒:22.66]
  
[2025-07-01 10:55:32] dev.INFO: 
alter table `personal_access_tokens` add unique `personal_access_tokens_token_unique`(`token`)
  [執行超過10秒:19.37]
  
[2025-07-01 10:55:32] dev.INFO: 
insert into `migrations` (`migration`, `batch`) values ('2019_12_14_000001_create_personal_access_tokens_table', '1')
  [執行超過10秒:3.66]
  
[2025-07-01 10:55:32] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:1.5]
  
[2025-07-01 10:55:32] dev.INFO: 
create table `adminuser` (`id` int unsigned not null auto_increment primary key, `account` varchar(50) not null comment '帳號', `name` varchar(50) not null comment '姓名', `password` varchar(100) not null comment '密碼', `email` varchar(255) null comment '電子信箱', `role` int null comment '角色', `limits` varchar(190) null comment '權限', `lastlogin_dt` datetime null comment '最後登入時間', `lastlogin_ip` varchar(45) null comment '最後登入IP', `online` tinyint null default '1' comment '是否核可', `failcount` int not null default '0' comment '登入錯誤次數', `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci'
  [執行超過10秒:33.35]
  
[2025-07-01 10:55:32] dev.INFO: 
alter table `adminuser` add unique `adminuser_account_unique`(`account`)
  [執行超過10秒:22.64]
  
[2025-07-01 10:55:32] dev.INFO: 
alter table `adminuser` add unique `adminuser_name_unique`(`name`)
  [執行超過10秒:19.49]
  
[2025-07-01 10:55:32] dev.INFO: 
alter table `adminuser` auto_increment = 10000
  [執行超過10秒:10.95]
  
[2025-07-01 10:55:32] dev.INFO: 
ALTER TABLE adminuser COMMENT '管理人員'
  [執行超過10秒:10.42]
  
[2025-07-01 10:55:32] dev.INFO: 
insert into `migrations` (`migration`, `batch`) values ('2020_03_13_225621_create_adminuser_table', '1')
  [執行超過10秒:3.32]
  
[2025-07-01 10:55:32] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:1.11]
  
[2025-07-01 10:55:32] dev.INFO: 
create table `adminuserloginlog` (`id` int unsigned not null auto_increment primary key, `account` varchar(50) not null comment '帳號', `clientip` varchar(255) not null comment 'IP', `loginstatus` varchar(50) null comment '登入狀態', `logouttime` datetime null comment '時間', `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci'
  [執行超過10秒:40.38]
  
[2025-07-01 10:55:32] dev.INFO: 
ALTER TABLE adminuserloginlog COMMENT '管理人員登入記錄'
  [執行超過10秒:11.73]
  
[2025-07-01 10:55:32] dev.INFO: 
insert into `migrations` (`migration`, `batch`) values ('2020_03_13_232127_create_adminuserloginlog_table', '1')
  [執行超過10秒:3.3]
  
[2025-07-01 10:55:32] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:1.23]
  
[2025-07-01 10:55:33] dev.INFO: 
create table `board` (`id` int unsigned not null auto_increment primary key, `kind` varchar(190) not null comment '種類', `kind_id` int null comment '種類編號', `title` varchar(500) not null comment '標題', `memo` varchar(500) null comment '描述', `body` mediumtext null comment '本文', `field1` varchar(190) null comment '其他欄位1', `field2` varchar(190) null comment '其他欄位2', `field3` varchar(190) null comment '其他欄位3', `field4` varchar(190) null comment '其他欄位4', `field5` varchar(190) null comment '其他欄位5', `field6` varchar(190) null comment '其他欄位6', `field7` varchar(190) null comment '其他欄位7', `field8` varchar(190) null comment '其他欄位8', `field9` varchar(190) null comment '其他欄位9', `field10` varchar(190) null comment '其他欄位10', `begindate` datetime null comment '開始時間', `closedate` datetime null comment '結束時間', `hits` int not null default '0' comment '點率次數', `boardsort` double(7, 3) null comment '排序號碼', `location` varchar(50) null comment '位置', `adminuser_id` int null comment '編輯人員', `adminuser_name` varchar(50) null comment '編輯人員', `alg` varchar(5) null comment '語系', `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci'
  [執行超過10秒:40.52]
  
[2025-07-01 10:55:33] dev.INFO: 
alter table `board` add index `board_kind_kind_id_index`(`kind`, `kind_id`)
  [執行超過10秒:22.95]
  
[2025-07-01 10:55:33] dev.INFO: 
alter table `board` add index `board_kind_index`(`kind`)
  [執行超過10秒:20.53]
  
[2025-07-01 10:55:33] dev.INFO: 
alter table `board` auto_increment = 10000
  [執行超過10秒:10.04]
  
[2025-07-01 10:55:33] dev.INFO: 
ALTER TABLE board COMMENT '訊息公告'
  [執行超過10秒:10.78]
  
[2025-07-01 10:55:33] dev.INFO: 
insert into `migrations` (`migration`, `batch`) values ('2020_03_17_190124_create_board_table', '1')
  [執行超過10秒:3.2]
  
[2025-07-01 10:55:33] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:1.73]
  
[2025-07-01 10:55:33] dev.INFO: 
create table `city1` (`city1title` varchar(50) not null comment '縣市', `sortnum` double(5, 3) not null comment '排序號碼', `city1id` bigint unsigned not null auto_increment primary key comment '自動編號', `online` int not null comment '上下架', `entitle` varchar(100) not null comment '英文', `partid` int not null comment '地區編號', `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci'
  [執行超過10秒:42.6]
  
[2025-07-01 10:55:33] dev.INFO: 
alter table `city1` add unique `city1_city1title_unique`(`city1title`)
  [執行超過10秒:24.6]
  
[2025-07-01 10:55:33] dev.INFO: 
ALTER TABLE city1 COMMENT '縣市'
  [執行超過10秒:11.73]
  
[2025-07-01 10:55:33] dev.INFO: 
insert into `migrations` (`migration`, `batch`) values ('2020_03_19_171235_create_city1_table', '1')
  [執行超過10秒:3.74]
  
[2025-07-01 10:55:33] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:1.27]
  
[2025-07-01 10:55:33] dev.INFO: 
create table `city2` (`city2id` bigint unsigned not null auto_increment primary key, `city1title` varchar(50) not null comment '縣市', `city2title` varchar(50) not null comment '鄉鎮', `postal` varchar(5) not null comment '郵遞區號', `city1id` int not null comment '縣市編號', `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci'
  [執行超過10秒:51.58]
  
[2025-07-01 10:55:33] dev.INFO: 
alter table `city2` add index `city2_city1title_index`(`city1title`)
  [執行超過10秒:33.22]
  
[2025-07-01 10:55:33] dev.INFO: 
ALTER TABLE city2 COMMENT '鄉鎮'
  [執行超過10秒:17.95]
  
[2025-07-01 10:55:33] dev.INFO: 
insert into `migrations` (`migration`, `batch`) values ('2020_03_19_171246_create_city2_table', '1')
  [執行超過10秒:4.47]
  
[2025-07-01 10:55:33] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:3.09]
  
[2025-07-01 10:55:33] dev.INFO: 
create table `kind` (`id` int unsigned not null auto_increment primary key, `kind` varchar(20) not null comment '種類', `kindtitle` varchar(190) not null comment '標題', `kindfield1` varchar(190) null comment '其他文字欄位1', `kindfield2` varchar(190) null comment '其他文字欄位2', `kindfield3` varchar(190) null comment '其他文字欄位3', `kindbody` mediumtext null comment '本文', `kindint1` int not null comment '其他數字欄位1', `kindint2` int not null comment '其他數字欄位2', `kindint3` int not null comment '其他數字欄位3', `alg` varchar(5) not null comment '語系', `kindsortnum` double(7, 3) null comment '排序號碼', `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci'
  [執行超過10秒:63.05]
  
[2025-07-01 10:55:33] dev.INFO: 
alter table `kind` add unique `kind_kind_kindtitle_unique`(`kind`, `kindtitle`)
  [執行超過10秒:37.89]
  
[2025-07-01 10:55:33] dev.INFO: 
alter table `kind` add index `kind_kind_index`(`kind`)
  [執行超過10秒:35.53]
  
[2025-07-01 10:55:33] dev.INFO: 
alter table `kind` auto_increment = 10000
  [執行超過10秒:14.51]
  
[2025-07-01 10:55:33] dev.INFO: 
ALTER TABLE kind COMMENT '種類'
  [執行超過10秒:14.28]
  
[2025-07-01 10:55:33] dev.INFO: 
insert into `migrations` (`migration`, `batch`) values ('2020_03_21_200217_create_kind_table', '1')
  [執行超過10秒:4.18]
  
[2025-07-01 10:55:33] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:3.04]
  
[2025-07-01 10:55:33] dev.INFO: 
create table `formquerylog` (`id` int unsigned not null auto_increment primary key, `pagename` varchar(190) null comment '功能名稱', `pathinfo` varchar(1000) null comment '程式位置', `formbody` mediumtext null comment 'FORM欄位值', `querybody` mediumtext null comment 'get內容', `raw` mediumtext null comment 'raw內容', `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci'
  [執行超過10秒:67.07]
  
[2025-07-01 10:55:33] dev.INFO: 
ALTER TABLE formquerylog COMMENT '操作記錄'
  [執行超過10秒:19.95]
  
[2025-07-01 10:55:33] dev.INFO: 
insert into `migrations` (`migration`, `batch`) values ('2020_04_02_112654_create_formquerylog_table', '1')
  [執行超過10秒:4.91]
  
[2025-07-01 10:55:33] dev.INFO: 
create table `epost` (`id` int unsigned not null auto_increment primary key, `epostid` varchar(50) not null comment '編號', `eposttitle` varchar(100) null comment '標題', `epostbody` text null comment '本文', `alg` varchar(10) null comment '語系', `adminuser_id` int null comment '編輯人員', `useraccount` varchar(50) null comment '編輯人員', `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci'
  [執行超過10秒:72.81]
  
[2025-07-01 10:55:33] dev.INFO: 
alter table `epost` add index `epost_epostid_index`(`epostid`)
  [執行超過10秒:30.6]
  
[2025-07-01 10:55:33] dev.INFO: 
ALTER TABLE epost COMMENT '上稿系統'
  [執行超過10秒:13.99]
  
[2025-07-01 10:55:33] dev.INFO: 
insert into `migrations` (`migration`, `batch`) values ('2020_04_02_112655_create_epost_table', '1')
  [執行超過10秒:4.27]
  
[2025-07-01 10:55:33] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:2.46]
  
[2025-07-01 10:55:33] dev.INFO: 
create table `jobs` (`id` bigint unsigned not null auto_increment primary key, `queue` varchar(190) not null, `payload` longtext not null, `attempts` tinyint unsigned not null, `reserved_at` int unsigned null, `available_at` int unsigned not null, `created_at` int unsigned not null) default character set utf8mb4 collate 'utf8mb4_unicode_ci'
  [執行超過10秒:51.78]
  
[2025-07-01 10:55:33] dev.INFO: 
alter table `jobs` add index `jobs_queue_index`(`queue`)
  [執行超過10秒:25.43]
  
[2025-07-01 10:55:33] dev.INFO: 
ALTER TABLE jobs COMMENT '排程'
  [執行超過10秒:13.12]
  
[2025-07-01 10:55:33] dev.INFO: 
insert into `migrations` (`migration`, `batch`) values ('2020_04_14_153609_create_jobs_table', '1')
  [執行超過10秒:4.02]
  
[2025-07-01 10:55:33] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:1.81]
  
[2025-07-01 10:55:33] dev.INFO: 
create table `failed_jobs` (`id` bigint unsigned not null auto_increment primary key, `uuid` varchar(190) not null, `connection` text not null, `queue` text not null, `payload` longtext not null, `exception` longtext not null, `failed_at` timestamp not null default CURRENT_TIMESTAMP) default character set utf8mb4 collate 'utf8mb4_unicode_ci'
  [執行超過10秒:50.48]
  
[2025-07-01 10:55:33] dev.INFO: 
alter table `failed_jobs` add unique `failed_jobs_uuid_unique`(`uuid`)
  [執行超過10秒:28.54]
  
[2025-07-01 10:55:33] dev.INFO: 
ALTER TABLE failed_jobs COMMENT '排程失敗記錄'
  [執行超過10秒:14.44]
  
[2025-07-01 10:55:33] dev.INFO: 
insert into `migrations` (`migration`, `batch`) values ('2020_04_14_222947_create_failed_jobs_table', '1')
  [執行超過10秒:5.44]
  
[2025-07-01 10:55:33] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:1.89]
  
[2025-07-01 10:55:34] dev.INFO: 
create table `member` (`id` int unsigned not null auto_increment primary key comment '會員編號', `email` varchar(100) not null comment '電子信箱', `password` varchar(190) not null comment '密碼', `name` varchar(50) not null comment '姓名', `phone` varchar(20) null comment '手機號碼', `birth` date null comment '生日', `gender` varchar(10) null comment '性別', `member_type` tinyint not null default '1' comment '會員類型', `status` tinyint not null default '1' comment '會員狀態', `bio` text null comment '個人簡介', `avatar` varchar(190) null comment '頭像', `wallet_balance` decimal(10, 2) not null default '0' comment '錢包餘額', `email_verified_at` datetime null comment '信箱驗證時間', `remember_token` varchar(190) null comment '記住我令牌', `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci'
  [執行超過10秒:44.83]
  
[2025-07-01 10:55:34] dev.INFO: 
alter table `member` add index `member_email_index`(`email`)
  [執行超過10秒:24.36]
  
[2025-07-01 10:55:34] dev.INFO: 
alter table `member` add index `member_member_type_index`(`member_type`)
  [執行超過10秒:24.06]
  
[2025-07-01 10:55:34] dev.INFO: 
alter table `member` add index `member_status_index`(`status`)
  [執行超過10秒:23.56]
  
[2025-07-01 10:55:34] dev.INFO: 
alter table `member` add unique `member_email_unique`(`email`)
  [執行超過10秒:23.54]
  
[2025-07-01 10:55:34] dev.INFO: 
alter table `member` auto_increment = 10000
  [執行超過10秒:11.41]
  
[2025-07-01 10:55:34] dev.INFO: 
ALTER TABLE member COMMENT 'XX'
  [執行超過10秒:11.47]
  
[2025-07-01 10:55:34] dev.INFO: 
insert into `migrations` (`migration`, `batch`) values ('2025_07_01_103539_create_member_table', '1')
  [執行超過10秒:3.9]
  
[2025-07-01 10:55:34] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:1.89]
  
[2025-07-01 10:55:34] dev.INFO: 
create table `coach` (`id` int unsigned not null auto_increment primary key comment '教練編號', `member_id` int unsigned not null comment '會員編號', `license_number` varchar(50) null comment '證照號碼', `certification_type` tinyint not null comment '認證類型', `coach_status` tinyint not null default '0' comment '教練狀態', `experience` text null comment '教學經驗', `speciality` text null comment '專長項目', `hourly_rate` decimal(8, 2) not null default '0' comment '時薪', `years_experience` tinyint not null default '0' comment '教學年資', `certification_documents` text null comment '認證文件', `certification_date` date null comment '認證日期', `certification_expiry` date null comment '認證到期日', `rating` decimal(3, 2) not null default '0' comment '評分', `total_reviews` int unsigned not null default '0' comment '評價總數', `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci'
  [執行超過10秒:44.09]
  
[2025-07-01 10:55:34] dev.INFO: 
alter table `coach` add index `coach_member_id_index`(`member_id`)
  [執行超過10秒:24.57]
  
[2025-07-01 10:55:34] dev.INFO: 
alter table `coach` add index `coach_certification_type_index`(`certification_type`)
  [執行超過10秒:21.81]
  
[2025-07-01 10:55:34] dev.INFO: 
alter table `coach` add index `coach_coach_status_index`(`coach_status`)
  [執行超過10秒:23.28]
  
[2025-07-01 10:55:34] dev.INFO: 
alter table `coach` add index `coach_rating_index`(`rating`)
  [執行超過10秒:22.69]
  
[2025-07-01 10:55:34] dev.INFO: 
alter table `coach` auto_increment = 10000
  [執行超過10秒:11.61]
  
[2025-07-01 10:55:34] dev.INFO: 
ALTER TABLE coach COMMENT 'XX'
  [執行超過10秒:10.74]
  
[2025-07-01 10:55:34] dev.INFO: 
insert into `migrations` (`migration`, `batch`) values ('2025_07_01_103547_create_coach_table', '1')
  [執行超過10秒:3.8]
  
[2025-07-01 10:55:34] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:1.74]
  
[2025-07-01 10:55:34] dev.INFO: 
create table `booking` (`id` int unsigned not null auto_increment primary key comment '預約編號', `member_id` int unsigned not null comment '會員編號', `course_id` int unsigned not null comment '課程編號', `coach_id` int unsigned not null comment '教練編號', `booking_number` varchar(20) not null comment '預約單號', `booking_date` datetime not null comment '預約日期時間', `start_time` datetime not null comment '開始時間', `end_time` datetime not null comment '結束時間', `booking_status` tinyint not null default '1' comment '預約狀態', `total_amount` decimal(8, 2) not null comment '總金額', `paid_amount` decimal(8, 2) not null default '0' comment '已付金額', `payment_status` tinyint not null default '0' comment '付款狀態', `payment_method` varchar(20) null comment '付款方式', `payment_transaction_id` varchar(50) null comment '付款交易號', `special_requests` text null comment '特殊需求', `cancellation_reason` text null comment '取消原因', `cancelled_at` datetime null comment '取消時間', `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci'
  [執行超過10秒:41.63]
  
[2025-07-01 10:55:34] dev.INFO: 
alter table `booking` add index `booking_member_id_index`(`member_id`)
  [執行超過10秒:24.2]
  
[2025-07-01 10:55:34] dev.INFO: 
alter table `booking` add index `booking_course_id_index`(`course_id`)
  [執行超過10秒:62.92]
  
[2025-07-01 10:55:34] dev.INFO: 
alter table `booking` add index `booking_coach_id_index`(`coach_id`)
  [執行超過10秒:24.42]
  
[2025-07-01 10:55:34] dev.INFO: 
alter table `booking` add index `booking_booking_status_index`(`booking_status`)
  [執行超過10秒:23.51]
  
[2025-07-01 10:55:34] dev.INFO: 
alter table `booking` add index `booking_booking_date_index`(`booking_date`)
  [執行超過10秒:23.72]
  
[2025-07-01 10:55:34] dev.INFO: 
alter table `booking` add index `booking_booking_number_index`(`booking_number`)
  [執行超過10秒:23.12]
  
[2025-07-01 10:55:34] dev.INFO: 
alter table `booking` add unique `booking_booking_number_unique`(`booking_number`)
  [執行超過10秒:23.92]
  
[2025-07-01 10:55:34] dev.INFO: 
alter table `booking` auto_increment = 10000
  [執行超過10秒:11.06]
  
[2025-07-01 10:55:34] dev.INFO: 
ALTER TABLE booking COMMENT 'XX'
  [執行超過10秒:11.89]
  
[2025-07-01 10:55:34] dev.INFO: 
insert into `migrations` (`migration`, `batch`) values ('2025_07_01_103552_create_booking_table', '1')
  [執行超過10秒:4.19]
  
[2025-07-01 10:55:34] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:2]
  
[2025-07-01 10:55:34] dev.INFO: 
create table `review` (`id` int unsigned not null auto_increment primary key comment '評價編號', `member_id` int unsigned not null comment '會員編號', `booking_id` int unsigned not null comment '預約編號', `course_id` int unsigned not null comment '課程編號', `coach_id` int unsigned not null comment '教練編號', `rating` tinyint not null comment '評分', `comment` text null comment '評價內容', `review_images` varchar(190) null comment '評價圖片', `is_anonymous` tinyint not null default '0' comment '是否匿名', `status` tinyint not null default '1' comment '狀態', `reviewed_at` datetime not null comment '評價時間', `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci'
  [執行超過10秒:45.16]
  
[2025-07-01 10:55:34] dev.INFO: 
alter table `review` add index `review_member_id_index`(`member_id`)
  [執行超過10秒:23.21]
  
[2025-07-01 10:55:34] dev.INFO: 
alter table `review` add index `review_booking_id_index`(`booking_id`)
  [執行超過10秒:23.11]
  
[2025-07-01 10:55:34] dev.INFO: 
alter table `review` add index `review_course_id_index`(`course_id`)
  [執行超過10秒:22.25]
  
[2025-07-01 10:55:34] dev.INFO: 
alter table `review` add index `review_coach_id_index`(`coach_id`)
  [執行超過10秒:21.75]
  
[2025-07-01 10:55:34] dev.INFO: 
alter table `review` add index `review_rating_index`(`rating`)
  [執行超過10秒:21.94]
  
[2025-07-01 10:55:34] dev.INFO: 
alter table `review` add index `review_status_index`(`status`)
  [執行超過10秒:20.75]
  
[2025-07-01 10:55:34] dev.INFO: 
alter table `review` add index `review_reviewed_at_index`(`reviewed_at`)
  [執行超過10秒:21.25]
  
[2025-07-01 10:55:34] dev.INFO: 
alter table `review` add unique `review_booking_id_unique`(`booking_id`)
  [執行超過10秒:22.07]
  
[2025-07-01 10:55:34] dev.INFO: 
alter table `review` auto_increment = 10000
  [執行超過10秒:10.32]
  
[2025-07-01 10:55:34] dev.INFO: 
ALTER TABLE review COMMENT 'XX'
  [執行超過10秒:10.98]
  
[2025-07-01 10:55:34] dev.INFO: 
insert into `migrations` (`migration`, `batch`) values ('2025_07_01_103556_create_review_table', '1')
  [執行超過10秒:3.66]
  
[2025-07-01 10:55:34] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:1.85]
  
[2025-07-01 10:55:34] dev.INFO: 
create table `course` (`id` int unsigned not null auto_increment primary key comment '課程編號', `coach_id` int unsigned not null comment '教練編號', `title` varchar(100) not null comment '課程標題', `description` text null comment '課程描述', `difficulty_level` tinyint not null comment '課程難度', `course_status` tinyint not null default '0' comment '課程狀態', `price` decimal(8, 2) not null comment '課程價格', `duration_hours` tinyint not null comment '課程時數', `max_students` tinyint not null default '1' comment '最大學員數', `location` varchar(100) null comment '上課地點', `equipment_required` text null comment '需要設備', `course_outline` text null comment '課程大綱', `images` varchar(190) null comment '課程圖片', `rating` decimal(3, 2) not null default '0' comment '課程評分', `total_bookings` int unsigned not null default '0' comment '預約總數', `created_at` timestamp not null default CURRENT_TIMESTAMP comment '建立時間', `updated_at` timestamp null on update CURRENT_TIMESTAMP comment '更新時間') default character set utf8mb4 collate 'utf8mb4_unicode_ci'
  [執行超過10秒:39.72]
  
[2025-07-01 10:55:34] dev.INFO: 
alter table `course` add index `course_coach_id_index`(`coach_id`)
  [執行超過10秒:21.26]
  
[2025-07-01 10:55:35] dev.INFO: 
alter table `course` add index `course_difficulty_level_index`(`difficulty_level`)
  [執行超過10秒:22.51]
  
[2025-07-01 10:55:35] dev.INFO: 
alter table `course` add index `course_course_status_index`(`course_status`)
  [執行超過10秒:21.02]
  
[2025-07-01 10:55:35] dev.INFO: 
alter table `course` add index `course_price_index`(`price`)
  [執行超過10秒:20.3]
  
[2025-07-01 10:55:35] dev.INFO: 
alter table `course` add index `course_rating_index`(`rating`)
  [執行超過10秒:22.02]
  
[2025-07-01 10:55:35] dev.INFO: 
alter table `course` auto_increment = 10000
  [執行超過10秒:9.53]
  
[2025-07-01 10:55:35] dev.INFO: 
ALTER TABLE course COMMENT 'XX'
  [執行超過10秒:10.13]
  
[2025-07-01 10:55:35] dev.INFO: 
insert into `migrations` (`migration`, `batch`) values ('2025_07_01_103601_create_course_table', '1')
  [執行超過10秒:3.58]
  
