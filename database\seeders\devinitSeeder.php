<?php

namespace Database\Seeders;

use DB;
use Illuminate\Database\Seeder;

class devinitSeeder extends Seeder {

    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run() {
        // $this->companyRepo = app(\App\Repositories\companyRepository::class);
        // $rows = $this->companyRepo->select('id');
        // $rows->myWhere('companynumber|S', '602606', 'del', 'Y');
        // $rows->delete();

        // $inputs = null;
        // $inputs['companynumber'] = '602606';
        // $inputs['subaccount'] = '6';
        // $inputs['companyname'] = '愛視森眼鏡行';
        // $inputs['password'] = \Hash::make("111111");
        // $this->companyRepo->create($inputs)->id;
        // $inputs = null;
        // $inputs['companynumber'] = '602606';
        // $inputs['companyname'] = '愛視森眼鏡行';
        // $inputs['password'] = \Hash::make("111111");
        // $this->companyRepo->create($inputs)->id;



    }
}
