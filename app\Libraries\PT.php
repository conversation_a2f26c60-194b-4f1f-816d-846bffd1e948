<?php

namespace App\Libraries;

use DB;
use PF;

/***
"功能名稱":"客製類別函式",
"建立時間":"2022-01-18 13:18:03",
 ***/
class PT {
    public static function nav($xmldoc, $controller, $subtitle = '') {
        if ('NULL' == gettype($xmldoc)) {
            $xmldoc = PF::xmlDoc('Setup.xml');
        }
        $titles = [];
        $xPath = "//參數設定檔/權限/選單/KIND/傳回值[.='" . $controller . "']/../..";
        $objxml1 = $xmldoc->xpath($xPath);
        if (!empty($objxml1)) {
            $titles[] = $objxml1[0]['主選單名稱'];
            $role = strval($objxml[0]['角色']);
        }

        $xPath = "//參數設定檔/權限/選單/KIND/傳回值[.='" . $controller . "']/parent::*";
        $objxml = $xmldoc->xpath($xPath);
        if (!empty($objxml)) {
            $titles[] = $objxml[0]->資料;

            if ('' == $role) {
                $role = strval($objxml[0]['角色']);
            }
        }

        $title = trim($title);
        if ($controller == $title || '' == $title || '>' == $title) {
            $xPath = "//參數設定檔/權限/選單/KIND/KIND/傳回值[.='" . $controller . "']/../../..";
            $objxml1 = $xmldoc->xpath($xPath);
            if (!empty($objxml1)) {
                $titles[] = $objxml1[0]['主選單名稱'];
            }
            $xPath = "//參數設定檔/權限/選單/KIND/KIND/傳回值[.='" . $controller . "']/../..";
            $objxml1 = $xmldoc->xpath($xPath);
            if (!empty($objxml1)) {
                $titles[] = $objxml1[0]->資料;
            }
            $xPath = "//參數設定檔/權限/選單/KIND/KIND/KIND/傳回值[.='" . $controller . "']/../..";
            $objxml1 = $xmldoc->xpath($xPath);
            if (!empty($objxml1)) {
                $titles[] = $objxml1[0]->資料;
            }
            $xPath = "//參數設定檔/權限/選單/KIND/KIND/傳回值[.='" . $controller . "']/parent::*";
            $objxml = $xmldoc->xpath($xPath);
            if (!empty($objxml)) {
                $titles[] = $objxml[0]->資料;
                if ('' == $role) {
                    $role = strval($objxml[0]['角色']);
                }
            }
            $xPath = "//參數設定檔/權限/選單/KIND/KIND/KIND/傳回值[.='" . $controller . "']/parent::*";
            $objxml = $xmldoc->xpath($xPath);
            if (!empty($objxml)) {
                $titles[] = $objxml[0]->資料;
                if ('' == $role) {
                    $role = strval($objxml[0]['角色']);
                }
            }
        }


        //權限管理

        $title = '';


        if (false == self::checkRoleLimits($role, $controller)) {

            abort(403);
        }
        if (request()->is('api/*')) {
            $cc = "";
            foreach ($titles as $k => $v) {
                $title .= $cc . $v;
                $cc = ' > ';
            }
            $html = $title;
            if ('' != $subtitle) {
                $html .= ' >' . $subtitle;
            }
            $GLOBALS['nav'] = $html;
        } else {
            $title = '';
            foreach ($titles as $k => $v) {
                $title .= '<li class="breadcrumb-item"';
                if ($k == count($titles) - 1) {
                    $title .= ' aria-current="page"';
                }
                $title .= '>';
                $title .= $v;
                $title .= '</li>';
            }

            $html = '<nav aria-label="breadcrumb">';
            $html .= '<ol class="breadcrumb">';
            //$html .= '<li class="breadcrumb-item">' . __('位置') . '</li>';
            $html .= '<li class="breadcrumb-item">' . __('首頁') . '</li>';
            $html .= $title;
            if ('' != $subtitle) {
                $html .= '<li class="breadcrumb-item">' . $subtitle . '</li>';
            }
            $html .= '</ol>';
            $html .= '</nav>';
        }

        return $html;
        //return '&nbsp; &nbsp; '.__('位置').' > '.__('首頁').' > '.$Title;
    }

    // <KIND 角色="limit">
    //     <資料>最新活動</資料>
    //     <傳回值>activity</傳回值>
    //     <網址>activity</網址>
    // </KIND>
    public static function checkRoleLimits($roles, $controller) {
        if ('999' == \Auth::guard('admin')->user()->role) {
            return true;
        }

        $roles = strval($roles);

        if ('ALL' == $roles) {
            return true;
        } elseif ('limit' == $roles) {
            if ('' == \Auth::guard('admin')->user()->limits) {
                return false;
            }
            if (in_array($controller,  \Auth::guard('admin')->user()->limits)) {
                return true;
            }
        } else {
            if (in_array(\Auth::guard('admin')->user()->role, explode(',', $roles))) {
                return true;
            }
        }

        return false;
    }
    public static function checkRoleLimitsThrow($roles, $controller) {

        if (!PT::checkRoleLimits($roles, $controller)) {
            abort(403);
        }
    }


    public static  function randomkeys($len = 6, $chars = '1234567890') {
        // characters to build the password from
        mt_srand((float) microtime() * 1000000 * getmypid());
        // seed the random number generater (must be done)
        $password = '';
        while (strlen($password) < $len) {
            $password .= substr($chars, (mt_rand() % strlen($chars)), 1);
        }

        return $password;
    }

    public static  function getEpostBody($epostid) {
        $epostbody = '';
        $rows = DB::table('epost');
        $rows->selectRaw('epostbody');
        //$rows->myWhere('alg|S', app()->getLocale(), 'lg', 'Y');
        $rows->myWhere('epostid|S', $epostid, 'epostid', 'Y');
        $rows->limit(1);
        //PF::dbSqlPrint($rows);
        if ($rows->count() > 0) {
            $rs = $rows->first();
            $epostbody = $rs->epostbody;
        }

        return $epostbody;
    }

    public static  function code($value) {
        return md5(config('app.code') . '_' . $value);
    }
    public static function getDomain($id = "") {

        //\Cache::forget('domain');

        $rows = \Cache::get('domain');
        if ($id != null) {
            $rs = collect($rows)->where('id', $id)->first();
        } else {
            $rs = collect($rows)->where('domain', \Request::getHttpHost())->first();
        }
        if ($rs != null) {

            if ($rs->end_date != "") {
                if (strtotime(now()) >= strtotime($rs->end_date)) {
                    throw new \CustomException("網域已過期");
                }
            }
        } else {
            // if (\Request::getHttpHost() != "localhost") {
            //     throw new \CustomException("No domain info");
            // }
        }

        return $rs;
    }
}
