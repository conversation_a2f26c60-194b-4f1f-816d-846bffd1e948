<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <system.webServer>
        <!-- 設定預設文件 -->
        <defaultDocument>
            <files>
                <clear />
                <add value="index.php" />
                <add value="index.html" />
            </files>
        </defaultDocument>

        <!-- URL 重寫規則 -->
        <rewrite>
            <rules>
                <!-- 強制 www (被註解掉的規則) -->
                <!--
                <rule name="Force WWW" stopProcessing="true">
                    <match url="(.*)" />
                    <conditions>
                        <add input="{HTTP_HOST}" pattern="^sctt\.com\.tw$" />
                    </conditions>
                    <action type="Redirect" url="http://www.{HTTP_HOST}/{R:1}" redirectType="Permanent" />
                </rule>
                -->

                <!-- 強制 HTTPS (被註解掉的規則) -->
                <!--
                <rule name="Force HTTPS" stopProcessing="true">
                    <match url="(.*)" />
                    <conditions logicalGrouping="MatchAll">
                        <add input="{HTTPS}" pattern="off" />
                        <add input="{HTTP_HOST}" pattern="^www\.sctt\.com\.tw$" />
                    </conditions>
                    <action type="Redirect" url="https://{HTTP_HOST}/{R:1}" redirectType="Permanent" />
                </rule>
                -->
                <!-- Rewrite api paths -->
                <rule name="Rewrite api paths" stopProcessing="true">
                    <match url="^api/(.*)$" />
                    <conditions logicalGrouping="MatchAll">
                        <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
                        <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
                    </conditions>
                    <action type="Rewrite" url="public/index.php" appendQueryString="true" />
                </rule>

                <!-- 重寫到 public/index.php -->
                <rule name="Rewrite to public index" stopProcessing="true">
                    <match url="^([^/]+)/?$" />
                    <conditions logicalGrouping="MatchAll">
                        <add input="{REQUEST_URI}" pattern="^public" negate="true" />
                        <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
                        <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
                    </conditions>
                    <action type="Rewrite" url="public/index.php" appendQueryString="true" />
                </rule>

                <!-- 處理 admin, membercenter, member 的路徑 -->
                <rule name="Rewrite admin/member paths" stopProcessing="true">
                    <match url="^(admin|membercenter|member)/([^/]+)/?$" />
                    <conditions logicalGrouping="MatchAll">
                        <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
                        <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
                    </conditions>
                    <action type="Rewrite" url="public/index.php" appendQueryString="true" />
                </rule>

                <!-- 處理不帶 public 的路徑 -->
                <rule name="Rewrite to public folder" stopProcessing="true">
                    <match url="^(.*)$" />
                    <conditions logicalGrouping="MatchAll">
                        <add input="{REQUEST_URI}" pattern="^/public/" negate="true" />
                        <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
                    </conditions>
                    <action type="Rewrite" url="public/{R:1}" appendQueryString="true" />
                </rule>
            </rules>
        </rewrite>

        <!-- 壓縮 HTML 文件 -->
        <httpCompression>
            <dynamicTypes>
                <add mimeType="text/html" enabled="true" />
            </dynamicTypes>
            <staticTypes>
                <add mimeType="text/html" enabled="true" />
            </staticTypes>
        </httpCompression>
    </system.webServer>
</configuration>