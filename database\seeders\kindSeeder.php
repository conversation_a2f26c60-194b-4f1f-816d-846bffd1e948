<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Repositories\kindRepository;

/***
"功能名稱":"seeder - 建立KIND假資料",
"資料表":"kind",
"備註":" ",
"建立時間":"2022-01-18 17:05:17",
***/
class kindSeeder extends baseSeeder
{
    private $kindRepo;

    public function __construct(kindRepository $kindRepo)
    {
        $this->kindRepo = $kindRepo;
    }

    /**
     * Run the database seeds.
     */
    public function run()
    {
        $kind = 'whitelist';
        $this->kindRepo->select()
        ->myWhere('kind|S', $kind, 'del', 'Y')
        ->delete();

        $this->faker = \Faker\Factory::create('zh_TW');
        /*
             for ($i=0; $i <10 ; $i++) {

                    $data = [
                'kind' => $kind,
                'kindtitle' =>  $this->faker->safeEmailDomain,
            ];
            $this->kindRepo->create($data);
        */
        // $items = ['www.yahoo.com.tw', 'www.google.com'];
        // foreach ($items as $k => $v) {
        //     $data = [
        //     'kind' => $kind,
        //     'kindtitle' => $v,
        //   ];
        //   $this->kindRepo->create($data);
        // }
    }
}
