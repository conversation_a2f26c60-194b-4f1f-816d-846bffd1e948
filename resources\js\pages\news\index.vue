<template>
    <div v-loading="http.getLoading()">
        <h2>{{ $t('訊息公告') }}</h2>
        <my-breadcrumb id="product" refs="breadcrumb"></my-breadcrumb>
        <my-search
            :searchNames="{
                title: '標題'
            }"
            :searchDateNames="{
                created_at: '建立日期'
            }"
            @onSearch="onSearch"
        ></my-search>

        <el-table border :data="data.data" style="width: 100%" @sort-change="onSort">
            <el-table-column prop="title" label="標題" sortable />

            <el-table-column sortable label="建立日期" prop="created_at">
                <template #default="scope">
                    {{ utils.formatDate(scope.row.created_at) }}
                </template>
            </el-table-column>
        </el-table>

        <my-pagination :total.number="data.total" v-model:page="inputs.page" @current-change="onPageChange"></my-pagination>
    </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue'

const http = createHttp() //http套件
const router = useRouter()
const route = useRoute()
const store = useDataStore()
const myDialog = ref('')

//const emits = defineEmits(["close"]); //接受外面送來的觸發的參數
const props = defineProps({}) //接受外面送來的參數

// 使用 reactive 定義對象
const data = reactive({
    data: [] as news[]
})
const edit = ref('')
// interface Irepo {
//     resultcode: string
//     resultmessage: string
//     data: any
// }

const inputs = reactive({
    search: '',
    page: 1,
    sortname: '',
    sorttype: ''
})
//分頁事件
function onPageChange(page) {
    inputs.page = page
    getData()
}
//排序事件
const onSort = async column => {
    inputs.sortname = column.prop
    inputs.sorttype = column.order == 'ascending' ? 'desc' : 'asc'
    getData()
}

//搜尋事件
const onSearch = async searchdatas => {
    inputs.page = 1
    inputs.sortname = ''
    Object.assign(inputs, searchdatas)

    getData()
}

//下載資料
const getData = async () => {
    try {
        let rep = await http.post('api/news', inputs)
        //debugger;
        if (rep.resultcode == '0') {
            Object.assign(data, rep.data)
        } else {
            throw new Error(rep.resultmessage)
        }
    } catch (error) {
        utils.formElError(error)
        console.error(error)
    } finally {
    }
}

onMounted(async () => {
    //console.clear();
    //console.clear();
    //console.clear();
    //console.log('lang', lang)
    //console.log('router', router.currentRoute.value.params.lang)
    //console.log('lang', store.lang)

    getData()
})
</script>
<style scoped></style>
