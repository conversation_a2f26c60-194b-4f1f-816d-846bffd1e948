<?php

namespace App\Models;

use DB;
use Illuminate\Database\Eloquent\Model;

//管理者後入LOG
class adminuserloginlog extends baseModel
{
    public $tabletitle = '管理者後入LOG';
    public $table = 'adminuserloginlog';
    public $primaryKey = 'id';

    //欄位必填
    public $rules = [
        'admiuserloginid' => 'required',
'account' => 'required',
    ];
    public $fieldInfo = [
'id'=>['title'=>'自動編號','type'=>'int(10) unsigned'],//
'account'=>['title'=>'帳號','type'=>'varchar(50)'],//
'clientip'=>['title'=>'IP','type'=>'varchar(255)'],//
'loginstatus'=>['title'=>'登入狀態','type'=>'varchar(50)'],//
'logouttime'=>['title'=>'時間','type'=>'datetime'],//
'created_at'=>['title'=>'建立時間','type'=>'timestamp'],//
'updated_at'=>['title'=>'編輯時間','type'=>'timestamp'],//
];
    //日期欄位的儲存格式。'Y-m-d' or 'U' or ...
    //protected $dateFormat = 'Y-m-d';
    public $timestamps = false;
    protected $fillable = ['account','clientip','loginstatus','logouttime','created_at','updated_at']; //可充許傳入的欄位
    protected $guarded = [];
    protected $dates = ['logouttime','created_at','updated_at'];

    public static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            //$model->uuid = (string) Uuid::generate();
        });
        self::created(function ($model) {
            //$model->uuid = (string) Uuid::generate();
        });
        static::updating(function ($model) {
            // do some logging
        });
        self::updated(function ($model) {
        });
        static::deleting(function ($model) {
            //  DB::table('adminuserloginlog')->select()
            // ->myWhere('admiuserloginid|N', $model->admiuserloginid, "admiuserloginid", 'Y')
            // ->delete();
        });
        static::deleted(function ($model) {
        });
    }
}
