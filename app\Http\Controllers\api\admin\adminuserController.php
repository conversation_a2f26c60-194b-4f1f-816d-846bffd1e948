<?php

namespace App\Http\Controllers\api\admin;

use PF, PT;
use Exception, DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\adminuser;
use App\Http\Controllers\Controller\api\admin;

/***
"功能名稱":"管理人員",
"資料表":"adminuser",
"建立時間":"2024-06-11 13:01:42 ",
 ***/
class adminuserController extends Controller {

    private $data;
    private $xmlDoc;


    public function __construct() {

        //$this->limit="xx";
        parent::__construct();
        //將request全部導入到$this->data變數中
        $this->data = PF::requestAll($this->data);
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');

        //$this->data['nav'] = PT::nav($this->data['xmldoc'],"adminuser",$this->data['nav']);
        $this->data['displaynames'] = adminuser::getFieldTitleArray();
    }


    /**
     * @OA\Post(
     *     path="/api/admin/adminuser",security={{"bearerAuth":{}}},operationId="",tags={"後台/管理人員"},summary="列表",description="",
     *     @OA\RequestBody(required=true,
     *      @OA\JsonContent(
     *      allOf={

     *         @OA\Schema(@OA\Property(property="page",description="頁數",type="integer",example="1",)),
     *         @OA\Schema(@OA\Property(property="pagesize",description="筆數/頁",type="integer",example="10",)),
     *         @OA\Schema(@OA\Property(property="search",description="搜尋",type="string",example="",)),
     *          @OA\Schema(@OA\Property(property="sortname", type="string",description="排序欄位", example="",)),
     *          @OA\Schema(@OA\Property(property="sorttype", type="string",description="排序方式", example="desc",)),
     *         @OA\Schema(@OA\Property(property="searchstartdate",description="開始時間",type="string",example="2021-01-01",)),
     *         @OA\Schema(@OA\Property(property="searchenddate",description="結束時間",type="string",example="2099-12-31",)),
     *     })

     *   ,),

     *   @OA\Response(response=200,description="回覆",
     *     @OA\JsonContent(type="object",
     *      @OA\Property(property="resultcode",type="integer",description="訊息代碼",),
     *      @OA\Property(property="resultmessage",type="string",description="訊息內容"),
     *      @OA\Property(property="data", type="object",
     *          @OA\Property(property="current_page", type="integer",description="目前頁數", ),
     *          @OA\Property(property="total", type="integer",description="總頁數", ),

     *      @OA\Property(property="data",  type="array",
     *      @OA\Items(allOf={
     *         @OA\Schema(ref="#/components/schemas/adminuser"),
     *         @OA\Schema(@OA\Property(property="", type="string",description="", example="") ),
     *     }))

     *      ),)
     * ),)
     */


    public function index(Request $request) {
        $rows = $this->getRows($request);
        //$rows = $rows->take(10);
        //PF::dbSqlPrint($rows);
        //$rows = $rows->get();
        $pagesize = (is_numeric($request->input('pagesize'))  ? $request->input('pagesize') : 10);
        $rows = $rows->paginate($pagesize);

        foreach ($rows as $key => $rs) {
            unset($rs->password);
            unset($rs->api_token);
            unset($rs->remember_token);
            unset($rs->lastlogin_ip);
        }

        // 顯示sqlcmd
        $this->jsondata['data'] = $rows;
        return $this->apiResponse($this->jsondata);
    }

    public function getRows($request) {
        //id,account,name,email,role,online,failcount,created_at
        $rows = DB::table("adminuser")->selectRaw('adminuser.*');
        //依條件搜尋資料的SQL語法
        //$rows->myWhere('name', $request->input('search'), $this->data['displaynames'], 'N');
        //依條件搜尋資料的SQL語法
        $rows->myWhere($request->input('searchname'), $request->input('search'), $this->data['displaynames'], 'N');
        //依條件時間搜尋資料的SQL語法
        $rows->myWhere('convert(adminuser.created_at,DATE)|>=', $request->input('searchstartdate'), $this->data['displaynames'], 'N');
        $rows->myWhere('convert(adminuser.created_at,DATE)|<=', $request->input('searchenddate'), $this->data['displaynames'], 'N');
        if ($request->input('sortname')) {
            $rows->orderBy($request->input('sortname'), $request->input('sorttype') == "desc"  ? $request->input('sorttype') : "asc");
        } else {
            $rows->orderByRaw('adminuser.id desc');
        }
        return $rows;
    }


    /**
     * @OA\Post(
     *     path="/api/admin/adminuser/show",security={{"bearerAuth":{}}},operationId="",tags={"後台/管理人員"},summary="單筆顯示",description="",
     *     @OA\RequestBody(required=true,
     *      @OA\JsonContent(
     *      allOf={

     *         @OA\Schema(@OA\Property(property="id",description="編號",type="integer",example="1",)),

     *     })
     *   ,),

     *   @OA\Response(response=200,description="回覆",
     *     @OA\JsonContent(type="object",
     *      @OA\Property(property="resultcode",type="integer",description="訊息代碼",),
     *      @OA\Property(property="resultmessage",type="string",description="訊息內容"),

     *      @OA\Property(property="data", type="object",
     *      allOf={
     *         @OA\Schema(ref="#/components/schemas/adminuser"),
     *         @OA\Schema(type="object",@OA\Property(property="", type="string",description="系列", example="") ),

     *     })

     *     ,)
     *),)
     */


    public function show($request) {


        $rows = \App\Models\adminuser::selectRaw('adminuser.*');
        $rows->where('id', '=', $request->input('id'));
        $rs = $rows->firstOrFail();


        unset($rs->password);
        unset($rs->api_token);
        unset($rs->remember_token);
        unset($rs->lastlogin_ip);

        $this->jsondata['data'] = $rs;

        return $this->apiResponse($this->jsondata);
    }


    /**
     * @OA\Post(
     *     path="/api/admin/adminuser/store",security={{"bearerAuth":{}}},operationId="",tags={"後台/管理人員"},summary="新增/編輯",description="編號有值代表編輯,沒有代表新增",
     *     @OA\RequestBody(required=true,

     *      @OA\JsonContent(
     *      allOf={
     *         @OA\Schema(ref="#/components/schemas/adminuser"),
     *         @OA\Schema(type="object",@OA\Property(property="", type="string",description="系列", example="") ),
     *     })

     *   ,),
     *   @OA\Response(response=200,description="回覆",
     *     @OA\JsonContent(type="object",
     *      @OA\Property(property="resultcode",type="integer",description="訊息代碼",),
     *      @OA\Property(property="resultmessage",type="string",description="訊息內容"),
     *        @OA\Property(property="data", type="object",
     *         allOf={
     *             @OA\Schema(@OA\Property(property="id", type="integer",description="編號", example="10101") ),
     *         }
     *        )
     *     ),)
     *),)
     */


    public function store(Request $request) {

        $edit = $request->input('id');
        //FIXME 那些欄位為必填判斷
        $validators = null;
        $validators['account'] = ['required']; //帳號
        $validators['name'] = ['required']; //姓名
        if ('' == $edit) {
            $validators['password'] = ['required', 'min:8']; //密碼
        } else {
            $validators['password'] = ['nullable', 'min:8']; //密碼
        }
        $validators['email'] = ['nullable', 'email']; //EMAIL
        //$validators['failcount'] = ['required']; //登入錯誤次數
        if ($validators != null) {
            $validator = \Validator::make($request->all(), $validators);
            $validator->setAttributeNames($this->data['displaynames']);
            if ($validator->fails()) {
                throw new \CustomException(implode(',', $validator->messages()->all()));
            }
        }
        $inputs = $request->all();
        //$inputs['account']=$this->data['account'];//帳號-
        //$inputs['name']=$this->data['name'];//姓名-
        if ($this->data['password'] != "") {
            $inputs['password'] = \Hash::make($this->data['password']); //密碼-
        } else {
            unset($inputs['password']); //密碼-
        }
        //$inputs['email']=$this->data['email'];//EMAIL-
        //$inputs['role']=$this->data['role'];//角色-[998:經銷商 ; 999:管理者 ; ]
        //$inputs['online']=$this->data['online'];//開啟-
        //$inputs['failcount']=$this->data['failcount'];//登入錯誤次數-



        $inputs['online'] = $this->data['online']; //開啟-
        if ('' == $edit) {
            //$inputs['api_token'] = hash('sha256', \Str::random(80));


            $inputs['failcount'] = 0; //登入錯誤次數-

            $edit = adminuser::create($inputs)->id;

            $this->jsondata['resultmessage'] = '新增成功';
        } else {

            $rows = adminuser::selectRaw('adminuser.*');
            $rows->myWhere('id|N', $edit, 'edit', 'Y');
            $rs = $rows->firstOrFail();


            $rs->update($inputs);

            $this->jsondata['resultmessage'] = '更新成功';
        }
        $this->jsondata['data']['id'] = $edit;
        return $this->apiResponse($this->jsondata);
    }
    /**
     * @OA\Post(
     *     path="/api/admin/adminuser/destroy",security={{"bearerAuth":{}}},operationId="",tags={"後台/管理人員"},summary="刪除",description="",
     *   @OA\RequestBody(required=true,@OA\MediaType(mediaType="application/json",@OA\Schema(
     *         @OA\Property(property="del",description="要刪除的編號",type="integer",example="1",description="多筆中間用逗號",),

     *       ),
     *   ),),
     *   @OA\Response(response=200,description="回覆",
     *     @OA\JsonContent(type="object",
     *      @OA\Property(property="resultcode",type="integer",description="訊息代碼",),
     *      @OA\Property(property="resultmessage",type="string",description="訊息內容"),
     *     ),)
     *),)
     */

    public function destroy(Request $request) {
        $rows = adminuser::selectRaw('adminuser.id');
        //$rows = $this->checkRoleRows($rows);
        $rows->myWhere('id|ININT', $this->data['del'], 'del', 'Y');
        //$rows->delete();
        $rows->chunk(200, function ($rows) {
            foreach ($rows as $rs) {
                $rs->delete();
            }
        });


        $this->jsondata['resultmessage'] = '刪除成功';
        return $this->apiResponse($this->jsondata);
    }
}
