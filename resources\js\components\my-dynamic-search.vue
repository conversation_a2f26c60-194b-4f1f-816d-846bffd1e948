<template>
    <div class="dynamic-search-container">
        <!-- 統一搜尋條件列表 -->
        <transition-group name="search-condition" tag="div">
            <div v-for="(condition, index) in searchConditions" :key="condition.id" class="search-condition-row">
                <!-- 邏輯運算子 (除了第一行) -->
                <template v-if="index > 0">
                    <el-select v-model="condition.operator" placeholder="邏輯" class="operator-select">
                        <el-option label="且" value="AND"></el-option>
                        <el-option label="或" value="OR"></el-option>
                    </el-select>
                </template>

                <!-- 搜尋欄位選擇 -->
                <el-select v-model="condition.field" placeholder="選擇欄位" class="field-select" @change="onFieldChange(index)">
                    <el-option v-for="(label, key) in allSearchFields" :key="key" :value="key" :label="label" />
                </el-select>

                <!-- 日期欄位特殊處理 -->
                <template v-if="condition.fieldType === 'date'">
                    <el-select v-model="condition.comparison" placeholder="比較" class="comparison-select">
                        <el-option label="等於" value="="></el-option>
                        <el-option label="大於" value=">"></el-option>
                        <el-option label="小於" value="<"></el-option>
                        <el-option label="大於等於" value=">="></el-option>
                        <el-option label="小於等於" value="<="></el-option>
                        <el-option label="範圍" value="BETWEEN"></el-option>
                    </el-select>

                    <template v-if="condition.comparison === 'BETWEEN'">
                        <div class="date-range-container">
                            <my-dateform v-model="condition.startDate" placeholder="開始日期" type="date" class="date-input" />
                            <my-dateform v-model="condition.endDate" placeholder="結束日期" type="date" class="date-input" />
                        </div>
                    </template>
                    <template v-else>
                        <my-dateform v-model="condition.value" placeholder="選擇日期" type="date" class="date-input" />
                    </template>
                </template>

                <!-- 一般欄位處理 -->
                <template v-else>
                    <!-- 比較運算子 -->
                    <el-select v-model="condition.comparison" placeholder="比較" class="comparison-select">
                        <!-- 如果有下拉選項（select類型），只顯示等於 -->
                        <template v-if="condition.selectOptions.length > 0">
                            <el-option label="等於" value="="></el-option>
                        </template>
                        <!-- 數字欄位顯示數字比較選項 -->
                        <template v-else-if="condition.fieldType === 'number'">
                            <el-option label="等於" value="="></el-option>
                            <el-option label="不等於" value="!="></el-option>
                            <el-option label="大於" value=">"></el-option>
                            <el-option label="小於" value="<"></el-option>
                            <el-option label="大於等於" value=">="></el-option>
                            <el-option label="小於等於" value="<="></el-option>
                        </template>
                        <!-- 一般文字欄位只顯示包含和等於 -->
                        <template v-else>
                            <el-option label="包含" value="LIKE"></el-option>
                            <el-option label="等於" value="="></el-option>
                        </template>
                    </el-select>

                    <!-- 搜尋值輸入 -->
                    <template v-if="condition.selectOptions.length > 0">
                        <!-- 如果有下拉選項，顯示下拉選單 -->
                        <el-select v-model="condition.value" placeholder="選擇值" class="value-select">
                            <el-option value="">全部</el-option>
                            <el-option
                                v-for="option in condition.selectOptions"
                                :key="option.value"
                                :value="option.value"
                                :label="option.label"
                            />
                        </el-select>
                    </template>
                    <template v-else>
                        <!-- 一般文字輸入 -->
                        <el-input v-model="condition.value" placeholder="請輸入搜尋值" class="value-input" />
                    </template>
                </template>

                <!-- 刪除按鈕 -->
                <button
                    type="button"
                    class="btn btn-danger btn-sm remove-btn"
                    @click="removeCondition(index)"
                    :disabled="searchConditions.length === 1"
                >
                    <i class="fa fa-trash"></i>
                </button>
            </div>
        </transition-group>

        <!-- 操作按鈕 -->
        <div class="action-buttons">
            <button type="button" class="btn btn-primary btn-sm add-btn" @click="addCondition"><i class="fa fa-plus"></i> 增加條件</button>

            <button type="button" class="btn btn-success btn-sm search-btn" @click="onSearch"><i class="fa fa-search"></i> 搜尋</button>

            <button type="button" class="btn btn-warning btn-sm clear-btn" @click="clearAll"><i class="fa fa-refresh"></i> 清除</button>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useDataStore } from '@/stores'
import { createHttp } from '@/utils/http'
import { computed, onMounted, ref } from 'vue'

const http = createHttp()
const store = useDataStore()

// 定義組件屬性
const props = defineProps({
    searchNames: {
        type: Object,
        required: false,
        default: () => ({})
    },
    searchDateNames: {
        type: Object,
        required: false,
        default: () => ({})
    }
})

// 定義事件
const emit = defineEmits(['onSearch'])

// 統一搜尋條件介面定義
interface UnifiedSearchCondition {
    id: string
    operator: string
    field: string
    fieldType: 'text' | 'date' | 'select' | 'number'
    comparison: string
    value: string
    startDate?: string
    endDate?: string
    selectOptions: Array<{ value: string; label: string }>
}

// 統一搜尋條件陣列
const searchConditions = ref<UnifiedSearchCondition[]>([])

// 建立統一的搜尋欄位對應表
const allSearchFields = computed(() => {
    const fields = { ...props.searchNames }

    // 將日期欄位也加入統一列表
    if (props.searchDateNames) {
        Object.assign(fields, props.searchDateNames)
    }

    return fields
})

/**
 * 生成唯一 ID
 */
const generateId = () => {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9)
}

/**
 * 判斷欄位類型
 */
const getFieldType = (field: string): 'text' | 'date' | 'select' | 'number' => {
    if (props.searchDateNames && props.searchDateNames[field]) {
        return 'date'
    }
    // 檢查是否為數字欄位 (含有|INT標記)
    if (field.includes('|INT')) {
        return 'number'
    }
    return 'text'
}

/**
 * 增加搜尋條件
 */
const addCondition = () => {
    searchConditions.value.push({
        id: generateId(),
        operator: 'AND',
        field: '',
        fieldType: 'text',
        comparison: 'LIKE',
        value: '',
        selectOptions: []
    })
}

/**
 * 移除搜尋條件
 * @param index 要移除的條件索引
 */
const removeCondition = (index: number) => {
    if (searchConditions.value.length > 1) {
        searchConditions.value.splice(index, 1)
    }
}

/**
 * 當欄位變更時，檢查是否需要載入下拉選項
 * @param index 條件索引
 */
const onFieldChange = async (index: number) => {
    const condition = searchConditions.value[index]

    // 重置條件
    condition.value = ''
    condition.startDate = ''
    condition.endDate = ''
    condition.selectOptions = []

    // 設定欄位類型
    condition.fieldType = getFieldType(condition.field)

    // 根據欄位類型設定預設比較運算子
    if (condition.fieldType === 'date') {
        condition.comparison = 'BETWEEN'
    } else if (condition.fieldType === 'select') {
        condition.comparison = '='
    } else if (condition.fieldType === 'number') {
        condition.comparison = '='
    } else {
        condition.comparison = 'LIKE'
    }

    if (!condition.field) return

    // 如果是日期欄位，不需要載入下拉選項
    if (condition.fieldType === 'date') return

    try {
        const label = allSearchFields.value[condition.field]
        let labels = []

        // 檢查是否有預設的下拉選項
        if (typeof store.setup[label] !== 'undefined') {
            labels = store.setup[label]['KIND'] || []
        }

        if (labels.length > 0) {
            // 使用預設的下拉選項
            if (Array.isArray(labels)) {
                labels.forEach((rs: any) => {
                    condition.selectOptions.push({
                        value: rs['傳回值'] || rs['資料'], // 簡化：優先使用傳回值，否則使用資料
                        label: rs['資料']
                    })
                })
                condition.fieldType = 'select'
                condition.comparison = '=' // 設定為等於
            }
        } else {
            // 從 API 載入下拉選項
            const response = await http.post('api/admin/dependentdropdown', { label }, false)

            if (response.resultcode === 0 && response.data && response.data.length > 0) {
                response.data.forEach((rs: any) => {
                    const values = Object.values(rs)
                    condition.selectOptions.push({
                        value: String(values[0]), // 直接使用第一個值
                        label: String(values[1] || values[0]) // 標籤使用第二個值，沒有則用第一個
                    })
                })
                condition.fieldType = 'select'
                condition.comparison = '=' // 設定為等於
            }
        }
    } catch (error) {
        console.error('載入下拉選項時發生錯誤:', error)
    }
}

/**
 * 執行搜尋
 */
const onSearch = () => {
    console.log('=== 開始搜尋檢查 ===')
    console.log('所有條件:', JSON.stringify(searchConditions.value, null, 2))

    // 過濾有效的搜尋條件 - 改善邏輯
    const validConditions = searchConditions.value.filter((condition, index) => {
        console.log(`檢查條件 ${index}:`, {
            field: condition.field,
            fieldType: condition.fieldType,
            comparison: condition.comparison,
            value: condition.value,
            startDate: condition.startDate,
            endDate: condition.endDate
        })

        // 必須有選擇欄位
        if (!condition.field) {
            console.log(`條件 ${index} 被過濾: 沒有選擇欄位`)
            return false
        }

        if (condition.fieldType === 'date') {
            if (condition.comparison === 'BETWEEN') {
                const isValid = condition.startDate && condition.endDate
                console.log(`條件 ${index} 日期範圍檢查:`, {
                    startDate: condition.startDate,
                    endDate: condition.endDate,
                    isValid
                })
                if (!isValid) {
                    console.log(`條件 ${index} 被過濾: 日期範圍不完整`)
                }
                return isValid
            } else {
                const isValid = condition.value !== '' && condition.value !== null && condition.value !== undefined
                console.log(`條件 ${index} 日期單值檢查:`, { value: condition.value, isValid })
                if (!isValid) {
                    console.log(`條件 ${index} 被過濾: 日期值為空`)
                }
                return isValid
            }
        } else if (condition.fieldType === 'select') {
            const isValid = condition.value !== '' && condition.value !== null && condition.value !== undefined
            console.log(`條件 ${index} select檢查:`, { value: condition.value, isValid })
            if (!isValid) {
                console.log(`條件 ${index} 被過濾: select值為空`)
            }
            return isValid
        } else {
            const isValid = condition.value !== '' && condition.value !== null && condition.value !== undefined
            console.log(`條件 ${index} text檢查:`, { value: condition.value, isValid })
            if (!isValid) {
                console.log(`條件 ${index} 被過濾: text值為空`)
            }
            return isValid
        }
    })

    console.log('過濾後的有效條件:', validConditions)

    // 轉換為統一的搜尋參數格式
    const searchParams = {
        dynamicConditions: validConditions.map(condition => ({
            ...condition,
            // select 直接回傳 value，其他類型確保是字串格式
            value: condition.fieldType === 'select' ? condition.value : String(condition.value || '')
        }))
    }

    //console.log('最終發送的搜尋參數:', JSON.stringify(searchParams, null, 2))

    // 發送搜尋事件
    emit('onSearch', searchParams)
}

/**
 * 清除所有搜尋條件
 */
const clearAll = () => {
    searchConditions.value = [
        {
            id: generateId(),
            operator: 'AND',
            field: '',
            fieldType: 'text',
            comparison: 'LIKE',
            value: '',
            selectOptions: []
        }
    ]
}

// 組件掛載時初始化
onMounted(() => {
    // 初始化第一個搜尋條件
    addCondition()
})

// 暴露方法供父組件使用
defineExpose({
    clearAll,
    searchConditions,
    allSearchFields
})
</script>

<style scoped>
.dynamic-search-container {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 15px;
}

.dynamic-search-container h5 {
    color: #495057;
    margin-bottom: 15px;
    font-weight: 600;
    font-size: 16px;
}

.search-condition-row {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    padding: 8px 12px;
    background: #ffffff;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    min-height: 40px;
    position: relative;
}

.date-search-section {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #dee2e6;
}

.date-condition-row {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 10px;
    flex-wrap: wrap;
}

.operator-select {
    width: 70px;
    flex-shrink: 0;
}

.field-select {
    width: 140px;
    flex-shrink: 0;
}

.comparison-select {
    width: 80px;
    flex-shrink: 0;
}

.value-input,
.value-select {
    width: 180px;
    flex-shrink: 0;
}

.date-input {
    width: 130px;
    flex-shrink: 0;
}

.date-range-container {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
    width: 100%;
}

.remove-btn {
    width: 32px;
    height: 32px;
    flex-shrink: 0;
    margin-left: auto;
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-buttons {
    display: flex;
    gap: 8px;
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #dee2e6;
    flex-wrap: wrap;
    justify-content: flex-start;
}

.add-btn,
.search-btn,
.clear-btn {
    min-width: 80px;
    max-width: 120px;
    flex: none;
}

/* PC版本樣式優化 */
@media (min-width: 769px) {
    .search-condition-row {
        flex-wrap: nowrap;
        max-width: 100%;
        overflow-x: auto;
    }

    .dynamic-search-container {
        max-width: 1200px;
    }

    /* 確保控制項尺寸固定 */
    .operator-select .el-input,
    .field-select .el-input,
    .comparison-select .el-input,
    .value-select .el-input,
    .value-input .el-input,
    .date-input .el-input {
        min-width: auto !important;
    }
}

/* 響應式設計 - 手機版 */
@media (max-width: 768px) {
    .search-condition-row {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
        padding: 15px;
    }

    .operator-select,
    .field-select,
    .comparison-select,
    .value-input,
    .value-select,
    .date-input {
        width: 100% !important;
        flex: none;
    }

    .date-range-container {
        flex-direction: column;
        gap: 10px;
        width: 100%;
        align-items: stretch;
    }

    .date-range-container .date-input,
    .date-input {
        width: 100% !important;
    }

    .date-range-container .date-input .el-date-editor,
    .date-input .el-date-editor,
    .date-range-container .date-input .el-input,
    .date-input .el-input,
    .date-range-container .date-input > div,
    .date-input > div {
        width: 100% !important;
        min-width: auto !important;
        max-width: none !important;
    }

    .remove-btn {
        align-self: center;
        width: 36px !important;
        height: 36px;
        margin-top: 8px;
        margin-left: 0;
        font-size: 14px;
    }

    .action-buttons {
        flex-direction: row;
        gap: 10px;
        justify-content: center;
        flex-wrap: wrap;
    }

    .add-btn,
    .search-btn,
    .clear-btn {
        flex: none;
        min-width: 80px;
        max-width: 120px;
    }

    .dynamic-search-container {
        min-width: auto;
        padding: 15px;
    }
}

h5 {
    color: #495057;
    margin-bottom: 10px;
    font-weight: 600;
}

/* 動畫效果 */
.search-condition-enter-active,
.search-condition-leave-active {
    transition: all 0.3s ease;
}

.search-condition-enter-from {
    opacity: 0;
    transform: translateY(-10px);
}

.search-condition-leave-to {
    opacity: 0;
    transform: translateX(10px);
}

.search-condition-move {
    transition: transform 0.3s ease;
}
</style>
