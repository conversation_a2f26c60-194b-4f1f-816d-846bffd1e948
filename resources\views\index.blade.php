<!doctype html>
<html lang="{{ app()->getLocale() }}">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta http-equiv="content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="content-Language" content="{{ app()->getLocale() }}" />

    <title>{{ $title != '' ? $title . ' | ' . PF::getConfig('title') : PF::getConfig('title') }}</title>
    <meta name="keywords" content="{{ $keyword != '' ? $keyword : PF::getConfig('keyword') }}" />
    <meta name="description" content="{{ $description != '' ? $description : PF::getConfig('description') }}" />
    <meta name="copyright" content="{{ PF::getConfig('name') }}" />
    <meta name="distribution" content="Taiwan" />
    <meta name="revisit-after" content="1 days" />
    <meta name="robots" content="index,follow" />
    <!--FaceBook-->
    <meta property="og:title"
        content="{{ $title != '' ? $title . ' | ' . PF::getConfig('title') : PF::getConfig('title') }}" />
    <meta property="og:image" content="{{ $img }}" />
    <meta property="og:url" content="{{ Request::url() }}" />
    <meta property="og:site_name" content="{{ PF::getConfig('title') }}" />
    <meta property="og:description" content="{{ $description != '' ? $description : PF::getConfig('description') }}" />
    <meta property="og:type" content="website" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Cache-Control" content="private" />
    <meta http-equiv="Expires" content="0" />
    <link rel='index' title="{{ PF::getConfig('title') }}" href="{{ str_replace(' /public', '/', url('/')) }}" />
    <link rel="canonical" href="{{ str_replace(' /public/', '/', Request::url()) }}" />
    <meta content="no" http-equiv="imagetoolbar" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="HandheldFriendly" content="True" />
    <link rel="icon" href="{{ url('/') }}/images/favicon.ico">
    <meta name="viewport"
        content="width=device-width, initial-scale=1.0, maximum-scale=1, minimum-scale=1, user-scalable=no, minimal-ui,shrink-to-fit=no">



    {!! $head !!}

    {!! $ld_json !!}

</head>

<body leftmargin="0" topmargin="0" marginwidth="0" marginheight="0" class="xs">

    @if ($title != '')
        <noscript>
            <header>
                <h1>{{ $title }}</h1>
            </header>
            <section>
                {{ $description != '' ? $description : PF::getConfig('description') }}
                @yield('content')
            </section>

        </noscript>
    @endif

    {!! $body !!}



    @include('layouts.debug')
</body>

</html>
