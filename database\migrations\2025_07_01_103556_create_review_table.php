<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
//php artisan migrate:refresh --path=/database/migrations/_create_review_table.php
return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        if (!Schema::hasTable('review')) {
            Schema::create('review', function (Blueprint $table) {
                //$table->engine = 'MyISAM';
                $table->increments('id')->from(10000)->comment('評價編號');
                $table->unsignedInteger('member_id')->comment('會員編號');
                $table->unsignedInteger('booking_id')->comment('預約編號');
                $table->unsignedInteger('course_id')->comment('課程編號');
                $table->unsignedInteger('coach_id')->comment('教練編號');
                $table->tinyInteger('rating')->comment('評分'); // 1-5 分
                $table->text('comment')->nullable()->comment('評價內容');
                $table->string('review_images')->nullable()->comment('評價圖片');
                $table->tinyInteger('is_anonymous')->default(0)->comment('是否匿名'); // 0:非匿名 1:匿名
                $table->tinyInteger('status')->default(1)->comment('狀態'); // 1:正常 0:隱藏
                $table->dateTime('reviewed_at')->comment('評價時間');
                $table->timestamps();

                // 建立索引
                $table->index(['member_id']);
                $table->index(['booking_id']);
                $table->index(['course_id']);
                $table->index(['coach_id']);
                $table->index(['rating']);
                $table->index(['status']);
                $table->index(['reviewed_at']);

                // 確保一個預約只能有一個評價
                $table->unique(['booking_id']);
            });
            \DB::statement("ALTER TABLE review COMMENT 'XX'");
        }
        /*
        $table->unsignedBigInteger('activitysession_id')->comment('場次');
        $table->foreign('activitysession_id')->references('id')->on('activitysession')->onDelete('cascade');

        $table->string('kind',50)->index()->comment('種類');
        $table->mediumText('body')->nullable()->comment('說明');
        $table->dateTime('begindate')->nullable()->comment('開始時間');
        $table->integer('hits')->default(0)->comment('點率次數');
        $table->float('sortnum', 5, 3)->nullable()->comment('排序號碼');
        $table->integer('adminsuer_id')->nullable()->comment('編輯人員');
        $table->string('adminuser_name', 50)->nullable()->comment('編輯人員');
        $table->string('edit_account',50)->comment('修改人');
        $table->string('account',50)->unique();;
        $table->timestamps('reviewed_at')->default('now');
        $table->unique(array('kind', 'kindid'));
        $table->index(array('kind', 'kindid'));
        $table->softDeletes();

        */
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::dropIfExists('review');
    }
};
