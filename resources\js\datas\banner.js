import { ref } from 'vue';

export const fields = ref([

    {
        label: "標題",
        name: "title",
        kind: "el-input",
        rules: [
            {
                required: true,
                message: "標題 未填",
            },
        ],
        value: "",
        islist: true,
        issearch: true,
        isedit: true

    },
    {
        label: "圖",
        name: "field1",
        kind: "my-upload",
        props: {
            width: "1118",
            height: "600",
            folder: "images/banner",
            limit: 1
        },
        rules: [
            {
                required: false,
                message: "圖 未上傳",
            },
        ],
        value: "",
        islist: false,
        isedit: true
    },
    {
        label: "youtube網址",
        name: "field2",
        kind: "el-input",
        rules: [
            {
                type: 'url',
                required: false,
                message: "標題 未填",
            },
        ],
        value: "",
        islist: false,
        issearch: true,
        isedit: true

    },

    {
        label: "連結",
        name: "memo",
        kind: "el-input",
        props: { placeholder: "https://www.yahoo.com.tw", type: "url", },
        rules: [
            {

                required: false,
                message: "錯誤",
            },
            {
                type: "url",
                required: false,
                message: "連結未填",
            },
        ],
        value: "",
        islist: false,
        isedit: true
    },
    {
        label: "開啟方式",
        name: "field9",
        kind: "my-xmlform",
        props: {
            type: "radio",
            xpath: "開啟方式",
        },
        rules: [
            {
                required: false,
                message: "開啟方式 未填",
            },
        ],
        value: "",

        islist: true,
        issearch: true,
        isedit: true
    },
    {
        label: "開始日期",
        name: "begindate",
        kind: "my-dateform",
        props: {
            type: "date",
        },
        rules: [
            {
                required: false,
                message: "建立日期 未填",
            },
        ],
        value: "",
        islist: true,
        issearch: true,
        isedit: true

    },
    {
        label: "結束日期",
        name: "closedate",
        kind: "my-dateform",
        props: {
            type: "date",
        },
        rules: [
            {
                required: false,
                message: "結束日期 未填",
            },
        ],
        value: "",
        islist: true,
        issearch: true,
        isedit: true

    },
    {
        label: "順序",
        name: "boardsort",
        kind: "el-input",
        props: {
            type: "number",
        },
        rules: [
            {
                required: false,
                message: "順序 未填",
            },
        ],
        value: "",
        isedit: true,
        islist: true,
        issearch: false,
        memo: '數字小到大'

    },
    {
        label: "建立日期",
        name: "created_at",
        kind: "my-dateform",
        props: {
            type: "date",
        },
        rules: [
            {
                required: true,
                message: "建立日期 未填",
            },
        ],
        value: "",
        islist: true,
        issearch: true,
        isedit: true

    },
]);