# TASK001 - 建立滑雪平台基礎架構與首頁

## 任務描述

建立滑雪平台網站的基礎架構，包含前後端基礎配置、資料庫架構設計，以及實作首頁功能。此任務將建立整個專案的基礎框架，為後續功能開發做準備。

### 主要功能內容：

1. 設計並建立核心資料庫表結構
2. 建立前端基礎布局和導航結構
3. 實作首頁展示內容
4. 建立基礎 API 路由架構
5. 設定前後端認證機制基礎

## 狀態

-   [x] 計劃中
-   [ ] 測試單元編寫中
-   [ ] 開發中
-   [ ] 完成

## 驗收標準

-   [ ] 完成核心資料庫表結構設計（member、coach、booking、review 等主要表）
-   [ ] 建立前端主要布局（header、footer、導航選單）
-   [ ] 首頁能正常顯示且具備基本 RWD 響應式設計
-   [ ] 前後端認證機制設定完成（Laravel Sanctum）
-   [ ] 建立基礎 API 路由結構
-   [ ] 所有頁面能正常載入，無 404 錯誤

## 注意事項

-   檔案名稱首字母必須小寫
-   會員相關功能統一使用 `member` 而非 `user`
-   資料庫表名與檔案名保持高度一致，不使用連字號 `-`
-   認證機制使用 Laravel Sanctum
-   API 統一使用 POST 方法
-   前端布局需考慮後續功能模組的擴展性
