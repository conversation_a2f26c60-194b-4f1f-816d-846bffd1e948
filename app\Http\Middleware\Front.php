<?php

namespace App\Http\Middleware;

use Closure;
use Session;

class Front extends baseMiddleware {
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure                 $next
     *
     * @return mixed
     */
    public function handle($request, Closure $next) {

        //支援語系
        // if ('/' == $request->path() || '/public/' == $request->path()) {
        //     $language = substr($request->server('HTTP_ACCEPT_LANGUAGE'), 0, 2);
        //     switch ($language) {
        //     case 'zh':
        //         $redirect = 'zh/index';
        //         break;
        //     case 'en':
        //         $redirect = 'en/index';
        //         break;
        //     default:
        //         $redirect = 'en/index';
        //         break;
        //     }

        //     return redirect($redirect);
        // }
        // $desiredLocale = $request->segment(1);
        // in_array($desiredLocale, config('app.locales')) ? app()->setLocale($desiredLocale) : app()->setLocale('en');
        // $request->merge(['middlewareurl' => url('/').'/'.app()->getLocale().'/']);




        // 調用父類的 handle 方法
        $response = parent::handle($request, $next);


        return $response;
    }
}
