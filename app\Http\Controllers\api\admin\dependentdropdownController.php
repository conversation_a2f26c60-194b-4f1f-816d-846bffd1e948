<?php

namespace App\Http\Controllers\api\admin;

use DB;
use Illuminate\Http\Request;
//use Illuminate\Support\Facades\DB;



class dependentdropdownController extends Controller {
    private $data;

    private $kindmainRepo;
    private $classtRepo;



    /**
     *建構子.
     */
    public function __construct() {
        //$this->limit="xx";
        parent::__construct();
    }
    public function index(Request $request) {
        $rows = [];
        switch ($request->input('label')) {
            case '編輯人員':
                $rows = DB::table('adminuser')->selectRaw('id as value,name as label ');
                $rows = $rows->orderByRaw('name');
                //\PF::dbSqlPrint($rows);
                $rows = $rows->get();
                break;

            default:
                # code...
                break;
        }


        $this->jsondata['data'] = $rows;

        return $this->apiResponse($this->jsondata);
    }
    public function kindhead(Request $request) {

        $rows = DB::table('kindhead')->selectRaw('id,title ');
        $rows = $rows->orderByRaw('sortnum');
        //\PF::dbSqlPrint($rows);
        $rows = $rows->get();

        $this->jsondata['data'] = $rows;

        return response()->json($this->jsondata, 200, ['Content-Type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);
    }
    public function kindmain(Request $request) {
        $datas['output'] = [];

        $rows = DB::table('kindmain')->selectRaw('id,title ');

        $rows->myWhere('kindhead_id|N', $request->input('kindhead_id'), "kindhead_id", 'Y');

        $rows = $rows->orderByRaw('sortnum');
        //\PF::dbSqlPrint($rows);
        $rows = $rows->get();

        $this->jsondata['data'] = $rows;

        return response()->json($this->jsondata, 200, ['Content-Type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);
    }
    public function kind(Request $request) {
        $datas['output'] = [];

        $rows = DB::table('kind')->selectRaw('id,kindtitle ');

        $rows->myWhere('kind|S', $request->input('kind'), "kind", 'Y');

        $rows = $rows->orderByRaw('kindsortnum');
        //\PF::dbSqlPrint($rows);
        $rows = $rows->get();

        $this->jsondata['data'] = $rows;

        return response()->json($this->jsondata, 200, ['Content-Type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);
    }
}
