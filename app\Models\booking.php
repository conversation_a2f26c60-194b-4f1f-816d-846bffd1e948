<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/*swagger api document start*/

/**
 * @OA\Schema(
 *   schema="booking",
 *      allOf={
 *         @OA\Schema( @OA\Property(property="id", type="integer", description="預約編號", example="10001")),
 *         @OA\Schema( @OA\Property(property="member_id", type="integer", description="會員編號", example="10001")),
 *         @OA\Schema( @OA\Property(property="course_id", type="integer", description="課程編號", example="10001")),
 *         @OA\Schema( @OA\Property(property="coach_id", type="integer", description="教練編號", example="10001")),
 *         @OA\Schema( @OA\Property(property="booking_number", type="string", description="預約單號", example="BK20240101001")),
 *         @OA\Schema( @OA\Property(property="booking_date", type="string", description="預約日期時間", example="2024-01-15 10:00:00")),
 *         @OA\Schema( @OA\Property(property="start_time", type="string", description="開始時間", example="2024-01-15 10:00:00")),
 *         @OA\Schema( @OA\Property(property="end_time", type="string", description="結束時間", example="2024-01-15 12:00:00")),
 *         @OA\Schema( @OA\Property(property="booking_status", type="integer", description="預約狀態", example="1")),
 *         @OA\Schema( @OA\Property(property="total_amount", type="number", description="總金額", example="1500.00")),
 *         @OA\Schema( @OA\Property(property="paid_amount", type="number", description="已付金額", example="1500.00")),
 *         @OA\Schema( @OA\Property(property="payment_status", type="integer", description="付款狀態", example="1")),
 *         @OA\Schema( @OA\Property(property="payment_method", type="string", description="付款方式", example="credit_card")),
 *         @OA\Schema( @OA\Property(property="payment_transaction_id", type="string", description="付款交易號", example="TXN123456789")),
 *         @OA\Schema( @OA\Property(property="special_requests", type="string", description="特殊需求", example="需要租借雪具")),
 *         @OA\Schema( @OA\Property(property="cancellation_reason", type="string", description="取消原因", example="天候不佳")),
 *         @OA\Schema( @OA\Property(property="cancelled_at", type="string", description="取消時間", example="2024-01-14 15:00:00")),
 *         @OA\Schema( @OA\Property(property="created_at", type="string", description="建立時間", example="2024-01-01 10:00:00")),
 *         @OA\Schema( @OA\Property(property="updated_at", type="string", description="更新時間", example="2024-01-01 10:00:00")),
 *      }
 *)
 */
/*swagger api document end*/

class booking extends Model {
    use HasFactory;

    /**
     * 資料表名稱
     */
    protected $table = 'booking';

    /**
     * 可批量賦值的屬性
     */
    protected $fillable = [
        'member_id',
        'course_id',
        'coach_id',
        'booking_number',
        'booking_date',
        'start_time',
        'end_time',
        'booking_status',
        'total_amount',
        'paid_amount',
        'payment_status',
        'payment_method',
        'payment_transaction_id',
        'special_requests',
        'cancellation_reason',
        'cancelled_at',
    ];

    /**
     * 屬性轉換
     */
    protected $casts = [
        'booking_date' => 'datetime',
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'cancelled_at' => 'datetime',
        'total_amount' => 'decimal:2',
        'paid_amount' => 'decimal:2',
    ];

    /*Relations start*/
    /**
     * 與 member 表的關聯 - 預約會員
     */
    public function member() {
        return $this->belongsTo(member::class, 'member_id', 'id');
    }

    /**
     * 與 course 表的關聯 - 預約課程
     */
    public function course() {
        return $this->belongsTo(course::class, 'course_id', 'id');
    }

    /**
     * 與 coach 表的關聯 - 預約教練
     */
    public function coach() {
        return $this->belongsTo(coach::class, 'coach_id', 'id');
    }

    /**
     * 與 review 表的關聯 - 預約的評價
     */
    public function review() {
        return $this->hasOne(review::class, 'booking_id', 'id');
    }
    /*Relations end*/

    /**
     * 模型事件
     */
    public static function boot() {
        parent::boot();

        static::creating(function ($model) {
            // 建立時生成預約單號
            if (empty($model->booking_number)) {
                $model->booking_number = 'BK' . date('Ymd') . str_pad(
                    static::whereDate('created_at', date('Y-m-d'))->count() + 1,
                    3,
                    '0',
                    STR_PAD_LEFT
                );
            }
        });

        static::updating(function ($model) {
            // 更新時的邏輯處理
        });

        static::deleted(function ($model) {
            /*Del Relations start*/
            // 刪除預約時，同時刪除相關評價
            if ($model->review) {
                $model->review->delete();
            }
            /*Del Relations end*/
        });
    }
}
