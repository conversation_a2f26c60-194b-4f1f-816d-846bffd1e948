<?php

namespace App\Http\Controllers\api\admin;

use Illuminate\Http\Request;
use App\Libraries\UploadFile;

class uploadController extends Controller
{
    private $fieldnicknames;
    private $data;
    private $xmlDoc;

    /**
     *建構子.
     */
    public function __construct()
    {
        //$this->limit="xx";
        parent::__construct();
    }

    /**
     * 資料列表.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {

        if ($request->file('upload')) {
            //ckeditor upload
            $inputs = [];
            try {
                /*檔案上傳*/
                $upload = new UploadFile();
                $upload->request = $request;
                $upload->inputs = $inputs;
                $upload->folder = 'images/epost';
                $upload->width = 2000;
                $upload->height = 9000;
                //$upload->limitext = config('app.FileLimit');
                $inputs = $upload->execute();
                //if ($request->input('ckCsrfToken') != "") {
                //return response()->json(['upload' => 1, 'url' => url('/') . "/" . $upload->folder . "/" . $inputs['upload']]);
                //}
                //return response()->json(['uploaded' => 1, 'fileName' => $inputs['upload'], 'url' => url('/') . "/" . $upload->folder . "/" . $inputs['upload']]);
                return response()->json(['uploaded' => 1, 'fileName' => $inputs['upload'], 'url' => "/" . $upload->folder . "/" . $inputs['upload']]);
            } catch (\Exception $e) {
                return response($e->getMessage());
            }
        } else {
            $validators = [];
            //$validators['f'] = 'required';

            $validators['file'] = ['required', 'mimes:' . \config('config.uploadfilelimit'), 'max:' . (\config('config.uploadfilesize') * 1024)];
            //$this->data['displaynames']['f'] = '請輸入資料';
            $validator = \Validator::make($request->all(), $validators);
            //$validator->setAttributeNames($this->data['displaynames']);
            $validator->setAttributeNames(array_keys($validators));
            if ($validator->fails()) {
                throw new \CustomException(implode(',', $validator->messages()->all()));
            }


            $folder = "epost";
            if ($request->input('f') != "") {
                $folder = $request->input('f');
            }
            if (false == \File::isDirectory(public_path('images/' . $folder))) {
                throw new \CustomException("not folder limit");
            }
            $width = $request->input('width');
            if ($request->input('width') != "") {
                $width = $request->input('width');
            }
            $height = $request->input('height');
            if ($request->input('height') != "") {
                $height = $request->input('height');
            }
            if ($request->hasFile('file')) {
                $inputs = [];
                try {
                    /*檔案上傳*/
                    $upload = new UploadFile();
                    $upload->request = $request;
                    $upload->inputs = $inputs;
                    $upload->folder = 'images/' . $folder;
                    $upload->width = $width;
                    $upload->height = $height;
                    //$upload->limitext = config('app.FileLimit');
                    $inputs = $upload->execute();
                    //    return response()->json(['name' => $inputs['file']], 200);
                    $this->jsondata['data']['name'] = $inputs['file'];
                    $this->jsondata['data']['url'] =  $inputs['file'];
                } catch (\Exception $e) {
                    return response($e->getMessage());
                }
            }
        }
        return $this->apiResponse($this->jsondata);
        //return response()->json(['error' => 'No file uploaded'], 400);
    }
    public function ckeditor(Request $request)
    {
        if ($request->hasFile('upload')) {
            $inputs = null;
            try {
                /*檔案上傳*/
                $upload = new UploadFile();
                $upload->request = $request;
                $upload->inputs = $inputs;
                $upload->folder = 'images/epost/';
                $upload->width = '800';
                $upload->height = '99900';
                //$upload->limitext = config('app.FileLimit');
                $inputs = $upload->execute();

                // Render HTML output
                @header('Content-type: text/html; charset=utf-8');
                $url = str_replace(asset('/'), '', '../../' . asset('images/epost/' . $inputs['upload']));
                $url = str_replace('../../', '/', $url);
                $CKEditorFuncNum = $request->input('CKEditorFuncNum');

                return response('<script type="text/javascript">window.parent.CKEDITOR.tools.callFunction(' . $CKEditorFuncNum . ", '" . $url . "', '');</script>");
            } catch (\Exception $e) {

                return response($e->getMessage());
            }
        } else {
            throw new \CustomException("no upload file");
        }


    }
}
