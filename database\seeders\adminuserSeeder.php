<?php

namespace Database\Seeders;

use DB;
use PF;
use Illuminate\Database\Seeder;
use App\Repositories\adminuserRepository;

/***
"功能名稱":"seeder - 建立管理人員資料",
"資料表":"adminuser",
"備註":" ",
"建立時間":"2022-01-18 17:05:17",
 ***/
class adminuserSeeder extends baseSeeder {

    public function __construct() {
        parent::__construct();
    }

    /**
     * Run the database seeds.
     */
    public function run() {
        DB::table('adminuser')->truncate();

        $faker = \Faker\Factory::create('zh_TW');

        $data[] = [


            'account' => 'admin',
            'name' => '管理者',
            'email' => '<EMAIL>',
            'password' => \Hash::make('a123456789'),

            'role' => '999',
            'online' => 1,
            'created_at' => now()
        ];

        $data[] = [
            'account' => 'allen',
            'name' => '開發工程師',
            'email' => '<EMAIL>',
            'password' => '$2y$10$QqK3/cmCNTizm74mi/5hmub1bFaFvu3PYWJso8JbO6QgGAfJCDkK6',

            'role' => '999',
            'online' => 1,
            'created_at' => now()

        ];
        $data[] = [
            'account' => 'test',
            'name' => '測試人員',
            'email' => '<EMAIL>',
            'password' => \Hash::make('a123456789'),
            'role' => '998',
            'online' => 1,
            'created_at' => now()

        ];

        // $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
        // foreach ($this->data['xmldoc']->xpath('//參數設定檔/角色/KIND[傳回值!=999]') as $v) {
        //     $data[] = [
        //         'id' => $id,
        //         'domain_id' => '10000',
        //         'account' => 'test' . $v->傳回值,
        //         'name' => $v->資料,
        //         'email' => '<EMAIL>',
        //         'password' => \Hash::make('a123456789'),
        //         'api_token' => 'allen' . $v->傳回值,
        //         'role' => $v->傳回值,
        //         'online' => 1,
        //     ];
        //     ++$id;
        // }

        DB::table('adminuser')->insert($data);

        // $this->call(UsersTableSeeder::class);
    }
}
