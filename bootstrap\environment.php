<?php

use Dotenv\Dotenv;

/*
 * 依據使用的.env檔載入
 * 改完名env檔名後需要清除cache.
 */

if (mb_substr_count(base_path(), 'AppServ\\laravel\\') > 0) {
    //$environmentPath = $app->environmentPath(); //.env所在目录
    //echo "environmentPath:".$environmentPath;
    //$app->loadEnvironmentFrom('/.env.production');

    $env = $app->detectEnvironment(function () {
        $dotenv = Dotenv::createImmutable(dirname(__DIR__) . '/', '.env.dev');
        $dotenv->load(); //this is important
    });
} elseif (mb_substr_count(base_path(), 'test') > 0 || mb_substr_count(base_path(), 'demo') > 0) {
    $env = $app->detectEnvironment(function () {
        $dotenv = Dotenv::createImmutable(dirname(__DIR__) . '/', '.env.test');
        $dotenv->load(); //this is important
    });
}
