<?php

namespace App\Http\Controllers\api;

use Illuminate\Http\Request;
//use Illuminate\Support\Facades\DB;

class kindController extends Controller {
    private $data;
    private $kindmainRepo;

    /**
     *建構子.
     */
    public function __construct() {
        //$this->limit="xx";
        parent::__construct();
    }
    /**
     * @OA\Post(
     *     path="/api/kind",operationId="index",tags={"前台/種類"},summary="列表",description="",
     *     @OA\RequestBody(required=true,

     *      @OA\JsonContent(
     *      allOf={

     *         @OA\Schema(@OA\Property(property="page",description="頁數",type="integer",example="1",)),
     *         @OA\Schema(@OA\Property(property="pagesize",description="筆數/頁",type="integer",example="10",)),
     *         @OA\Schema(@OA\Property(property="search",description="搜尋",type="string",example="",)),
     *     })

     *   ,),
     *   @OA\Response(response=200,description="回覆",
     *     @OA\JsonContent(type="object",
     *      @OA\Property(property="resultcode",type="integer",description="訊息代碼",),
     *      @OA\Property(property="resultmessage",type="string",description="訊息內容"),
     *      @OA\Property(property="data", type="object",
     *          @OA\Property(property="current_page", type="integer",description="目前頁數", ),
     *          @OA\Property(property="total", type="integer",description="總頁數", ),
     *
     *      @OA\Property(property="data",  type="array",
     *      @OA\Items(allOf={
     *         @OA\Schema(ref="#/components/schemas/kind"),


     *     }))

     *      ),)
     * ),)
     */
    public function index(Request $request) {
        $rows = \DB::table('kind')->selectRaw('kind.*');
        $rows->myWhere('kind|S', $request->input('kind'), "kind", 'N');

        $rows->orderByRaw('kindsortnum');
        $rows = $rows->paginate(10);

        $this->jsondata['data'] = $rows;

        return response()->json($this->jsondata, 200, ['Content-Type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);
    }
}
