<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "DiscussionForumPosting",
    "headline": "{{ $rs->title }} - {{PF::getConfig('name')}}",
    "text": "{{ $rs->body }}",
    @if ($rs->img!="")
    "image": {
        "@type": "ImageObject",
        "contentUrl": "{{$imgurl }}{{ $rs->img }}",
        "name": "{{ $rs->title }}",
        "uploadDate": "{{ $rs->created_at!="" ? $rs->created_at->toIso8601String() : now()->toIso8601String() }}",
        "thumbnailUrl": ""
    },
    @endif
    "url": "{{ Request::getUri() }}",
    "author": {
        "@type": "Person",
        "name": "{{PF::getConfig('name')}}",
        "url": "{{ url('/') }}",
        "agentInteractionStatistic": {
            "@type": "InteractionCounter",
            "interactionType": "https://schema.org/WriteAction",
            "userInteractionCount": {{$rs->hits!=''? $rs->hits :0 }}
        }
    },
    "datePublished": "{{ $rs->created_at!="" ? $rs->created_at->toIso8601String() : now()->toIso8601String() }}",
    "interactionStatistic": {
        "@type": "InteractionCounter",
        "interactionType": "https://schema.org/LikeAction",
        "userInteractionCount": {{$rs->hits!=''? $rs->hits :0 }}
    },
    "comment": [
        {
            "@type": "Comment",
            "text": "{{ $rs->rebody }}",
            "author": {
                "@type": "Person",
                "name": "{{PF::getConfig('name')}}",
                "url": "{{ Request::getUri() }}",
                "agentInteractionStatistic": {
                    "@type": "InteractionCounter",
                    "interactionType": "https://schema.org/WriteAction",
                    "userInteractionCount": {{$rs->hits!=''? $rs->hits :0 }}
                }
            },
            "datePublished": "{{ $rs->created_at!="" ? $rs->created_at->toIso8601String() : now()->toIso8601String() }}"
        }
    ]
}
</script>
