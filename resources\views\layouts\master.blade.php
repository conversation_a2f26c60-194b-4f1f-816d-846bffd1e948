<!doctype html>
<html lang="{{ app()->getLocale() }}">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta http-equiv="content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="content-Language" content="{{ app()->getLocale() }}" />

    <title>@yield('title', PF::getConfig('title'))</title>
    <meta name="keywords" content="@yield('keyword', PF::getConfig('keyword'))" />
    <meta name="description" content="@yield('description', PF::getConfig('description'))" />
    <meta name="copyright" content="{{ PF::getConfig('name') }}" />
    <meta name="distribution" content="Taiwan" />
    <meta name="revisit-after" content="1 days" />
    <meta name="robots" content="index,follow" />
    <!--FaceBook-->
    <meta property="og:title" content="@yield('title', PF::getConfig('title'))" />
    <meta property="og:image" content="{{ PF::getConfig('image') }}" />
    <meta property="og:url" content="{{ Request::url() }}" />
    <meta property="og:site_name" content="{{ PF::getConfig('title') }}" />
    <meta property="og:description" content="{{ PF::getConfig('description') }}" />
    <meta property="og:type" content="website" />
    <!--FaceBook-->
    @if (request::is('admin/*') || request::is('admincontrol/*'))
        <meta name="api_token" content="{{ Auth::guard('admin')->user()->api_token }}">
    @elseif (request::is('membercenter/*'))
        <meta name="api_token" content="{{ Auth::guard('member')->user()->api_token }}">
    @endif
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Cache-Control" content="private" />
    <meta http-equiv="Expires" content="0" />
    <link rel='index' title="{{ PF::getConfig('title') }}" href="{{ str_replace(' /public', '/', url('/')) }}" />
    <link rel="canonical" href="{{ str_replace(' /public/', '/', Request::url()) }}" />
    <meta content="no" http-equiv="imagetoolbar" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="HandheldFriendly" content="True" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    {{-- <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests"> --}}

    <!-- Favicons -->
    <link rel="shortcut icon" href="{{ asset('images/favicon.ico') }}" type="image/x-icon">

    <link href="{{ asset('css/bootstrap.min.css') }}" rel="stylesheet" type="text/css" />
    <link href="{{ asset('css/font-awesome.min.css') }}" rel="stylesheet" type="text/css" />
    <link href="{{ asset('css/sweetalert2.min.css') }}" rel="stylesheet" type="text/css" />
    <link href="{{ asset('css/css.css') }}?d={{ config('app.env') != 'production' ? date('His') : '5' }}"
        rel="stylesheet" type="text/css" />

    <!-- Google Font: Source Sans Pro -->
    <link rel="stylesheet"
        href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">

    @yield('css')


    <script type="text/javascript" src="{{ asset('Scripts/jquery.js') }}"></script>
    <script type="text/javascript" src="{{ asset('Scripts/sweetalert.min.js') }}"></script>
    <script type="text/javascript"
        src="{{ asset('Scripts/PJSFunc.js') }}?d={{ config('app.env') != 'production' ? date('His') : '1' }}"></script>
    <script type="text/javascript" src="{{ asset('Scripts/JSFunc.js') }}"></script>

    @include('layouts.header')

    @yield('js')

</head>

<base target="_self">

<body leftmargin="0" topmargin="0" marginwidth="0" marginheight="0">

    <div class="loading show" id="loading">
        <svg class="spinner" width="65px" height="65px" viewBox="0 0 66 66" xmlns="http://www.w3.org/2000/svg">
            <circle id="circle" class="path" fill="none" stroke-width="6" stroke-linecap="round" cx="33"
                cy="33" r="30">
            </circle>
        </svg>
    </div>
    <div id="app">
        @yield('content')
    </div>

</body>

</html>
