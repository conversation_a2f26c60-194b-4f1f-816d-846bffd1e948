html,
body {
    -webkit-overflow-scrolling: touch !important;
    overflow: auto !important;
    margin: 0 auto;
    padding: 0 auto;
}

.el-card {
    margin-left: auto;
    margin-right: auto;
}

.inline {
    display: flex;
    gap: 10px;
    /* 控制每個 div 之間的間距 */
    flex: 1;
}


.grecaptcha-badge {
    display: none;
}

.el-form {
    width: 98%;
}

/*mobile  */
@media screen and (max-width: 724px) and (min-width: 0) {
    .table-responsive-md .el-select,
    .table-responsive-md textarea {
        min-width: 100px !important;
    }
    .el-input__inner {
        min-width: 100px;
        font-size: 16px !important;
    }
}

.hcenter {
    margin: 0 auto;
}

/*pc*/
@media screen and (min-width: 725px) {
    .el-card {
        width: 640px;
    }

    .vcenter {
        min-height: 100vh;

        box-sizing: border-box;
        display: grid;
        place-items: center;
    }

    .el-card {
        max-width: 98%;
    }
}

.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}

/* 在全局 CSS 文件中添加 */

button {
    word-break: keep-all;
    /* 不換行 */
    white-space: nowrap;
    /* 不換行 */
}

.table td,
.table th {
    padding: 0.25rem;
    vertical-align: middle !important;

    text-wrap: nowrap;
}

.table td {
    text-wrap: auto;
}

th.sortable {
    position: relative;
    padding-right: 2px;
    /* Adjust as needed */
}

th.sortable::after {
    content: '\f0de \A \f0dd';
    /* 改成 Font Awesome 5 Pro 的字型名稱 */
    font-family: 'Font Awesome 5 Pro';
    font-size: 9px;
    /* 使用 FontAwesome 字體 */
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    color: #888;

    /* 調整顏色 */
    /* 調整大小 */
    line-height: 0.1;
    /* 調整箭頭之間的距離 */
    display: inline-block;
    text-align: center;
    white-space: pre;
    /* 允许使用换行符 */
}

.form-group label {
    font-weight: Semi Bold;
}

input[type='number'],
.el-select {
    min-width: 66px;
}

.form-group label,
td,
th {
    font-size: 17px;
}
