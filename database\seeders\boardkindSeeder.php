<?php

namespace Database\Seeders;

use App\Repositories\boardRepository;

class boardkindSeeder extends baseSeeder
{
    private $boardRepo;

    public function __construct(boardRepository $boardRepo)
    {
        parent::__construct();

        $this->boardRepo = $boardRepo;
    }

    /**
     * Run the database seeds.
     */
    public function run()
    {
        $this->boardRepo->select()
        ->myWhere('kind|S', 'news', 'del', 'Y')
        ->delete();

        $this->kindRepo = app(\App\Repositories\kindRepository::class);
        $rows = $this->kindRepo->selectRaw('id');
        $rows->myWhere('kind|S', 'newskind', 'kind', 'Y');
        $rows = $rows->get();
        foreach ($rows as $rs) {
            $data = [
                'kind' => 'news',
                'kind_id' => $rs->id,
                'title' => '花盒子飲食生活公告',
                'memo' => 'announcement',
                'body' => $this->faker->realText(),
                //'field1' => $faker->file('C:\AppServ\laravel\1\Pictures\banner.jpg', 'C:\AppServ\laravel\e\storage\app\public\images\news', false),
                //'field1' => $faker->image(public_path('images/place'), 800, 800, 'cats',false),
                //'field1' => $this->myfaker->getImage('images/banner', 1200, 360),

                // 'field2' => '(02)2314-6871',
                // 'field3' => '(02)2331-8047',
                //'body' => $faker->randomHtml(2, 3),
                'hits' => 0,
                'begindate' => date('Y-m-d', strtotime(date('Y-m-d', strtotime('-1 month')))),
            ];
            $this->boardRepo->create($data);
        }

        // $this->call(UsersTableSeeder::class);
    }
}
