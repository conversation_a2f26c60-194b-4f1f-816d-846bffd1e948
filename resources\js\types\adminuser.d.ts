/**
 * adminuser 資料表類型定義
 * * 管理人員
 */
declare global {
    interface adminuser {
        id: number
        account: string // 帳號
        name: string // 姓名
        password: string // 密碼
        email?: string // 電子信箱
        role?: number // 角色
        limits?: string // 權限
        lastlogin_dt?: string // 最後登入時間
        lastlogin_ip?: string // 最後登入IP
        online?: number // 是否核可
        failcount?: number // 登入錯誤次數
        created_at?: string
        updated_at?: string
    }
}

export {}
