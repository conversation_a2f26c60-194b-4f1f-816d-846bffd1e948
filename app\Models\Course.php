<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/*swagger api document start*/

/**
 * @OA\Schema(
 *   schema="course",
 *      allOf={
 *         @OA\Schema( @OA\Property(property="id", type="integer", description="課程編號", example="10001")),
 *         @OA\Schema( @OA\Property(property="coach_id", type="integer", description="教練編號", example="10001")),
 *         @OA\Schema( @OA\Property(property="title", type="string", description="課程標題", example="基礎滑雪入門課程")),
 *         @OA\Schema( @OA\Property(property="description", type="string", description="課程描述", example="適合初學者的基礎滑雪課程")),
 *         @OA\Schema( @OA\Property(property="difficulty_level", type="integer", description="課程難度", example="1")),
 *         @OA\Schema( @OA\Property(property="course_status", type="integer", description="課程狀態", example="1")),
 *         @OA\Schema( @OA\Property(property="price", type="number", description="課程價格", example="1500.00")),
 *         @OA\Schema( @OA\Property(property="duration_hours", type="integer", description="課程時數", example="2")),
 *         @OA\Schema( @OA\Property(property="max_students", type="integer", description="最大學員數", example="4")),
 *         @OA\Schema( @OA\Property(property="location", type="string", description="上課地點", example="新手滑雪道")),
 *         @OA\Schema( @OA\Property(property="equipment_required", type="string", description="需要設備", example="滑雪板、雪靴、護具")),
 *         @OA\Schema( @OA\Property(property="course_outline", type="string", description="課程大綱", example="基礎滑雪技巧、安全知識")),
 *         @OA\Schema( @OA\Property(property="images", type="string", description="課程圖片", example="course1.jpg,course2.jpg")),
 *         @OA\Schema( @OA\Property(property="rating", type="number", description="課程評分", example="4.5")),
 *         @OA\Schema( @OA\Property(property="total_bookings", type="integer", description="預約總數", example="15")),
 *         @OA\Schema( @OA\Property(property="created_at", type="string", description="建立時間", example="2024-01-01 10:00:00")),
 *         @OA\Schema( @OA\Property(property="updated_at", type="string", description="更新時間", example="2024-01-01 10:00:00")),
 *      }
 *)
 */
/*swagger api document end*/

class course extends Model {
    use HasFactory;

    /**
     * 資料表名稱
     */
    protected $table = 'course';

    /**
     * 可批量賦值的屬性
     */
    protected $fillable = [
        'coach_id',
        'title',
        'description',
        'difficulty_level',
        'course_status',
        'price',
        'duration_hours',
        'max_students',
        'location',
        'equipment_required',
        'course_outline',
        'images',
        'rating',
        'total_bookings',
    ];

    /**
     * 屬性轉換
     */
    protected $casts = [
        'price' => 'decimal:2',
        'rating' => 'decimal:2',
    ];

    /*Relations start*/
    /**
     * 與 coach 表的關聯 - 所屬教練
     */
    public function coach() {
        return $this->belongsTo(coach::class, 'coach_id', 'id');
    }

    /**
     * 與 booking 表的關聯 - 課程的預約記錄
     */
    public function bookings() {
        return $this->hasMany(booking::class, 'course_id', 'id');
    }

    /**
     * 與 review 表的關聯 - 課程的評價記錄
     */
    public function reviews() {
        return $this->hasMany(review::class, 'course_id', 'id');
    }
    /*Relations end*/

    /**
     * 模型事件
     */
    public static function boot() {
        parent::boot();

        static::creating(function ($model) {
            // 建立時的邏輯處理
        });

        static::updating(function ($model) {
            // 更新時的邏輯處理
        });

        static::deleted(function ($model) {
            /*Del Relations start*/
            // 刪除課程時，同時刪除相關資料
            $model->bookings()->delete();
            $model->reviews()->delete();
            /*Del Relations end*/
        });
    }
}
