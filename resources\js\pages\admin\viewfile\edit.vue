<template>
    <my-breadcrumb :id="inputs.edit"></my-breadcrumb>

    <!--// TODO : 前端資料填寫-->
    <el-form ref="formEl" :model="inputs" v-loading="http.getLoading()">
        <el-form-item
            label="內容"
            prop="body"
            :rules="[
                {
                    required: true,
                    message: '內容 未填'
                }
            ]"
        >
            <template v-if="type == 'text'">
                <el-input
                    v-model="inputs.body"
                    style="width: 98%"
                    type="textarea"
                    :rows="30"
                    placeholder="Please input"
                />
            </template>

            <template v-else>
                <my-ckeditor v-model="inputs.body" />
            </template>
        </el-form-item>

        <div align="center"><el-button type="primary" @click="onSubmit"> 確定</el-button> </div>
    </el-form>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'

import { createHttp } from '@/utils/http'
const http = createHttp() //http套件

import { utils } from '@/utils' //工具套件
const router = useRouter()
const route = useRoute()

const props = defineProps(['edit', 'type'])

// 使用 reactive 定義對象
const datas = ref([])
const inputs = reactive({
    type: '',
    body: '',
    edit: route.query?.edit || ''
})
//const templetedata = reactive({});
const getData = async () => {
    try {
        if (typeof inputs.edit == 'undefined' || inputs.edit == '') {
            return
        }
        //console.log(['inputs.edit', inputs.edit])
        let rep = await http.post('api/admin/viewfile/show', inputs)

        if (rep.resultcode == '0') {
            Object.keys(rep.data).forEach(key => {
                inputs[key] = rep.data[key]
            })
            //console.log(["inputs", inputs]);
            //console.log(["inputs", inputs]);
        } else {
            utils.alert(rep.resultmessage)
        }
    } catch (error) {
        utils.alert(error)
        console.error(error)
    } finally {
    }
}
watch(
    () => route.fullPath,
    () => {
        inputs.edit = route.query?.edit || ''
        inputs.type = route.query?.type
        getData()
    }
)
const formEl = ref(null)
const onSubmit = async () => {
    if (!formEl.value) return

    try {
        const valid = await formEl.value.validate()
        if (valid) {
            try {
                let rep = await http.post('api/admin/viewfile/store', inputs)
                //debugger;
                if (rep.resultcode == '0') {
                    utils.toast(rep.resultmessage)
                } else {
                    throw new Error(rep.resultmessage)
                }
            } catch (error) {
                utils.formElError(error)
                console.error(error)
            }
        }
    } catch (error) {
        console.error('Validation error:', error)
    }
}

onMounted(async () => {
    getData()
})
</script>
