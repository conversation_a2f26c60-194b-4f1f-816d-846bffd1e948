// composables/useRecaptcha.ts
import { ref } from 'vue'

/**
 * 惰性加载 Google reCAPTCHA 脚本，并获取 token
 * （以 v3 为例）
 */
export function useRecaptcha() {
    // 仅需一次加载即可，用一个状态来标记是否已加载

    const isScriptLoaded = ref(false)

    /**
     * 动态插入 reCAPTCHA 脚本
     * @param siteKey 你的 reCAPTCHA site key
     * @returns Promise<void>
     */
    async function loadScript(siteKey: string) {
        if (isScriptLoaded.value) return

        // 仅在浏览器环境才加载
        if (typeof window === 'undefined') return

        return new Promise<void>((resolve, reject) => {
            // 如果已经有同样的 script，就直接认定它已经加载过
            const existingScript = document.getElementById('recaptcha-script') as HTMLScriptElement
            if (existingScript) {
                isScriptLoaded.value = true
                return resolve()
            }

            // 创建 script 标签
            const script = document.createElement('script')
            script.id = 'recaptcha-script'
            script.src = `https://www.google.com/recaptcha/api.js?render=${siteKey}`
            script.async = true
            script.defer = true

            // 加载成功后
            script.onload = () => {
                isScriptLoaded.value = true
                resolve()
            }
            // 加载出错
            script.onerror = () => {
                reject(new Error('Failed to load reCAPTCHA script.'))
            }

            document.head.appendChild(script)
        })
    }

    /**
     * 获取 reCAPTCHA token
     * @param siteKey  你的 reCAPTCHA site key
     * @param action   reCAPTCHA v3 的 action 名（自定义，如 'login'、'submit'）
     * @returns Promise<string> 返回 token
     */
    async function getRecaptchaToken(action: string): Promise<string> {
        const siteKey: string = utils.getConfig('RECAPTCHA_ID')
        // 如果脚本未加载，则先加载
        if (!isScriptLoaded.value) {
            await loadScript(siteKey)
        }

        // 当脚本加载完成后，grecaptcha 才可用
        // 使用 v3 时，需要先调用 grecaptcha.ready()
        if (typeof window.grecaptcha === 'undefined') {
            throw new Error('grecaptcha is not available.')
        }

        return new Promise((resolve, reject) => {
            window.grecaptcha.ready(() => {
                window.grecaptcha
                    .execute(siteKey, { action })
                    .then((token: string) => {
                        resolve(token)
                    })
                    .catch((error: unknown) => {
                        reject(error)
                    })
            })
        })
    }

    return {
        loadScript,
        getRecaptchaToken,
        isScriptLoaded
    }
}
