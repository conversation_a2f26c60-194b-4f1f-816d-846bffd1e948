<?php

namespace App\Http\Controllers\api;

use PF;
use PT;
use Illuminate\Http\Request;

class utilsController extends Controller {
    private $data;


    /**
     *建構子.
     */
    public function __construct() {

        parent::__construct();
    }
    /**
     * @OA\Get(
     *     path="/api/utils/timestamp",operationId="",tags={"前台/系統參數"},summary="系統時間",description="",

     *   @OA\Response(response=200,description="回覆",
     *     @OA\JsonContent(type="object",
     *      @OA\Property(property="resultcode",type="integer",description="訊息代碼",),
     *      @OA\Property(property="resultmessage",type="string",description="訊息內容"),

     *      @OA\Property(property="data", type="object",
     *      allOf={

     *         @OA\Schema(@OA\Property(property="timestamp", type="string",description="timestamp", example="") ),

     *     })

     *     ,)
     *),)
     */

    public function timestamp(Request $request) {
        $this->jsondata['data']['timestamp'] = round(microtime(true) * 1000);
        return $this->apiResponse($this->jsondata);
    }
}
