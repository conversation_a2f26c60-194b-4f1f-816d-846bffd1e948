export default {
    routes: (_routes) => {
        const routes = [
            ..._routes,
            // {
            //     name: 'index',
            //     path: '',
            //     component: () => import('~/pages/index/index.vue'),
            //     //redirect: { name: '/' }
            // },
            //{
            //    name: 'index',
            //    path: '',
             //   //component: () => import('~/pages/index/index.vue'),
            //    redirect: { path: '/member/login' }
           // },
            {

                path: '/index',
                //component: () => import('~/pages/index/index.vue'),
                redirect: { path: '/' }
            },
            // {
            //     name: 'product',
            //     path: '/product',
            //     component: () => import('~/pages/product/[kind_id].vue'),
            // },
            {
                name: 'about',
                path: '/about',
                component: () => import('~/pages/about/[id].vue'),
            }

        ]

        // Now transform each route that has a component
        // so that `props:` merges route.params & route.query
        return routes.map((route) => {
            // If it's a redirect-only route (no component), just return it
            if (!route.component) {
                return route
            }

            // Otherwise, wrap the route with `props`:
            return {
                ...route,
                props: (to) => ({
                    ...to.params,
                    ...to.query,
                })
            }
        })
    }
}
