<?php

namespace Tests\Feature\api\admin;

class adminloginTest extends baseTest {


    public function setUp(): void {
        parent::setUp();

        $this->adminuser = \App\Models\adminuser::factory()->create([
            //'adminlogin_id' => $this->adminlogin->id,
        ]);
    }


    public function test_index() {

        $datas = [
            'header' =>
            array(
                'CONTENT_TYPE' => 'application/json',
            ),
            'url' => '/api/adminlogin',
            'raw' =>
            array(
                'account' => $this->adminuser->account,
                'password' => 'a123456789',

            ),
            'post' => NULL,

        ];

        $response = $this->withHeaders($datas['header'])
            ->json('POST', $datas['url'], $datas['raw']);
        // echo $response->getStatusCode();
        //echo "response" . $response->getContent();


        $this->checkJson($response);
        //檢查回傳資料是否存在

        $response
            ->assertStatus(200)
            ->assertJsonPath('data.name', $this->adminuser->name);
    }
}
