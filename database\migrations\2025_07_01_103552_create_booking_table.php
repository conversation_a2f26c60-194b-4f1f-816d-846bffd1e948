<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
//php artisan migrate:refresh --path=/database/migrations/_create_booking_table.php
return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        if (!Schema::hasTable('booking')) {
            Schema::create('booking', function (Blueprint $table) {
                //$table->engine = 'MyISAM';
                $table->increments('id')->from(10000)->comment('預約編號');
                $table->unsignedInteger('member_id')->comment('會員編號');
                $table->unsignedInteger('course_id')->comment('課程編號');
                $table->unsignedInteger('coach_id')->comment('教練編號');
                $table->string('booking_number', 20)->unique()->comment('預約單號');
                $table->dateTime('booking_date')->comment('預約日期時間');
                $table->dateTime('start_time')->comment('開始時間');
                $table->dateTime('end_time')->comment('結束時間');
                $table->tinyInteger('booking_status')->default(1)->comment('預約狀態'); // 1:已預約 2:已確認 3:進行中 4:已完成 5:已取消 9:退款
                $table->decimal('total_amount', 8, 2)->comment('總金額');
                $table->decimal('paid_amount', 8, 2)->default(0)->comment('已付金額');
                $table->tinyInteger('payment_status')->default(0)->comment('付款狀態'); // 0:未付款 1:已付款 2:部分付款 9:退款
                $table->string('payment_method', 20)->nullable()->comment('付款方式');
                $table->string('payment_transaction_id', 50)->nullable()->comment('付款交易號');
                $table->text('special_requests')->nullable()->comment('特殊需求');
                $table->text('cancellation_reason')->nullable()->comment('取消原因');
                $table->dateTime('cancelled_at')->nullable()->comment('取消時間');
                $table->timestamps();

                // 建立索引
                $table->index(['member_id']);
                $table->index(['course_id']);
                $table->index(['coach_id']);
                $table->index(['booking_status']);
                $table->index(['booking_date']);
                $table->index(['booking_number']);
            });
            \DB::statement("ALTER TABLE booking COMMENT 'XX'");
        }
        /*
        $table->unsignedBigInteger('activitysession_id')->comment('場次');
        $table->foreign('activitysession_id')->references('id')->on('activitysession')->onDelete('cascade');

        $table->string('kind',50)->index()->comment('種類');
        $table->mediumText('body')->nullable()->comment('說明');
        $table->dateTime('begindate')->nullable()->comment('開始時間');
        $table->integer('hits')->default(0)->comment('點率次數');
        $table->float('sortnum', 5, 3)->nullable()->comment('排序號碼');
        $table->integer('adminsuer_id')->nullable()->comment('編輯人員');
        $table->string('adminuser_name', 50)->nullable()->comment('編輯人員');
        $table->string('edit_account',50)->comment('修改人');
        $table->string('account',50)->unique();;
        $table->timestamps('reviewed_at')->default('now');
        $table->unique(array('kind', 'kindid'));
        $table->index(array('kind', 'kindid'));
        $table->softDeletes();

        */
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::dropIfExists('booking');
    }
};
