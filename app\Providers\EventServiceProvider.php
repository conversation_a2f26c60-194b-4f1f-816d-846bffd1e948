<?php

namespace App\Providers;

use App\Listeners\dblogListener;
use Illuminate\Support\Facades\Event;
use Illuminate\Auth\Events\Registered;
use Illuminate\Database\Events\QueryExecuted;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider {
    /**
     * The event listener mappings for the application.
     *
     * @var array
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
        QueryExecuted::class => [
            dblogListener::class,
        ],
        'App\Events\Event' => [
            'App\Listeners\EventListener',
        ],
        'Illuminate\Mail\Events\MessageSent' => [
            'App\Listeners\LogSentMessage',
        ],
        // 'Illuminate\Console\Events\ScheduledTaskStarting' => [
        //     'App\Listeners\LogScheduledTaskStarting',
        // ],
        'Illuminate\Queue\Events\JobProcessing' => [
            'App\Listeners\LogJobProcessing',
        ],

        // 'Illuminate\Console\Events\ScheduledTaskFinished' => [
        //     'App\Listeners\LogScheduledTaskFinished',
        // ],

        // 'Illuminate\Console\Events\ScheduledBackgroundTaskFinished' => [
        //     'App\Listeners\LogScheduledBackgroundTaskFinished',
        // ],

        // 'Illuminate\Console\Events\ScheduledTaskSkipped' => [
        //     'App\Listeners\LogScheduledTaskSkipped',
        // ],

        // 'Illuminate\Console\Events\ScheduledTaskFailed' => [
        //     'App\Listeners\LogScheduledTaskFailed',
        // ],
    ];

    /**
     * Register any events for your application.
     */
    public function boot() {
    }
}
