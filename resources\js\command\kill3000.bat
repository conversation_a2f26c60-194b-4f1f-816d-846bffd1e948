chcp 65001
@echo off
setlocal enabledelayedexpansion
set PORT=3000
set MAX_ATTEMPTS=3
set ATTEMPTS=0

:loop
if !ATTEMPTS! geq !MAX_ATTEMPTS! goto end

for /f "tokens=5" %%a in ('netstat -ano -n ^| findstr ":%PORT% "') do (
    if %%a gtr 0 (  :: 過濾 PID 0
        echo 正在終止佔用端口 %PORT% 的進程：PID %%a
        taskkill /PID %%a /F >nul 2>&1
    )
)

set /a ATTEMPTS+=1
timeout /t 1 /nobreak >nul 2>&1  :: 增加延遲避免瞬時重複檢測
goto loop

:end
echo 已完成 %MAX_ATTEMPTS% 次清理嘗試，端口 %PORT% 現應可用。
