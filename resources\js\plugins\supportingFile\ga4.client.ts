export default defineNuxtPlugin(nuxtApp => {
    const { GA4_ID } = useRuntimeConfig().public
    // if (process.env.NODE_ENV !== 'production') {
    //     provide('gtag', () => {})
    //     return
    // }

    // 加载 Google Analytics Script
    function addGtagScript() {
        const script = document.createElement('script')
        script.async = true
        script.src = `https://www.googletagmanager.com/gtag/js?id=${GA4_ID}`
        document.head.appendChild(script)
    }

    // 初始化 gtag
    function initGtag() {
        window.dataLayer = window.dataLayer || []
        window.gtag = function () {
            window.dataLayer.push(arguments)
        }
        window.gtag('js', new Date())
        window.gtag('config', GA4_ID)
    }

    // 添加脚本和初始化
    addGtagScript()
    initGtag()

    nuxtApp.hook('page:finish', () => {
        if (!window.location.pathname.startsWith('/admin')) {
            setTimeout(() => {
                window.gtag('event', 'page_view', {
                    page_path: window.location.pathname + window.location.search + window.location.hash,
                    page_title: document.title, // Upload the web title
                    page_location: window.location.href
                })
            }, 2000)
        }
    })
})
