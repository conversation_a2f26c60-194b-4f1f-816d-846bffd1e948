<?php

namespace App\Http\Controllers\api;

use PF;
use PT;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Validator;

class adminloginController extends Controller {

    private $data;

    public function __construct() {
        // 呼叫父類別建構子
        parent::__construct();

        // 將所有request資料導入到$data變數中
        $this->data = PF::requestAll($this->data);
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
        $this->data['displaynames'] = \App\Models\adminuser::getFieldTitleArray();
    }

    public function index(Request $request) {
        // 定義驗證規則
        $validators = [
            'account' => 'required',
            'password' => 'required',
        ];

        // 生產環境下新增 Google reCAPTCHA 驗證
        if (\config('app.env') == 'production') {
            $validators['google_recaptcha_token'] = ['required', 'string', new \App\Rules\MyValidatorsGoogleRecapchaV3()];
        }

        // 使用 Validator 進行請求數據的驗證
        $validator = Validator::make($request->all(), $validators);
        $validator->setAttributeNames($this->data['displaynames']);

        // 如果驗證失敗，拋出自定義例外
        if ($validator->fails()) {
            throw new \CustomException(implode(',', $validator->messages()->all()));
        }

        $account = $request->input('account');
        $user = \App\Models\adminuser::where('account', $account)->where('online', 1)->first();

        if (!$user || !\Hash::check($request->input('password'), $user->password)) {
            // 增加登錄失敗次數
            \DB::table('adminuser')->where('account', $account)->increment('failcount');
            $this->insert($account, __('失敗'));

            throw new \CustomException(__('登入失敗，連續失敗5次帳戶將會被鎖定') . ' (IP:' . request()->ip() . ')');
            //return response()->json(['message' => 'Unauthorized'], 401);
        }
        if ($user->failcount >= 5) {
            \Auth::guard('admin')->logout();
            throw new \CustomException('密碼錯誤太多次，請聯絡管理員解鎖帳戶');
        }
        // 後踢前機制：刪除舊的 Token
        //$user->tokens()->delete();
        // 產生 Token
        $token = $user->createToken('admin', ['*'], \Carbon::now()->addDay())->plainTextToken;
        // 設置過期時間為1天後
        // $user->tokens->last()->expires_at = \Carbon::now()->addDay();  // 1天後過期
        // $user->tokens->last()->save();

        $inputs = [
            'lastlogin_dt' => now(),
            'lastlogin_ip' => \Request::ip(),
            'failcount' => 0
        ];
        $user->update($inputs);

        // 返回登錄成功的響應數據
        $this->jsondata['resultmessage'] = '登入成功';
        $this->jsondata['data'] = [
            'api_token' => $token,
            'name' => $user->name,
            'role' => $user->role,
        ];

        // 記錄登錄日誌
        $this->insert($user->name, __('成功'));


        return $this->apiResponse($this->jsondata);
    }

    // 記錄登錄活動
    public function insert($account, $loginstatus) {
        if ($account) {
            \App\Models\adminuserloginlog::create([
                'account' => $account,
                'clientip' => request()->ip(),
                'created_at' => now(),
                'loginstatus' => $loginstatus,
            ]);
        }
    }
}
