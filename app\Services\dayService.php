<?php

namespace App\Services;

/**
 * Class JsonTranslator.
 */
class dayService {
    public function __construct() {
    }

    public function day() {
        //只保留30天
        \DB::delete("DELETE FROM adminuserloginlog WHERE convert(created_at,DATE)<convert((DATE_ADD(convert(now(),DATETIME),INTERVAL -30 DAY)),DATE) ");
        //\DB::update("update project set article_count = (select count(*) from article where project_id = project.id) ");
        //\DB::update("update project set article_update_count = (select count(*) from article where wordpress_datetime is not null and project_id = project.id) ");
        //         $sql = <<<EOF
        // DELETE rating_log
        // select *
        // FROM rating_log left join rating_product on (rating_product.id=rating_product_id)
        // where rating_product.id is null
        // EOF;
        //\DB::delete($sql);
    }
}
