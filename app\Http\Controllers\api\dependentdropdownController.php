<?php

namespace App\Http\Controllers\api;

use DB;
//use Illuminate\Support\Facades\DB;

use Illuminate\Http\Request;

class dependentdropdownController extends Controller {
    private $data;





    /**
     *建構子.
     */
    public function __construct() {
        //$this->limit="xx";
        parent::__construct();
    }
    public function city1(Request $request) {

        $rows = DB::table('city1')->selectRaw('city1title');

        $rows = $rows->orderByRaw('sortnum desc');
        $rows = $rows->get();

        $this->jsondata['data'] = $rows;

        return $this->apiResponse($this->jsondata);
    }

    public function city2(Request $request) {

        $rows = DB::table('city2')->selectRaw('concat(city2title,postal) as id,concat(city2title,postal) as name');

        $rows->myWhere('city1title|S', $request->input('city1'), "city1", 'N');

        $rows = $rows->orderByRaw('city2title');
        $rows = $rows->get();

        $this->jsondata['data'] = $rows;

        return $this->apiResponse($this->jsondata);
    }
    public function kindhead(Request $request) {

        $rows = DB::table('kindhead')->selectRaw('id,title');
        $rows = $rows->orderByRaw('sortnum');
        //\PF::dbSqlPrint($rows);
        $rows = $rows->get();

        $this->jsondata['data'] = $rows;

        return $this->apiResponse($this->jsondata);
    }
    public function kindmain(Request $request) {
        $datas['output'] = [];

        $rows = DB::table('kindmain')->selectRaw('id,title');

        $rows->myWhere('kindhead_id|N', $request->input('kindhead_id'), "kindhead_id", 'Y');

        $rows = $rows->orderByRaw('sortnum');
        //\PF::dbSqlPrint($rows);
        $rows = $rows->get();

        $this->jsondata['data'] = $rows;

        return $this->apiResponse($this->jsondata);
    }
    public function kind(Request $request) {
        $datas['output'] = [];

        $rows = DB::table('kind')->selectRaw('id,kindtitle ');

        $rows->myWhere('kind|S', $request->input('kind'), "kind", 'Y');

        $rows = $rows->orderByRaw('kindsortnum');
        //\PF::dbSqlPrint($rows);
        $rows = $rows->get();

        $this->jsondata['data'] = $rows;

        return response()->json($this->jsondata, 200, ['Content-Type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);
    }

    public function citys(Request $request) {

        $rows = \App\Models\city1::selectRaw('city1title,city1title as label,city1title as value')->with(['children' => function ($query) {
            $query->selectRaw('city1title,concat(city2title,postal) as value,concat(city2title,postal) as label');
        }]);
        //$rows = $rows->selectRaw('city1title as label,city1title as value');
        $rows = $rows->orderByRaw('sortnum desc');
        $rows = $rows->get();

        $this->jsondata['data'] = $rows;

        return $this->apiResponse($this->jsondata);
    }
}
