// middleware/admin.ts
export default defineNuxtRouteMiddleware(
    (to, _from) => {
        //const store = useDataStore()
        //console.clear();
        //console.log(['222', 222])
        console.log(['to', to.params, to.meta.props])
        to.meta.props = {
            ...to.params,
            ...to.query
        }
        console.log(['to.meta.props', to.meta.props])
        console.log(['to.meta.props2', to.meta.props])
        // console.log(['to12', to])
    },
    { override: true }
)
