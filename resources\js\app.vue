<template>
    <Html>
        <Head> </Head>
        <!-- 載入中的遮罩層 -->
        <div v-if="isLoading" class="loading-mask">
            <div class="loading-spinner">
                <div class="spinner"></div>
            </div>
        </div>

        <!-- 主要內容 -->
        <div v-show="!isLoading" class="main-content">
            <NuxtLoadingIndicator :throttle="0" />
            <NuxtLayout>
                <NuxtPage />
            </NuxtLayout>
        </div>
    </Html>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'

const isLoading = ref(true)

useHead({
    link: [
        {
            rel: 'stylesheet',
            href: '/css/loading.css'
        },
        {
            rel: 'stylesheet',
            href: 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css'
        },

        {
            rel: 'stylesheet',
            href: '/css/css.css'
        },

        {
            rel: 'stylesheet',
            href: 'https://pro.fontawesome.com/releases/v5.10.0/css/all.css'
        }
    ],
    script: [
        {
            src: 'https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4',
            crossorigin: 'anonymous', // Optional: Add crossorigin attribute
            defer: false,
            async: false
        },
        {
            src: 'https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js',
            crossorigin: 'anonymous', // Optional: Add crossorigin attribute
            defer: false,
            async: false
        },
        {
            src: 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js',
            crossorigin: 'anonymous', // Optional: Add crossorigin attribute
            defer: false,
            async: false
        }
    ]
})

const store = useDataStore()

await callOnce(async () => {
    await store.setConfigSetup()
    useSeoMeta({
        title: store.config?.title,
        keywords: store.config?.keyword,
        ogTitle: store.config?.title,
        description: store.config?.description,
        ogDescription: store.config?.description
        //ogImage: 'https://example.com/image.png',
        //twitterCard: 'summary_large_image',
    })
})

// 檢查CSS和JavaScript是否已載入完成
onMounted(() => {
    // 確保DOM已完全載入，然後顯示內容
    console.log('Loading started, isLoading:', isLoading.value)
    nextTick(() => {
        setTimeout(() => {
            console.log('Loading completed, hiding loading mask')
            isLoading.value = false
        }, 2000) // 延長到2秒，確保能看到loading效果
    })
})

//getConfig()

useHead({
    meta: [
        { name: 'viewport', content: 'width=device-width, initial-scale=1, shrink-to-fit=no' },
        { name: 'mobile-web-app-capable', content: 'yes' },
        { name: 'apple-mobile-web-app-status-bar-style', content: 'black' },
        { name: 'HandheldFriendly', content: 'True' },
        { 'http-equiv': 'imagetoolbar', content: 'no' },
        { 'http-equiv': 'X-UA-Compatible', content: 'IE=edge,chrome=1' }
    ]
})
</script>
<style scoped>
/* 關鍵loading樣式 - 必須內聯確保立即生效 */
.loading-mask {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    text-align: center;
    color: #666;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #ff5657;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.main-content {
    opacity: 1;
    transition: opacity 0.5s ease-in-out;
}

/* 基礎樣式 */
body {
    margin: 0; /* 等同於 leftmargin="0" topmargin="0" marginwidth="0" marginheight="0" */
}

/* 頁面切換動畫（保留註解供參考）
.page-enter-active,
.page-leave-active {
    transition: all 0.4s;
}
.page-enter-from,
.page-leave-to {
    opacity: 0;
    filter: blur(1rem);
} */
</style>
