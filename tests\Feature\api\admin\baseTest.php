<?php

namespace Tests\Feature\api\admin;





class baseTest extends \Tests\Feature\baseTest {

    public function setUp(): void {
        parent::setUp();

        $this->adminuser = \App\Models\adminuser::factory()->create([]);
        $token = $this->adminuser->createToken('Test Token')->plainTextToken;
        $this->adminuser->api_token = $token;
        $this->actingAs($this->adminuser, "admin");
        //$response = $this->get('/');
    }
}
