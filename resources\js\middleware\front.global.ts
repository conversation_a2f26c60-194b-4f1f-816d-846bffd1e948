// middleware/admin.ts

export default defineNuxtRouteMiddleware(
    (to, _from) => {
        const store = useDataStore()

        //console.clear();
        //console.log(['to', to])
        if (to.path.startsWith('/admin') == false && to.path.startsWith('/vue') == false) {
            // 手动设置布局
            to.meta.layout = 'front'
            //console.log(['to.meta.layout', to.meta.layout])
        }
        if (to.path == '/member/login') {
            if (!utils.isEmpty(store.member?.api_token)) {
                return navigateTo('/membercenter/home')
            }
        }
    },
    {
        override: true
    }
)
