<template>
    <my-breadcrumb :id="kind"></my-breadcrumb>
    <el-form ref="formEl" style="width: 98%" :model="inputs" @submit.prevent="onSubmit" v-loading="http.getLoading()">
        <div class="form-group row" v-for="(field, index) in fields">
            <label class="col-md-2"
                >{{ field.label
                }}<span class="text-danger p-1">
                    {{ field.rules && field.rules.length > 0 && field.rules[0].required ? '*' : '' }}</span
                ></label
            >
            <div class="col-md-10">
                <el-form-item :prop="field.name" :rules="field.rules">
                    <Suspense>
                        <template #default>
                            <div class="dialog-content">
                                <component :is="utils.getComponent(field.kind)" v-model="inputs[field.name]" v-bind="field.props">
                                </component>
                            </div>
                        </template>
                        <template #fallback>
                            <div class="dialog-loading">
                                <el-icon class="is-loading"><Loading /></el-icon>
                                <span>Loading...</span>
                            </div>
                        </template>
                    </Suspense>
                </el-form-item>
            </div>
        </div>

        <div align="center">
            <button type="submit" class="btn btn-primary">確定</button>
             
            <button type="reset" class="btn btn-secondary" @click="formEl.resetFields()">取消</button>
             
            <button type="button" class="btn btn-secondary" @click="emits('closed-dialog', false)">返回</button>
        </div>
    </el-form>
</template>

<script setup lang="ts">
import { ref, reactive, getCurrentInstance, onMounted } from 'vue'

const router = useRouter()
const route = useRoute()

const http = createHttp()

//const emit = defineEmits(["updateNavTitle"]);

const emits = defineEmits(['closed-dialog']) //接受外面送來的觸發的參數
const props = defineProps({
    edit: {
        default: ''
    },
    kind: {
        default: ''
    }
}) //接受外面送來的參數
const fields = ref([])
const formEl = ref(null)
// 使用 reactive 定義對象
const datas = ref([])
const inputs = reactive({
    title: '',
    memo: '',
    field1: '',
    begindate: '',
    closedate: '',
    kindsort: '',
    kind: props.kind
})
const templetedata = reactive({})
const getData = async () => {
    try {
        let rep = await http.post('api/admin/kind/show?id=' + props.edit, {})
        if (rep.resultcode == '0') {
            Object.assign(inputs, rep.data)
            //console.log(["inputs", inputs]);
        } else {
            throw new Error(rep.resultmessage)
        }
    } catch (error) {
        utils.formElError(error)
        console.error(error)
    } finally {
    }
}

const onSubmit = async () => {
    if (!formEl.value) return

    try {
        const valid = await formEl.value.validate()
        if (valid) {
            try {
                let rep = await http.post('api/admin/kind/store', inputs)

                //console.log(["rep",rep]);
                //debugger;
                if (rep.resultcode == '0') {
                    utils.toast(rep.resultmessage)
                    //router.go(-1);
                    emits('closed-dialog', true)
                } else {
                    throw new Error(rep.resultmessage)
                }
            } catch (error: any) {
                utils.message(error.message)
                console.error(error)
            }
        }
    } catch (error) {
        console.error('Validation error:', error)
    }
}

onMounted(async () => {
    importModule()

    if (props.edit != '') {
        getData()
    }
})
async function importModule() {
    const modules = import.meta.glob('@/datas/*.js')

    const configName = props.kind
    const path = `/datas/${route.query.kind}.js`

    if (modules[path]) {
        modules[path]()
            .then(module => {
                fields.value = module.fields._rawValue.filter(rs => rs.isedit == true)
            })
            .catch(error => {
                console.error(`Failed to load module at ${path}`, error)
            })
    } else {
        console.error(`Module ${path} does not exist`)
    }
}
</script>
