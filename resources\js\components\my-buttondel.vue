<template>
    <button type="button" class="btn btn-danger btn-sm m-2" @click="onDel">
        <i class="fa fa-trash" aria-hidden="true"></i>
    </button>
</template>

<script setup lang="ts">
import { ref, watch, defineEmits, onMounted } from 'vue'
import { utils } from '@/utils'
const emits = defineEmits(['click'])
const onDel = () => {
    utils
        .confirm('確定要刪除嗎?')
        .then(async result => {
            emits('click')
        })
        .catch(error => {})
}
</script>

<style scoped></style>
