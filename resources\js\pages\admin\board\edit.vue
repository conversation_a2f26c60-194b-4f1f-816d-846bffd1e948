<template>
    <my-breadcrumb :id="kind"></my-breadcrumb>
    <el-form ref="formEl" style="width: 98%" :model="inputs" @submit="onSubmit" v-loading="http.getLoading()" @submit.prevent="onSubmit">
        <div class="form-group row" v-for="(field, index) in fields" :key="index">
            <label class="col-md-2"
                >{{ field.label
                }}<span class="text-danger p-1">
                    {{ field.rules && field.rules.length > 0 && field.rules[0].required ? '*' : '' }}</span
                ></label
            >
            <div class="col-md-10">
                <el-form-item :prop="field.name" :rules="field.rules">
                    <Suspense>
                        <template #default>
                            <div class="dialog-content">
                                <component
                                    :is="utils.getComponent(field.kind)"
                                    v-model="inputs[field.name as keyof typeof inputs]"
                                    v-bind="field.props"
                                    v-if="field.kind"
                                >
                                </component>
                                <div v-else class="text-danger">組件 "{{ field.kind }}" 不存在，請檢查組件設定是否正確</div>
                            </div>
                        </template>
                        <template #fallback>
                            <div class="dialog-loading">
                                <el-icon class="is-loading"><Loading /></el-icon>
                                <span>Loading...</span>
                            </div>
                        </template>
                    </Suspense>
                </el-form-item>

                {{ field.memo }}
            </div>
        </div>

        <div align="center">
            <button type="submit" class="btn btn-primary">確定</button>
             
            <button type="reset" class="btn btn-secondary" @click="formEl?.resetFields()">取消</button>
             
            <button type="button" class="btn btn-secondary" @click="emits('closed-dialog', false)">返回</button>
        </div>
    </el-form>
</template>

<script setup lang="ts">
import type { FormInstance } from 'element-plus'
import { onMounted, reactive, ref } from 'vue'

const router = useRouter()

const http = createHttp() //http套件

//const emit = defineEmits(["updateNavTitle"]);

const emits = defineEmits(['closed-dialog']) //接受外面送來的觸發的參數
const props = defineProps({
    edit: {
        default: ''
    },
    kind: {
        default: ''
    }
}) //接受外面送來的參數

const fields = ref<field[]>([])
const formEl = ref<FormInstance>()

// 使用 reactive 定義對象
const datas = ref([])
const inputs = reactive({
    title: '',
    memo: '',
    field1: '',
    begindate: '',
    closedate: '',
    boardsort: '',
    kind: props.kind
})
const templetedata = reactive({})

const getData = async () => {
    try {
        let rep = (await http.post('api/admin/board/show?id=' + props.edit, {})) as ApiResponse
        if (rep.resultcode == '0') {
            Object.assign(inputs, rep.data)
            //console.log(["inputs", inputs]);
        } else {
            throw new Error(rep.resultmessage)
        }
    } catch (error) {
        utils.formElError(error)

        console.error(error)
    } finally {
    }
}

const onSubmit = async () => {
    if (!formEl.value) return

    try {
        const valid = await formEl.value.validate()
        if (valid) {
            let rep = (await http.post('api/admin/board/store', inputs)) as ApiResponse

            //console.log(["rep",rep]);
            //debugger;
            if (rep.resultcode == '0') {
                utils.toast(rep.resultmessage)
                //router.go(-1);
                emits('closed-dialog', true)
            } else {
                throw new Error(rep.resultmessage)
            }
        }
    } catch (error) {
        console.error('Validation error:', error)
        utils.formElError(error)
    }
}

onMounted(async () => {
    importModule()

    if (props.edit != '') {
        getData()
    }
})

async function importModule() {
    try {
        const modules = (import.meta as any).glob('@/datas/*.js') as Record<string, () => Promise<{ fields: { _rawValue: Field[] } }>>
        //console.clear();
        // console.log(['props', props])
        const configName = props.kind
        const path = `/datas/${configName}.js`
        //console.clear();
        // console.log(['path', path])

        if (modules[path]) {
            const module = await modules[path]()
            fields.value = module.fields._rawValue.filter((rs: field) => rs.isedit == true)
        } else {
            console.error(`Module ${path} does not exist`)
        }
    } catch (error: any) {
        console.error(`Failed to load module`, error)
    }
}
</script>
