<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;

/*swagger api document start*/

/**
 * @OA\Schema(
 *   schema="member",
 *      allOf={
 *         @OA\Schema( @OA\Property(property="id", type="integer", description="會員編號", example="10001")),
 *         @OA\Schema( @OA\Property(property="email", type="string", description="電子信箱", example="<EMAIL>")),
 *         @OA\Schema( @OA\Property(property="name", type="string", description="姓名", example="王小明")),
 *         @OA\Schema( @OA\Property(property="phone", type="string", description="手機號碼", example="0912345678")),
 *         @OA\Schema( @OA\Property(property="birth", type="string", description="生日", example="1990-01-01")),
 *         @OA\Schema( @OA\Property(property="gender", type="string", description="性別", example="先生")),
 *         @OA\Schema( @OA\Property(property="member_type", type="integer", description="會員類型", example="1")),
 *         @OA\Schema( @OA\Property(property="status", type="integer", description="會員狀態", example="1")),
 *         @OA\Schema( @OA\Property(property="bio", type="string", description="個人簡介", example="熱愛滑雪的初學者")),
 *         @OA\Schema( @OA\Property(property="avatar", type="string", description="頭像", example="avatar.jpg")),
 *         @OA\Schema( @OA\Property(property="wallet_balance", type="number", description="錢包餘額", example="1500.00")),
 *         @OA\Schema( @OA\Property(property="email_verified_at", type="string", description="信箱驗證時間", example="2024-01-01 10:00:00")),
 *         @OA\Schema( @OA\Property(property="created_at", type="string", description="建立時間", example="2024-01-01 10:00:00")),
 *         @OA\Schema( @OA\Property(property="updated_at", type="string", description="更新時間", example="2024-01-01 10:00:00")),
 *      }
 *)
 */
/*swagger api document end*/

class member extends Authenticatable {
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * 資料表名稱
     */
    protected $table = 'member';

    /**
     * 可批量賦值的屬性
     */
    protected $fillable = [
        'email',
        'password',
        'name',
        'phone',
        'birth',
        'gender',
        'member_type',
        'status',
        'bio',
        'avatar',
        'wallet_balance',
        'email_verified_at',
    ];

    /**
     * 隱藏的屬性
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * 屬性轉換
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'birth' => 'date',
        'wallet_balance' => 'decimal:2',
        'password' => 'hashed',
    ];

    /*Relations start*/
    /**
     * 與 coach 表的關聯 - 會員可能是教練
     */
    public function coach() {
        return $this->hasOne(coach::class, 'member_id', 'id');
    }

    /**
     * 與 booking 表的關聯 - 會員的預約記錄
     */
    public function bookings() {
        return $this->hasMany(booking::class, 'member_id', 'id');
    }

    /**
     * 與 review 表的關聯 - 會員的評價記錄
     */
    public function reviews() {
        return $this->hasMany(review::class, 'member_id', 'id');
    }
    /*Relations end*/

    /**
     * 模型事件
     */
    public static function boot() {
        parent::boot();

        static::creating(function ($model) {
            // 建立時的邏輯處理
        });

        static::updating(function ($model) {
            // 更新時的邏輯處理
        });

        static::deleted(function ($model) {
            /*Del Relations start*/
            // 刪除會員時，同時刪除相關資料
            if ($model->coach) {
                $model->coach->delete();
            }
            $model->bookings()->delete();
            $model->reviews()->delete();
            /*Del Relations end*/
        });
    }
}
