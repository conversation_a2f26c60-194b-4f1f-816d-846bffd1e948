<?php

namespace App\Http\Controllers\api;

use PF;
use PT;
use Illuminate\Http\Request;
use DB;

//use Illuminate\Support\Facades\DB;
/***
"功能名稱":"訊息公告",
"資料表":"board",
"備註":" ",
"建立時間":"2022-01-18 17:27:28",
 ***/
class newsController extends Controller {
    private $data;


    /**
     *建構子.
     */
    public function __construct() {
        //$this->limit="xx";
        parent::__construct();

        //將request全部導入到$this->data變數中
        $this->data = PF::requestAll($this->data);
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
    }

    /**
     * @OA\Post(
     *     path="/api/news",operationId="index",tags={"前台/訊息公告"},summary="列表",description="",
     *     @OA\RequestBody(required=true,
     *      @OA\JsonContent(
     *      allOf={

     *         @OA\Schema(@OA\Property(property="page",description="頁數",type="integer",example="1",)),
     *         @OA\Schema(@OA\Property(property="pagesize",description="筆數/頁",type="integer",example="10",)),

     *     })

     *   ,),

     *   @OA\Response(response=200,description="回覆",
     *     @OA\JsonContent(type="object",
     *      @OA\Property(property="resultcode",type="integer",description="訊息代碼",),
     *      @OA\Property(property="resultmessage",type="string",description="訊息內容"),
     *      @OA\Property(property="data", type="object",
     *          @OA\Property(property="current_page", type="integer",description="目前頁數", ),
     *          @OA\Property(property="total", type="integer",description="總頁數", ),

     *      @OA\Property(property="data",  type="array",
     *      @OA\Items(allOf={
     *         @OA\Schema(ref="#/components/schemas/news"),

     *     }))

     *      ),)
     * ),)
     */
    public function index(Request $request) {

        $rows = \DB::table('board')->selectRaw('id,title,field1 as img,body,created_at,begindate');

        $rows->whereRaw('convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE() and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()');
        $rows->myWhere('kind|S', 'news', 'kind', 'Y');
        //$rows->where('kindid', $request->input('kindid'));
        $rows->orderByRaw('boardsort desc');

        $pagesize = (is_numeric($request->input('pagesize')) ? $request->input('pagesize') : 10);
        $rows = $rows->paginate($pagesize);
        foreach ($rows as $key => $rs) {


            unset($rs->adminuser_name);
            unset($rs->updated_at);

            $ebody = PF::noHtml(str_replace("&nbsp;", "", $rs->body));
            $rs->body = PF::left($ebody, 50);


            //$rs->img = url('/') . '/images/banner/' . $rs->img;
        }

        $this->jsondata['data'] = $rows;

        return $this->apiResponse($this->jsondata);
    }
    /**
     * @OA\Post(
     *     path="/api/news/show",security={{"bearerAuth":{}}},operationId="",tags={"前台/訊息公告"},summary="單筆顯示",description="",
     *     @OA\RequestBody(required=true,
     *      @OA\JsonContent(
     *      allOf={

     *         @OA\Schema(@OA\Property(property="id",description="編號",type="integer",example="1",)),

     *     })
     *   ,),

     *   @OA\Response(response=200,description="回覆",
     *     @OA\JsonContent(type="object",
     *      @OA\Property(property="resultcode",type="integer",description="訊息代碼",),
     *      @OA\Property(property="resultmessage",type="string",description="訊息內容"),

     *      @OA\Property(property="data", type="object",
     *      allOf={
     *         @OA\Schema(ref="#/components/schemas/news"),
     *         @OA\Schema(type="object",@OA\Property(property="", type="string",description="系列", example="") ),

     *     })

     *     ,)
     *),)
     */


    public function show($request) {


        $rows = \App\Models\board::selectRaw('board.*');
        $rows->where('id', '=', $request->input('id'));
        $rs = $rows->firstOrFail();
        DB::table('board')->where(['id' => $request->input('id')])->increment('hits');
        // 取得上一筆資料的ID
        $prevId = \App\Models\board::where('id', '<', $request->input('id'))
            ->where('kind', '=', 'news')
            ->orderBy('id', 'desc')
            ->value('id');

        // 取得下一筆資料的ID
        $nextId = \App\Models\board::where('id', '>', $request->input('id'))
            ->where('kind', '=', 'news')
            ->orderBy('id', 'asc')
            ->value('id');

        // 將上下筆ID加入回傳資料
        $rs->prev_id = $prevId;
        $rs->next_id = $nextId;

        $this->jsondata['data'] = $rs;

        return $this->apiResponse($this->jsondata);
    }
}
