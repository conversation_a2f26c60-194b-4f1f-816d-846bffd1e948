<?php

namespace Database\Factories;

use App\Models\board;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * Board模型的Factory類
 */
class BoardFactory extends Factory
{
    /**
     * 定義模型的默認狀態
     *
     * @return array
     */
    protected $model = board::class;

    public function definition()
    {
        return [
            'title' => $this->faker->sentence(3),
            'kind' => 'banner',
            'memo' => $this->faker->url,
            'field1' => $this->faker->image('public/images/banner', 640, 480, null, false), // 圖片檔名
            'field9' => $this->faker->randomElement(['_blank', '_self']), // target
            'begindate' => now(),
            'closedate' => now()->addDays(rand(1, 30)),
            'boardsort' => $this->faker->randomFloat(3, 1, 100),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}