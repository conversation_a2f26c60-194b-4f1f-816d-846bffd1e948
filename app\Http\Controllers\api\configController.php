<?php

namespace App\Http\Controllers\api;

use PF;
use PT;
use Illuminate\Http\Request;

class configController extends Controller {
    private $data;


    /**
     *建構子.
     */
    public function __construct() {

        parent::__construct();
    }
    /**
     * @OA\Get(
     *     path="/api/config",
     *     operationId="",
     *     tags={"前台/系統參數"},
     *     summary="列表",
     *     description="取得系統參數設定",
     *     @OA\Response(
     *         response=200,
     *         description="回覆",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="resultcode", type="integer", description="訊息代碼", example=0),
     *             @OA\Property(property="resultmessage", type="string", description="訊息內容", example=""),
     *             @OA\Property(
     *                 property="data", 
     *                 type="object",
     *                 @OA\Property(
     *                     property="config", 
     *                     type="object",
     *                     description="系統設定值",
     *                     @OA\AdditionalProperties(type="string")
     *                 )
     *             )
     *         )
     *     )
     * )
     */

    public function index(Request $request) {

        //        $items = [];

        // $jsonxml = json_encode($this->data['xmldoc'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);




        // // foreach ($this->data['xmldoc']  as $key => $v) {
        // //     if ($key == "權限") {
        // //         $item1 = [];
        // //         foreach ($v->選單  as $key1 => $v1) {
        // //             foreach ($v1->KIND  as $key2 => $v2) {
        // //                 $item1[] = ['id' => strval($v2->傳回值), 'title' => $v1['主選單名稱'] . " / " . strval($v2->資料)];
        // //                 foreach ($v2->KIND  as $key3 => $v3) {
        // //                     $item1[] = ['id' => strval($v3->傳回值), 'title' => $v1['主選單名稱'] . " / " . strval($v2->資料) . " / " . strval($v3->資料)];
        // //                     foreach ($v3->KIND  as $key3 => $v4) {
        // //                         $item1[] = ['id' => strval($v4->傳回值), 'title' => $v1['主選單名稱'] . " / " . strval($v3->資料) . " / " . strval($v4->資料)];
        // //                     }
        // //                 }
        // //             }
        // //         }
        // //         $items[$key] = $item1;
        // //     } else {
        // //         $item1 = [];
        // //         $c = [];
        // //         $c['資料'] = "label";
        // //         $c['傳回值'] = "value";
        // //         foreach ($v  as $key1 => $v1) {
        // //             $item = [];
        // //             foreach ($v1 as $k2 => $v2) {

        // //                 //$item[$k2] = ['value' => strval($v1->傳回值), 'label' => strval($v1->資料)];
        // //                 $key2 = strval($k2);

        // //                 if ($c[strval($k2)] != "") {
        // //                     $key2 = $c[strval($k2)];
        // //                 }
        // //                 $item[$key2] = strval($v2);
        // //             };
        // //             $item1[] = $item;
        // //             //$item1[] = ['value' => strval($v1->傳回值), 'label' => strval($v1->資料)];
        // //         }
        // //         $items[$key] = $item1;
        // //     }
        // //     // \PF::printr([$key, $v]);
        // // }


        //$this->jsondata['data']['xml'] = $jsonxml;

        $allConfigValues = config('config');
        foreach ($allConfigValues as $k => $v) {
            $this->jsondata['data']['config'][$k] = $v;
        };

        foreach ($this->jsondata['data']['config'] as $k => $v) {
            if (\Str::contains($k, ['upload'])) {
                unset($this->jsondata['data']['config'][$k]);
            }
        };


        return $this->apiResponse($this->jsondata);
    }
    // public function xpath(Request $request) {
    //     $items = [];
    //     foreach ($this->data['xmldoc']->xpath($request->input('xpath'))  as $key => $v) {
    //         $item = [];
    //         $item[$v->傳回值] = strval($v->資料);
    //         $items[] = $item;
    //     }

    //     $this->jsondata['data'] = $items;

    //     return $this->apiResponse($this->jsondata);
    // }
}
