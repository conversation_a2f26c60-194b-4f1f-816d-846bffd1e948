<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * @OA\Schema(
 *   schema="board",
 *          @OA\Property(property="kind", type="string",description="種類", ),
 *          @OA\Property(property="kindid", type="string",description="次種類編號", ),
 *          @OA\Property(property="title", type="string",description="標題", ),
 *          @OA\Property(property="memo", type="string",description="備註", ),
 *          @OA\Property(property="body", type="string",description="本文", ),
 *          @OA\Property(property="begindate", type="string",description="開始時間", ),
 *          @OA\Property(property="closedate", type="string",description="結束時間", ),
 *          @OA\Property(property="created_at", type="string",description="建立時間", ),
 *          @OA\Property(property="updated_at", type="string",description="異動時間", ),
 *          @OA\Property(property="boardsort", type="string",description="排序號碼", ),
 *          @OA\Property(property="location", type="string",description="放置位置" ),
 *          @OA\Property(property="field1", type="string",description="圖案", ),
 *          @OA\Property(property="field2", type="string",description="其他2", ),
 *          @OA\Property(property="field3", type="string",description="其他3", ),
 *          @OA\Property(property="field4", type="string",description="其他4", ),
 *          @OA\Property(property="field5", type="string",description="其他5", ),
 *          @OA\Property(property="field6", type="string",description="其他6", ),
 *          @OA\Property(property="field7", type="string",description="其他7", ),
 *          @OA\Property(property="field8", type="string",description="其他8", ),
 *          @OA\Property(property="field9", type="string",description="其他9", ),
 *          @OA\Property(property="hits", type="integer",description="點閱次數", ),
 *          @OA\Property(property="userid", type="integer",description="操作者編號", ),
 *          @OA\Property(property="online", type="string",description="上下架", ),
 *          @OA\Property(property="useraccount", type="string",description="操作者帳號", ),
 *          @OA\Property(property="alg", type="string",description="語系", ),
 * )
 */
//訊息公告
class board extends baseModel {
    use HasFactory;
    public $tabletitle = '訊息公告';
    public $table = 'board';
    public $primaryKey = 'id';

    //欄位必填
    public $rules = [];
    public $fieldFiles = [
        'field1' => 'public/images/banner',
        'field1' => 'public/images/news',
    ];
    public $fieldInfo = [
'id'=>['title'=>'自動編號','type'=>'int(10) unsigned'],//
'kind'=>['title'=>'種類','type'=>'varchar(190)'],//
'kind_id'=>['title'=>'種類編號','type'=>'int(11)'],//
'title'=>['title'=>'標題','type'=>'varchar(500)'],//
'memo'=>['title'=>'描述','type'=>'varchar(500)'],//
'body'=>['title'=>'本文','type'=>'mediumtext'],//
'field1'=>['title'=>'其他欄位1','type'=>'varchar(190)'],//
'field2'=>['title'=>'其他欄位2','type'=>'varchar(190)'],//
'field3'=>['title'=>'其他欄位3','type'=>'varchar(190)'],//
'field4'=>['title'=>'其他欄位4','type'=>'varchar(190)'],//
'field5'=>['title'=>'其他欄位5','type'=>'varchar(190)'],//
'field6'=>['title'=>'其他欄位6','type'=>'varchar(190)'],//
'field7'=>['title'=>'其他欄位7','type'=>'varchar(190)'],//
'field8'=>['title'=>'其他欄位8','type'=>'varchar(190)'],//
'field9'=>['title'=>'其他欄位9','type'=>'varchar(190)'],//
'field10'=>['title'=>'其他欄位10','type'=>'varchar(190)'],//
'begindate'=>['title'=>'開始時間','type'=>'datetime'],//
'closedate'=>['title'=>'結束時間','type'=>'datetime'],//
'hits'=>['title'=>'點率次數','type'=>'int(11)'],//
'boardsort'=>['title'=>'排序號碼','type'=>'double(7,3)'],//
'location'=>['title'=>'位置','type'=>'varchar(50)'],//
'adminuser_id'=>['title'=>'編輯人員','type'=>'int(11)'],//
'adminuser_name'=>['title'=>'編輯人員','type'=>'varchar(50)'],//
'alg'=>['title'=>'語系','type'=>'varchar(5)'],//
'created_at'=>['title'=>'建立時間','type'=>'timestamp'],//
'updated_at'=>['title'=>'編輯時間','type'=>'timestamp'],//
];
    //日期欄位的儲存格式。'Y-m-d' or 'U' or ...
    //protected $dateFormat = 'Y-m-d';

    protected $fillable = ['kind','kind_id','title','memo','body','field1','field2','field3','field4','field5','field6','field7','field8','field9','field10','begindate','closedate','hits','boardsort','location','adminuser_id','adminuser_name','alg','created_at','updated_at'];
    protected $guarded = [];
    protected $dates = ['begindate','closedate','created_at','updated_at'];
    /*Relations start*/
    /*Relations end*/
    public static function boot() {
        parent::boot();

        static::creating(function ($model) {
            //$model->uuid = (string) Uuid::generate();
        });
        self::created(function ($model) {
            //$model->uuid = (string) Uuid::generate();
        });
        static::updating(function ($model) {

            // do some logging
        });
        self::updated(function ($model) {
        });
        static::deleting(function ($model) {
            parent::delFieldFile($model);
        });

        static::deleted(function ($model) {
            /*Del Relations start*/
            /*Del Relations end*/
        });
    }
}
