<?php

namespace App\Console;

use App\Services\notifyService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\File;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

/***
"功能名稱":"排程",
"資料表":"",
"備註":" ",
"建立時間":"2022-01-18 16:37:13",
 ***/
class Kernel extends ConsoleKernel {
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        //'App\Console\Commands\dayCommand',
    ];

    /**
     * Define the application's command schedule.
     *
     * @param \Illuminate\Console\Scheduling\Schedule $schedule
     */
    protected function schedule(Schedule $schedule) {

        //建立LOG檔案
        $schedule->call(function () {
            $logfile = storage_path('logs/laravel-' . date('Y-m-d') . '.log');
            \File::put($logfile, '');
            chmod($logfile, 0777);
        })->daily()->onSuccess(function () {
            \Log::notice('create logs successful.');
        })->onFailure(function (Stringable $output) {
            \Log::error('create logs failed.' . $output);
        })->name("create logs");


        $schedule->call(function () {
            try {
                $validCommands = array('route:clear', 'config:clear', 'cache:clear', 'view:clear', 'clear-compiled', 'config:cache');
                foreach ($validCommands as $cmd) {
                    try {
                        $this->call('' . $cmd . '');
                    } catch (\Exception $e) {
                        \Log::error($cmd . ' > ' . $e->getMessage());
                    }
                }
            } catch (\Exception $e) {
                \Log::error($e->getMessage());
            }
        })->dailyAt('04:00')->onSuccess(function () {
            \Log::notice('day:clear successful.');
        })->onFailure(function (Stringable $output) {
            \Log::error('day:clear failed.' . $output);
        })->name("clear");


        $schedule->call(function () {
            try {
                $dayService = app(\App\Services\dayService::class);
                $dayService->day();
            } catch (\Exception $e) {
                \Log::error($e->getMessage());
            }
        })->dailyAt('04:20')->onSuccess(function () {
            \Log::notice('dayService successful.');
        })->onFailure(function (Stringable $output) {
            \Log::error('dayService failed.' . $output);
        })->name("del garbag log");


        $schedule->command('sanctum:prune-expired --hours=24')->dailyAt('00:05')->name("刪除api到期token");
        //php.ini要支援 register_argc_argv=On 才可以在command加arg參數

        //備份資料庫
        $schedule->command('backup:database 5')->dailyAt('04:30')->name("資料庫備份");
        //$schedule->command('queue:work --stop-when-empty')->everyMinute()->name("queue:work");

        //$schedule->command('queue:restart')->everyTenMinutes()->appendOutputTo(storage_path('logs/laravel-' . date('Y-m-d') . '.log'))->name("queue:restart");

        // $schedule->command('queue:work')->appendOutputTo(storage_path('logs/laravel-' . date('Y-m-d') . '.log'))->name("queue:work");

        //$schedule->command('queue:work')->appendOutputTo(storage_path('logs/laravel-' . date('Y-m-d') . '.log'))->name("queue:work-1");
        //$schedule->command('queue:work')->appendOutputTo(storage_path('logs/laravel-' . date('Y-m-d') . '.log'))->name("queue:work-2");
        // $schedule->call(function () {
        //     try {
        //         $notifyService = new \App\Services\notifyService();
        //         $notifyService->execute();
        //     } catch (\Exception $e) {
        //         \Log::error($e->getMessage());
        //     }
        // })->dailyAt('08:30')->onSuccess(function () {
        //     \Log::notice('notifyService successful.');
        // })->onFailure(function (Stringable $output) {
        //     \Log::error('notifyService failed.'.$output);
        // })->name("資料庫備份")->emailOutputOnFailure($toMail)->withoutOverlapping();

        // ->cron('* * * * *');	在自訂Cron調度上運行任務
        // ->everyMinute();	每分鐘運行一次任務
        // ->everyTwoMinutes();	每兩分鐘運行一次任務
        // ->everyThreeMinutes();	每三分鐘運行一次任務
        // ->everyFourMinutes();	每四分鐘運行一次任務
        // ->everyFiveMinutes();	每五分鐘運行一次任務
        // ->everyTenMinutes();	每十分鐘運行一次任務
        // ->everyFifteenMinutes();	每十五分鐘運行一次任務
        // ->everyThirtyMinutes();	每三十分鐘運行一次任務
        // ->hourly();	每小時運行一次任務
        // ->hourlyAt(17);	每小時第十七分鐘運行一次任務
        // ->everyTwoHours();	每兩小時運行一次任務
        // ->everyThreeHours();	每三小時運行一次任務
        // ->everyFourHours();	每四小時運行一次任務
        // ->everySixHours();	每六小時運行一次任務
        // ->daily();	每天淩晨零點運行任務
        // ->dailyAt('13:00');	每天13:00運行任務
        // ->twiceDaily(1, 13);	每天1:00 & 13:00運行任務
        // ->weekly();	每週運行一次任務
        // ->weeklyOn(1, '8:00');	每週一上午8點運行一次任務
        // ->monthly();	每月運行一次任務
        // ->monthlyOn(4, '15:00');	每月4號15:00運行一次任務
        // ->quarterly();	每個季度運行一次
        // ->yearly();	每年運行一次
        // ->timezone('America/New_York');	設置時區
    }

    /**
     * Register the commands for the application.
     */
    protected function commands() {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
