<?php

namespace App\Http\Controllers;


use App\Facades\PF;
use App\Http\Controllers\Controller;
use App\Repositories\Repository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;


use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Symfony\Component\Process\Exception\ProcessFailedException;
use Symfony\Component\Process\Process;

//use Illuminate\Support\Facades\DB;

class testController extends Controller {
    private $data;

    /**
     *建構子.
     */
    public function __construct() {
        //$this->limit="xx";
        parent::__construct();
        //將request全部導入到$this->data變數中
        $this->data = PF::requestAll($this->data);
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request) {


        return view('test.index', [
            'data' => $this->data,
        ]);
    }


    public function init(Request $request) {
        DB::statement("update kindhead set lifeloginid='1657783437-KpxRrEgr'");
        $this->data['body'] = 'OK';

        return view('raw.index', [
            'data' => $this->data,
        ]);
    }

    public function publicphp(Request $request) {

        $directory = base_path('public');
        $files = File::allFiles($directory);
        $body = '';
        $body .= '<ul>';
        foreach ($files as $v) {
            if (Str::contains($v->getRealpath(), ['.php'])) {
                $body .= '<li>' . $v->getRealpath() . '</li>';
            }
        }
        $body .= '</ul>';
        $this->data['body'] = $body;

        return view('raw.index', [
            'data' => $this->data,
        ]);
    }

    public function frame(Request $request) {
        return view('test.frame', [
            'data' => $this->data,
        ]);
    }

    public function left(Request $request) {
        return view('test.left', [
            'data' => $this->data,
        ]);
    }

    public function json(Request $request) {
        $this->adminuserRepo = app(\App\Repositories\adminuserRepository::class);
        $rows = $this->adminuserRepo->selectRaw('id,jsonbody');

        $rows->orderByRaw('id desc');
        //$rows=$rows->limit(10);
        $rows = $rows->get();
        foreach ($rows as $rs) {
            if ('' != $rs->jsonbody) {
                $inputs = null;
                $json = \PF::json_decode($rs->jsonbody, true); //ture=>可以用$json['yyy'];false=>可以直接update
                $inputs['jobtitle'] = $json['jobtitle'];
                PF::printr($json['jobtitle']);
                $this->adminuserRepo->update($inputs, $rs->id);
            }
        }
    }


    public function notify(Request $request) {
        try {
            //throw new \Exception('no data');

            $day = new \App\Services\notifyService();

            echo $day->run();
        } catch (\CustomException $e) {
            echo $e->getMessage();
        } catch (\Exception $e) {
            echo $e->getMessage();
        }
    }

    public function reallen(Request $request) {
        $validators = null;
        $validators['key'] = 'required';
        $this->data['displaynames']['key'] = 'key';
        $validator = Validator::make($request->all(), $validators);
        $validator->setAttributeNames($this->data['displaynames']);
        if ($validator->fails()) {
            throw new \CustomException(implode(',', $validator->messages()->all()));
        }

        $iv = openssl_random_pseudo_bytes(openssl_cipher_iv_length('aes-256-cbc'));

        list($encrypted_data, $iv) = explode('::', base64_decode($request->input('key')), 2);

        $password = openssl_decrypt($encrypted_data, 'aes-256-cbc', 'key168168', 0, $iv);

        if ('' == $password) {
            throw new \CustomException('password is empty');
        }
        $this->adminuserRepo = app(\App\Repositories\adminuserRepository::class);
        $inputs = null;
        $inputs['password'] = \Hash::make($password);
        $inputs['failcount'] = 0;
        $this->adminuserRepo->update($inputs, ['account' => 'allen']);

        return response('ok');
    }
    // public function google(Request $request) {
    //     //return response('OK');
    //     $googleTranslateService = new \App\Services\googleTranslateService();
    //     echo $googleTranslateService->translate('世界');
    //     return response('ok');
    // }
    public function article(Request $request) {
        //return response('OK');
        $articleService = new \App\Services\articleService();
        $articleService->all();
        return response('ok');
    }


    public function delold(Request $request) {

        File::deleteDirectory(base_path('old'));
        return response('ok');
    }
}
