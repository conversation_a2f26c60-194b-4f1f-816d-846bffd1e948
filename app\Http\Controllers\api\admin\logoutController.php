<?php

namespace App\Http\Controllers\api\admin;

use Laravel\Sanctum\PersonalAccessToken;
use PF;
use PT;
use Illuminate\Http\Request;

class logoutController extends Controller {
    private $data;
    public $filename;


    /**
     *建構子.
     */
    public function __construct() {

        parent::__construct();
    }
    /**
     * @OA\Get(
     *     path="/api/admin/logout",operationId="",tags={"後台/管理人員"},summary="登出",description="",

     *   @OA\Response(response=200,description="回覆",
     *     @OA\JsonContent(type="object",
     *      @OA\Property(property="resultcode",type="integer",description="訊息代碼",),
     *      @OA\Property(property="resultmessage",type="string",description="訊息內容"),

     *      @OA\Property(property="data", type="object",
     *      allOf={


     *     })

     *     ,)
     *),)
     */

    public function index(Request $request) {
        $token = $request->input('token');
        if ('' == $token) {
            $token = $request->bearerToken();
        }
        // 使用 Sanctum Guard 來刪除當前的 token

        $user = PersonalAccessToken::findToken($token);

        $user->delete();
        //$request->user()->currentAccessToken()->delete();


        return $this->apiResponse($this->jsondata);
    }
}
