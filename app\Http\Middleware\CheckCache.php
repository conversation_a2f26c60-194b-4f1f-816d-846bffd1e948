<?php

namespace App\Http\Middleware;

use DB;
use PT;
use Closure;
use Illuminate\Support\Facades\Schema;
use PF;

class CheckCache {
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next) {
        //\Cache::flush();
     \Cache::rememberForever('setup', function () {
            $xmldoc = PF::xmlDoc('Setup.xml');
            $item = [];
            foreach ($xmldoc  as $key => $v) {

                foreach ($v->選單  as $key1 => $v1) {
                    foreach ($v1->KIND  as $key2 => $v2) {
                        $item[strval($v2->傳回值)] = strval($v2->資料);
                        foreach ($v2->KIND  as $key3 => $v3) {
                            $item[strval($v3->傳回值)] =  strval($v3->資料);
                            foreach ($v3->KIND  as $key3 => $v4) {
                                $item[strval($v4->傳回值)] = strval($v4->資料);
                            }
                        }
                    }
                }
            }


            return $item;
        });

        // \Cache::rememberForever('domain', function () {

        //     $rows = DB::table('domain')->selectRaw('*');
        //     $rows->whereRaw('end_date is null or convert(end_date,DATE) >convert(now(),DATE)');
        //     $rows = $rows->get();

        //     foreach ($rows  as $key => $rs) {
        //         $rs = \PF::jsonToRs($rs, $rs->jsonbody);

        //         $rs = \PF::jsonToRs($rs, $rs->jsonbodya);
        //         unset($rs->jsonbody);
        //         unset($rs->jsonbodya);
        //     }

        //     return $rows;
        // });


        // }
        return $next($request);
    }
}
