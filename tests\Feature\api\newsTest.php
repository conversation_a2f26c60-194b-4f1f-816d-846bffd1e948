<?php

namespace Tests\Feature\api;

use Tests\Feature\baseTest;
use App\Models\board;
use Illuminate\Foundation\Testing\RefreshDatabase;

class newsTest extends baseTest {
    use RefreshDatabase;

    /**
     * 測試初始化
     */
    public function setUp(): void {
        parent::setUp();

        // 建立測試用的公告資料
        $this->news = board::factory()->create([
            'kind' => 'news',
            'title' => '測試公告',
            'field1' => 'test.jpg',
            'begindate' => now()->subDay(),
            'closedate' => now()->addDay(),
            'boardsort' => 1
        ]);
    }

    /**
     * 測試取得訊息公告列表
     */
    public function test_取得訊息公告列表_index() {
        $response = $this->postJson('/api/news', [
            'page' => 1,
            'pagesize' => 10
        ]);

        $response->assertStatus(200);
        $this->checkJson($response);
        $response->assertJsonPath('data.current_page', 1);
        $response->assertJsonPath('data.data.0.title', '測試公告');
    }

    /**
     * 測試取得單筆訊息公告
     */
    public function test_取得單筆訊息公告_show() {
        $response = $this->postJson('/api/news/show', [
            'id' => $this->news->id
        ]);

        $response->assertStatus(200);
        $this->checkJson($response);
        $response->assertJsonPath('data.title', '測試公告');
    }
}
