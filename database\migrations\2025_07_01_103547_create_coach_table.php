<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
//php artisan migrate:refresh --path=/database/migrations/_create_coach_table.php
return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        if (!Schema::hasTable('coach')) {
            Schema::create('coach', function (Blueprint $table) {
                //$table->engine = 'MyISAM';
                $table->increments('id')->from(10000)->comment('教練編號');
                $table->unsignedInteger('member_id')->comment('會員編號');
                $table->string('license_number', 50)->nullable()->comment('證照號碼');
                $table->tinyInteger('certification_type')->comment('認證類型'); // 1:基礎滑雪 2:進階滑雪 3:單板滑雪 4:競技滑雪
                $table->tinyInteger('coach_status')->default(0)->comment('教練狀態'); // 0:未認證 1:認證中 2:已認證 3:暫停 9:停用
                $table->text('experience')->nullable()->comment('教學經驗');
                $table->text('speciality')->nullable()->comment('專長項目');
                $table->decimal('hourly_rate', 8, 2)->default(0)->comment('時薪');
                $table->tinyInteger('years_experience')->default(0)->comment('教學年資');
                $table->text('certification_documents')->nullable()->comment('認證文件');
                $table->date('certification_date')->nullable()->comment('認證日期');
                $table->date('certification_expiry')->nullable()->comment('認證到期日');
                $table->decimal('rating', 3, 2)->default(0)->comment('評分');
                $table->unsignedInteger('total_reviews')->default(0)->comment('評價總數');
                $table->timestamps();

                // 建立索引
                $table->index(['member_id']);
                $table->index(['certification_type']);
                $table->index(['coach_status']);
                $table->index(['rating']);
            });
            \DB::statement("ALTER TABLE coach COMMENT 'XX'");
        }
        /*
        $table->unsignedBigInteger('activitysession_id')->comment('場次');
        $table->foreign('activitysession_id')->references('id')->on('activitysession')->onDelete('cascade');

        $table->string('kind',50)->index()->comment('種類');
        $table->mediumText('body')->nullable()->comment('說明');
        $table->dateTime('begindate')->nullable()->comment('開始時間');
        $table->integer('hits')->default(0)->comment('點率次數');
        $table->float('sortnum', 5, 3)->nullable()->comment('排序號碼');
        $table->integer('adminsuer_id')->nullable()->comment('編輯人員');
        $table->string('adminuser_name', 50)->nullable()->comment('編輯人員');
        $table->string('edit_account',50)->comment('修改人');
        $table->string('account',50)->unique();;
        $table->timestamps('reviewed_at')->default('now');
        $table->unique(array('kind', 'kindid'));
        $table->index(array('kind', 'kindid'));
        $table->softDeletes();

        */
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::dropIfExists('coach');
    }
};
