<?php

namespace App\Http\Middleware;

use Illuminate\Support\Facades\Auth;
use PF;
use Closure;
use App\Models\member;
use <PERSON><PERSON>\Sanctum\PersonalAccessToken;

/***
"功能名稱":"laravel 中介層 - API管理人員",
"資料表":"adminuser",
"備註":"api/admin目錄中介層",
"建立時間":"2022-01-18 16:47:45",
 ***/
class ApiAuthMember extends baseMiddleware {
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure                 $next
     *
     * @return mixed
     */
    public function handle($request, Closure $next) {

        $token = $request->input('token');
        if ('' == $token) {
            $token = $request->bearerToken();
        }
        if (\config('app.env') == 'dev' && '' == $token) {
            \Auth::guard('member')->loginUsingId(10001);
        } else {
        // 驗證 token 是否有效
        $user = PersonalAccessToken::findToken($token);

        if (!$user || !$user->tokenable) { // 可以加上檢查是否為 admin
            abort(401);
        }
            \Auth::guard('member')->setUser($user->tokenable);  // 設定使用者
        }

        // 驗證成功，繼續處理請求


        //$request->merge(['domain_id' => $rs->id]);
        $request->merge(['middlewareurl' => url('/') . '/membercenter/api/']);
        $response = parent::handle($request, $next);


        return $response;
        //return $next($request);
    }
}
