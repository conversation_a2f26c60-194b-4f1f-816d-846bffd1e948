<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews -Indexes
    </IfModule>


    RewriteEngine On

    # Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

    # Redirect Trailing Slashes If Not A Folder...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [L,R=301]

    # Handle Front Controller...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]

     # 如果請求的文件存在 gzip 版本，返回 gzip 版本
    RewriteCond %{HTTP:Accept-Encoding} gzip
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME}.gz -f
    RewriteRule ^(.*)$ $1.gz [QSA,L]

    # 設置正確的 MIME 類型
    AddEncoding gzip .gz
    AddType text/javascript .gz
    AddType text/css .gz

    # BEGIN Expires-Headers

    ExpiresActive On
    AddType application/vnd.ms-fontobject .eot
    AddType application/x-font-ttf .ttf
    AddType application/x-font-opentype .otf
    AddType application/x-font-woff .woff
    AddType image/svg+xml .svg
    ExpiresByType application/vnd.ms-fontobject "access 1 year"
    ExpiresByType application/x-font-ttf "access 1 year"
    ExpiresByType application/x-font-opentype "access 1 year"
    ExpiresByType application/x-font-woff "access 1 year"
    ExpiresByType image/svg+xml "access 1 year"
    ExpiresByType text/html "access 1 hour"
    ExpiresByType text/css "access 14 days"
    ExpiresByType text/x-javascript "access 3 weeks"
    ExpiresByType application/javascript "access 1 month"
    ExpiresByType application/x-javascript "access 1 month"
    ExpiresByType image/gif "access 2 months"
    ExpiresByType image/png "access 2 months"
    ExpiresByType image/jpg "access 2 months"
    ExpiresByType image/jpeg "access 2 months"
    ExpiresByType image/gif "access 2 months"
    ExpiresByType application/pdf "access 1 year"
    ExpiresByType application/x-shockwave-flash "access 1 year"
    ExpiresByType image/x-icon "access 1 year"
    ExpiresDefault "access 2 days"
</IfModule>
# END Expires-Headers


# BEGIN Cache-Control-Headers
<ifmodule mod_headers.c>
    #Header set Access-Control-Allow-Origin "*"
    # Header set Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept"
    # Header set Access-Control-Allow-Methods "GET, POST, OPTIONS"
    # Header set Cache-Control "no-cache, no-store, must-revalidate"
    # Header set Pragma "no-cache"
    # Header set Expires 0

    <filesmatch "(gif|ico|jpeg|jpe|jpg|svg|png|css)$">
        Header set Cache-Control "max-age=604800, must-revalidate"
    </filesmatch>
</ifmodule>
<IfModule mod_deflate.c>
  # Compress HTML, CSS, JavaScript, Text, XML and fonts
  AddOutputFilterByType DEFLATE application/javascript
  AddOutputFilterByType DEFLATE application/rss+xml
  AddOutputFilterByType DEFLATE application/vnd.ms-fontobject
  AddOutputFilterByType DEFLATE application/x-font
  AddOutputFilterByType DEFLATE application/x-font-opentype
  AddOutputFilterByType DEFLATE application/x-font-otf
  AddOutputFilterByType DEFLATE application/x-font-truetype
  AddOutputFilterByType DEFLATE application/x-font-ttf
  AddOutputFilterByType DEFLATE application/x-javascript
  AddOutputFilterByType DEFLATE application/xhtml+xml
  AddOutputFilterByType DEFLATE application/xml
  AddOutputFilterByType DEFLATE font/opentype
  AddOutputFilterByType DEFLATE font/otf
  AddOutputFilterByType DEFLATE font/ttf
  AddOutputFilterByType DEFLATE image/svg+xml
  AddOutputFilterByType DEFLATE image/x-icon
  AddOutputFilterByType DEFLATE text/css
  AddOutputFilterByType DEFLATE text/html
  AddOutputFilterByType DEFLATE text/javascript
  AddOutputFilterByType DEFLATE text/plain
  AddOutputFilterByType DEFLATE text/xml

  # Remove browser bugs (only needed for really old browsers)
  BrowserMatch ^Mozilla/4 gzip-only-text/html
  BrowserMatch ^Mozilla/4\.0[678] no-gzip
  BrowserMatch \bMSIE !no-gzip !gzip-only-text/html
  Header append Vary User-Agent
</IfModule>
# END Cache-Control-Headers