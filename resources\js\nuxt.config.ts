// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
    // 設置兼容性日期，確保 Nuxt 使用指定的功能版本
    compatibilityDate: '2024-11-01',

    // 全局啟用服務端渲染 (SSR)，提高 SEO 與首屏加載性能
    ssr: false, // 确保 SSR 已启用

    // 路由規則：針對特定路由控制渲染模式
    routeRules: {
        '/admin/**': { ssr: false }, // 管理後台路由使用客戶端渲染 (SPA)，減少服務器負擔
        '/membercenter/**': { ssr: false }, // 會員中心路由使用客戶端渲染
        '/**': { ssr: true } // 其他路由默認啟用 SSR
    },

    // TypeScript 配置，啟用嚴格模式以提高代碼質量
    typescript: {
        strict: true
    },

    // 應用全局配置
    app: {
        // 禁用頁面轉場動畫以提高性能（可根據需求啟用）
        pageTransition: false,
        // pageTransition: { name: 'page', mode: 'out-in' }, // 示例：啟用頁面轉場動畫

        // 配置 HTML head 標籤
        head: {
            meta: [
                // { name: 'viewport', content: 'width=device-width, initial-scale=1' }, // 響應式設計
                // { name: 'apple-mobile-web-app-status-bar-style', content: 'black-translucent' } // iOS 狀態欄樣式
            ]
            // 可添加其他 meta 標籤，如 description、keywords 等
        }
    },

    // 自動掃描並註冊全局組件
    components: {
        global: true,
        dirs: ['~/components'] // 指定組件目錄
    },

    // 禁用 Vue DevTools（生產環境推薦禁用以減少開銷）
    devtools: { enabled: false },

    // 使用的 Nuxt 模塊
    modules: [
        '@pinia/nuxt', // 狀態管理
        '@element-plus/nuxt', // Element Plus UI 組件庫
        '@vueuse/nuxt', // VueUse 工具函數
        '@nuxt/image' // 圖片優化
    ],

    // Element Plus 配置
    elementPlus: {
        importStyle: 'css' // 使用預編譯的 CSS 樣式
        // 可選：啟用暗色主題
        // themes: ['dark'],
    },

    // 運行時配置，將環境變量暴露給客戶端
    runtimeConfig: {
        // 公共配置（可以在客戶端和伺服器端訪問）
        public: {
            ...Object.keys(process.env).reduce((acc, key) => {
                // 只添加以 "NUXT_PUBLIC_" 开头的环境变量
                if (key.startsWith('NUXT_PUBLIC_')) {
                    // 去掉前缀并添加到 public
                    acc[key.replace('NUXT_PUBLIC_', '')] = process.env[key]
                }
                //console.log(['acc', acc])
                return acc
            }, {} as Record<string, any>)
        }
    },

    // Vite 配置，優化開發與構建過程
    vite: {
        // 將環境變量注入 Vite
        define: {
            'process.env': Object.keys(process.env).reduce((acc, key) => {
                if (key.startsWith('NUXT_PUBLIC_')) {
                    // 移除 NUXT_PUBLIC_ 前綴
                    const newKey = key.replace('NUXT_PUBLIC_', '')
                    // 確保 process.env[key] 不為 undefined
                    acc[newKey] = process.env[key] || ''
                }
                return acc
            }, {} as Record<string, string>)
        },
        css: {
            preprocessorOptions: {
                scss: {
                    api: 'modern' // 使用現代 SCSS API
                }
            }
        },
        server: {
            hmr: {
                clientPort: 3000 // HMR 客戶端端口
            },
            watch: {
                usePolling: true, // 啟用輪詢，適用於某些文件系統
                interval: 100
            },
            fs: {
                allow: ['C:/AppServ/nuxt/node_modules'] // 允許訪問 node_modules
            }
        },
        build: {
            cssCodeSplit: true, // 將 CSS 提取到獨立文件中
            rollupOptions: {
                output: {
                    // 優化 chunk 分割，減少文件數量
                    manualChunks(id) {
                        if (id.includes('node_modules')) {
                            return 'vendor'
                        }
                    }
                }
            }
        },
        optimizeDeps: {
            include: ['vue', '@element-plus/icons-vue'] // 預編譯依賴
        }
    },

    // 全局 CSS 文件
    css: ['element-plus/dist/index.css'],

    // 路由配置
    router: {
        middleware: ['admin', 'front'], // 全局中間件
        options: {
            strict: true, // 嚴格模式，確保路由行為一致
            trailingSlash: false // 禁用尾部斜杠
        }
    },

    // 自定義 hooks
    hooks: {
        // 示例：動態添加錯誤頁面
        // 'pages:extend'(pages) {
        //   pages.push({
        //     name: 'error',
        //     path: '/error',
        //     file: '~/error.vue',
        //     props: {
        //       error: {
        //         statusCode: '500',
        //         statusMessage: 'Server Error',
        //       },
        //     },
        //   });
        // },
    },

    // 開發服務器配置
    devServer: {
        host: '0.0.0.0', // 允許外部訪問
        port: 3000,
        https: {
            cert: 'C:\\xampp82\\apache\\crt\\allennb.com.tw\\server.crt',
            key: 'C:\\xampp82\\apache\\crt\\allennb.com.tw\\server.key'
        }
    },

    // 自動導入目錄
    imports: {
        dirs: ['stores', 'utils', 'composables', 'types'],
        global: true,
        autoImport: true
    },

    // 禁用內聯樣式，提升性能
    features: {
        inlineStyles: false
    },

    // 構建配置
    build: {
        extractCSS: true // 提取 CSS 到獨立文件
    },

    // 啟用現代模式（僅支持現代瀏覽器）
    modern: 'client',

    // Nitro 服務器配置
    nitro: {
        output: {
            dir: 'C:/Users/<USER>/Downloads/.output' // 自定義輸出目錄
        },
        compressPublicAssets: false // 壓縮公共資源
    }
})
