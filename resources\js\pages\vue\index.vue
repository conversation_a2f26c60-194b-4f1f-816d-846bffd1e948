<template>
    <div>{{ utils.url }}</div>
</template>

<script setup lang="ts">
definePageMeta({
    layout: 'raw'
})
import { createHttp } from '@/utils/http'
const http = createHttp() //http套件
import {
    ref,
    reactive,
    getCurrentInstance,
    onMounted,
    computed,
    onBeforeMount,
    onServerPrefetch
} from 'vue'

import { useDataStore } from '@/stores'
const store = useDataStore()
import { utils } from '@/utils'
const inputs = reactive({
    //不用加.value,可以覆蓋資料
    name: ''
})
const state = reactive({
    data: ''
})

const getData = async (): Promise<void> => {}

onBeforeMount(async () => {})
onMounted(async () => {})
</script>
