<template>
    <div class="homepage">
        <!-- 英雄區塊 -->
        <section class="relative min-h-screen bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 text-white overflow-hidden">
            <!-- 背景圖片 -->
            <div class="absolute inset-0 bg-black opacity-40"></div>
            <div
                class="absolute inset-0 bg-cover bg-center bg-no-repeat"
                style="
                    background-image: url('https://images.unsplash.com/photo-1551524164-687a55dd1126?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80');
                "
            ></div>

            <!-- 內容區 -->
            <div class="relative z-10 container mx-auto px-4 h-screen flex items-center">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center w-full">
                    <!-- 文字內容 -->
                    <div class="text-center lg:text-left">
                        <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight">專業滑雪教學平台</h1>
                        <p class="text-lg md:text-xl lg:text-2xl mb-8 text-gray-200 leading-relaxed">
                            找到最適合的滑雪教練，體驗最專業的滑雪課程
                        </p>
                        <div class="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                            <button
                                @click="router.push('/courses')"
                                class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-4 px-8 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg"
                                aria-label="立即預約課程"
                            >
                                <i class="fas fa-ski mr-2" aria-hidden="true"></i>
                                立即預約課程
                            </button>
                            <button
                                @click="router.push('/coaches')"
                                class="bg-transparent border-2 border-white text-white hover:bg-white hover:text-blue-600 font-semibold py-4 px-8 rounded-full transition-all duration-300"
                                aria-label="瀏覽教練"
                            >
                                <i class="fas fa-users mr-2" aria-hidden="true"></i>
                                瀏覽教練
                            </button>
                        </div>
                    </div>

                    <!-- 圖片區 -->
                    <div class="hidden lg:block">
                        <img
                            src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
                            alt="專業滑雪教學示範"
                            class="rounded-2xl shadow-2xl w-full h-auto"
                        />
                    </div>
                </div>
            </div>
        </section>

        <!-- 功能特色區塊 -->
        <section class="py-16 md:py-24 bg-gray-50">
            <div class="container mx-auto px-4">
                <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-center text-gray-800 mb-16">為什麼選擇我們</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                    <!-- 特色卡片 1 -->
                    <div
                        class="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 text-center"
                    >
                        <div class="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-6">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                class="h-8 w-8 text-white"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="2"
                                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                                />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-800 mb-4">專業教練</h3>
                        <p class="text-gray-600 leading-relaxed">所有教練均經過嚴格認證，擁有豐富的教學經驗</p>
                    </div>

                    <!-- 特色卡片 2 -->
                    <div
                        class="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 text-center"
                    >
                        <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                class="h-8 w-8 text-white"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="2"
                                    d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"
                                />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-800 mb-4">多元課程</h3>
                        <p class="text-gray-600 leading-relaxed">從初學者到專業級，提供各種難度的滑雪課程</p>
                    </div>

                    <!-- 特色卡片 3 -->
                    <div
                        class="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 text-center"
                    >
                        <div class="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-6">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                class="h-8 w-8 text-white"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="2"
                                    d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                                />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-800 mb-4">安全保障</h3>
                        <p class="text-gray-600 leading-relaxed">完善的安全措施與保險保障，讓您安心學習</p>
                    </div>

                    <!-- 特色卡片 4 -->
                    <div
                        class="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 text-center"
                    >
                        <div class="w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-6">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                class="h-8 w-8 text-white"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="2"
                                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                                />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-800 mb-4">靈活預約</h3>
                        <p class="text-gray-600 leading-relaxed">24小時線上預約系統，隨時預約最適合的時間</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 推薦課程區塊 -->
        <section class="py-16 md:py-24 bg-white">
            <div class="container mx-auto px-4">
                <div class="flex flex-col md:flex-row justify-between items-center mb-12">
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-800 mb-6 md:mb-0">推薦課程</h2>
                    <button
                        @click="router.push('/courses')"
                        class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-6 rounded-full transition-all duration-300"
                        aria-label="查看更多課程"
                    >
                        查看更多
                    </button>
                </div>

                <div v-if="coursesLoading" class="flex justify-center items-center py-16">
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
                </div>

                <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <div
                        v-for="course in featuredCourses"
                        :key="course.id"
                        class="bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 cursor-pointer"
                        @click="router.push(`/courses/${course.id}`)"
                        :aria-label="`查看課程: ${course.title}`"
                    >
                        <!-- 課程圖片 -->
                        <div class="relative h-48 overflow-hidden">
                            <img
                                :src="
                                    course.images ||
                                    'https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
                                "
                                :alt="`${course.title}課程圖片`"
                                class="w-full h-full object-cover transition-transform duration-300 hover:scale-110"
                            />
                            <div class="absolute top-4 right-4 bg-blue-500 text-white px-3 py-1 rounded-full font-semibold">
                                ${{ course.price }}
                            </div>
                        </div>

                        <!-- 課程內容 -->
                        <div class="p-6">
                            <h3 class="text-xl font-semibold text-gray-800 mb-3">{{ course.title }}</h3>
                            <p class="text-gray-600 mb-4 leading-relaxed">{{ course.description }}</p>

                            <div class="flex justify-between items-center">
                                <!-- 評分 -->
                                <div class="flex items-center">
                                    <div class="flex text-yellow-400 mr-2">
                                        <span
                                            v-for="n in 5"
                                            :key="n"
                                            :class="n <= Math.floor(course.rating) ? 'text-yellow-400' : 'text-gray-300'"
                                        >
                                            ★
                                        </span>
                                    </div>
                                    <span class="text-sm text-gray-600">{{ course.rating }}</span>
                                </div>

                                <!-- 教練資訊 -->
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        class="h-4 w-4 mr-1"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                                        />
                                    </svg>
                                    {{ course.coach?.member?.name }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 優秀教練區塊 -->
        <section class="py-16 md:py-24 bg-gray-50">
            <div class="container mx-auto px-4">
                <div class="flex flex-col md:flex-row justify-between items-center mb-12">
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-800 mb-6 md:mb-0">優秀教練</h2>
                    <button
                        @click="router.push('/coaches')"
                        class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-6 rounded-full transition-all duration-300"
                        aria-label="查看更多教練"
                    >
                        查看更多
                    </button>
                </div>

                <div v-if="coachesLoading" class="flex justify-center items-center py-16">
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
                </div>

                <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                    <div
                        v-for="coach in featuredCoaches"
                        :key="coach.id"
                        class="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 cursor-pointer text-center"
                        @click="router.push(`/coaches/${coach.id}`)"
                        :aria-label="`查看教練: ${coach.member?.name}`"
                    >
                        <!-- 教練頭像 -->
                        <div class="w-20 h-20 mx-auto mb-4 rounded-full overflow-hidden">
                            <img
                                :src="
                                    coach.member?.avatar ||
                                    'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
                                "
                                :alt="`${coach.member?.name}教練照片`"
                                class="w-full h-full object-cover"
                            />
                        </div>

                        <!-- 教練資訊 -->
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ coach.member?.name }}</h3>
                        <p class="text-gray-600 mb-3">{{ coach.years_experience }}年教學經驗</p>

                        <!-- 評分 -->
                        <div class="flex justify-center items-center mb-3">
                            <div class="flex text-yellow-400 mr-2">
                                <span v-for="n in 5" :key="n" :class="n <= Math.floor(coach.rating) ? 'text-yellow-400' : 'text-gray-300'">
                                    ★
                                </span>
                            </div>
                            <span class="text-sm text-gray-600">{{ coach.rating }}</span>
                        </div>

                        <!-- 專長標籤 -->
                        <div class="mt-4">
                            <span class="inline-block bg-blue-100 text-blue-800 text-xs px-3 py-1 rounded-full">
                                {{ coach.speciality }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 統計數據區塊 -->
        <section class="py-16 md:py-24 bg-gray-800 text-white">
            <div class="container mx-auto px-4">
                <div class="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
                    <div>
                        <div class="text-4xl md:text-5xl lg:text-6xl font-bold text-blue-400 mb-2">500+</div>
                        <div class="text-lg text-gray-300">專業教練</div>
                    </div>
                    <div>
                        <div class="text-4xl md:text-5xl lg:text-6xl font-bold text-green-400 mb-2">1000+</div>
                        <div class="text-lg text-gray-300">滿意學員</div>
                    </div>
                    <div>
                        <div class="text-4xl md:text-5xl lg:text-6xl font-bold text-purple-400 mb-2">50+</div>
                        <div class="text-lg text-gray-300">滑雪場地</div>
                    </div>
                    <div>
                        <div class="text-4xl md:text-5xl lg:text-6xl font-bold text-yellow-400 mb-2">5★</div>
                        <div class="text-lg text-gray-300">用戶評價</div>
                    </div>
                </div>
            </div>
        </section>
    </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'

// 定義布局
definePageMeta({
    layout: 'front'
})

const router = useRouter()

// 響應式數據 - 定義型別
interface Course {
    id: number
    title: string
    description: string
    price: number
    rating: number
    images?: string
    coach?: {
        member?: {
            name: string
        }
    }
}

interface Coach {
    id: number
    years_experience: number
    rating: number
    speciality: string
    member?: {
        name: string
        avatar?: string
    }
}

const featuredCourses = ref<Course[]>([])
const featuredCoaches = ref<Coach[]>([])
const coursesLoading = ref(false)
const coachesLoading = ref(false)

// 獲取推薦課程 - 使用模擬資料
const fetchFeaturedCourses = async () => {
    try {
        coursesLoading.value = true
        // 模擬 API 延遲
        await new Promise(resolve => setTimeout(resolve, 500))

        featuredCourses.value = [
            {
                id: 1,
                title: '初學者滑雪基礎課程',
                description: '適合完全沒有滑雪經驗的學員，從基礎開始學習安全滑雪技巧',
                price: 1500,
                rating: 4.8,
                images: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
                coach: {
                    member: {
                        name: '張志明教練'
                    }
                }
            },
            {
                id: 2,
                title: '進階滑雪技巧提升',
                description: '提升滑雪技巧，學習轉彎、剎車等進階動作，挑戰更高難度的滑道',
                price: 2000,
                rating: 4.9,
                images: 'https://images.unsplash.com/photo-1551524164-687a55dd1126?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
                coach: {
                    member: {
                        name: '李美玲教練'
                    }
                }
            },
            {
                id: 3,
                title: '單板滑雪專業訓練',
                description: '單板滑雪專業技巧訓練課程，適合想要挑戰單板滑雪的學員',
                price: 2500,
                rating: 4.7,
                images: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
                coach: {
                    member: {
                        name: '王大明教練'
                    }
                }
            }
        ]
    } catch (error) {
        console.error('獲取推薦課程失敗:', error)
    } finally {
        coursesLoading.value = false
    }
}

// 獲取優秀教練 - 使用模擬資料
const fetchFeaturedCoaches = async () => {
    try {
        coachesLoading.value = true
        // 模擬 API 延遲
        await new Promise(resolve => setTimeout(resolve, 600))

        featuredCoaches.value = [
            {
                id: 1,
                years_experience: 8,
                rating: 4.9,
                speciality: '初學者教學',
                member: {
                    name: '張志明',
                    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
                }
            },
            {
                id: 2,
                years_experience: 12,
                rating: 4.8,
                speciality: '進階技巧',
                member: {
                    name: '李美玲',
                    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
                }
            },
            {
                id: 3,
                years_experience: 10,
                rating: 4.9,
                speciality: '單板滑雪',
                member: {
                    name: '王大明',
                    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
                }
            },
            {
                id: 4,
                years_experience: 15,
                rating: 5.0,
                speciality: '競技訓練',
                member: {
                    name: '陳小華',
                    avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
                }
            }
        ]
    } catch (error) {
        console.error('獲取優秀教練失敗:', error)
    } finally {
        coachesLoading.value = false
    }
}

// 組件掛載時獲取數據
onMounted(() => {
    fetchFeaturedCourses()
    fetchFeaturedCoaches()
})

// 設置頁面 SEO - 符合新規範的 SEO 要求
useHead({
    title: '滑雪平台 - 專業滑雪教學平台 | 找到最適合的滑雪教練',
    meta: [
        {
            name: 'description',
            content: '專業的滑雪教學平台，提供優質的滑雪課程與教練服務。500+專業教練，1000+滿意學員，完全響應式設計，24小時線上預約系統。'
        },
        {
            name: 'keywords',
            content: '滑雪,滑雪教學,滑雪課程,滑雪教練,滑雪平台,專業滑雪,滑雪訓練,線上預約'
        },
        {
            property: 'og:title',
            content: '滑雪平台 - 專業滑雪教學平台'
        },
        {
            property: 'og:description',
            content: '找到最適合的滑雪教練，體驗最專業的滑雪課程。500+專業教練為您提供安全、有趣的滑雪學習體驗。'
        },
        {
            property: 'og:image',
            content: 'https://images.unsplash.com/photo-1551524164-687a55dd1126?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80'
        },
        {
            property: 'og:type',
            content: 'website'
        },
        {
            name: 'robots',
            content: 'index, follow'
        }
    ],
    link: [
        {
            rel: 'canonical',
            href: 'https://ski-platform.com'
        }
    ]
})
</script>
