<?php

namespace App\Http\Controllers\api;

use Illuminate\Http\Request;

class Controller extends Controller {
    private $data;


    public function __construct() {
        //$this->limit="xx";
        parent::__construct();
    }
    /**
     * @OA\GET(
     *     path="/api/captcha",operationId="",tags={"前台/驗證碼"},summary="產生",description="",
     *   @OA\Response(response=200,description="回覆",
     *     @OA\JsonContent(type="object",
     *      @OA\Property(property="resultcode",type="integer",description="訊息代碼",),
     *      @OA\Property(property="resultmessage",type="string",description="訊息內容"),
     *      @OA\Property(property="data", type="object",
     *      @OA\Property(property="url", type="object",
     *         @OA\Property(property="key",description="captcha要送到檢核頁key",type="string",example="",),
     *         @OA\Property(property="img",description="base64d驗證碼圖",type="string",example="",),
     *     ),
     *     ),)
     *),)
     */

    public function index(Request $request) {
        $this->jsondata['data']['url'] = app('captcha')->create('default', true);

        return $this->apiResponse($this->jsondata);
    }
}
