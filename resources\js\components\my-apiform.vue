<template>
    <ClientOnly fallback-tag="span" fallback="Loading comments...">
        <template v-if="type == 'checkbox'">
            <el-checkbox-group v-model="localModelValue">
                <el-checkbox :key="index" :value="rs.value" :label="rs.label" v-for="(rs, index) in localOptions" />
            </el-checkbox-group>
        </template>

        <template v-else-if="type == 'radio'">
            <el-radio-group v-model="localModelValue">
                <el-radio :key="index" :label="rs.label" :value="rs.value" v-for="(rs, index) in localOptions"> </el-radio>
            </el-radio-group>
        </template>
        <template v-else-if="type == 'select2'">
            <el-select-v2 v-model="localModelValue" style="width: 100%" filterable :options="localOptions" />
        </template>
        <template v-else-if="type == 'select2multiple'">
            <el-select-v2 v-model="localModelValue" style="width: 100%" :multiple="true" filterable :options="localOptions" />
        </template>

        <template v-else>
            <el-select v-model="localModelValue">
                <el-option value="">{{ title }}</el-option>
                <el-option :key="index" :value="rs.value" :label="rs.label" v-for="(rs, index) in localOptions"> </el-option>
            </el-select>
        </template>

        <el-icon
            v-if="showReconnectIcon"
            class="reconnect-icon"
            @click="fetchOptions"
            style="display: inline; width: 100%; text-align: left"
        >
            <span class="text-danger"><Refresh /> 連線失敗，點擊重新連線</span>
        </el-icon>
        <slot></slot>
    </ClientOnly>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, defineProps, defineEmits, watch, getCurrentInstance, onErrorCaptured } from 'vue'

const http = createHttp()
import { Refresh } from '@element-plus/icons-vue'
const emits = defineEmits(['update:modelValue', 'change'])
const props = defineProps({
    type: {
        type: String,
        required: true,
        //default:'select',
        validator: (value: string) => ['select', 'checkbox', 'radio', 'select2', 'select2multiple'].includes(value)
    },
    options: {
        type: [Object]
    },
    isFirstDisplayFlag: {
        type: Boolean,
        default: true
    },
    title: {
        type: String,
        default: 'Select'
    },
    api: {
        type: String,
        default: '',
        required: true
    },
    modelValue: {
        default: ''
    }
})

const showReconnectIcon = ref(false)
const localModelValue = computed({
    get() {
        const type = props.type as string
        if (type === 'checkbox') {
            return Array.isArray(props.modelValue) ? props.modelValue.map(String) : (props.modelValue || '').split(',').filter(Boolean)
        }
        if (type === 'select2multiple') {
            if (props.modelValue != null) {
                //是否字串
                if (typeof props.modelValue == 'string' && props.modelValue != '') {
                    return ('' + props.modelValue).split(',')
                } else if (Array.isArray(props.modelValue)) {
                    return props.modelValue
                }
            }
            return []
        }
        return String(props.modelValue ?? '')
    },
    set(val: string) {
        if (Array.isArray(val)) {
            emits('update:modelValue', val.join(','))
        } else {
            emits('update:modelValue', val)
        }
        //emits('update:modelValue', val)
    }
})

let localOptions = ref([])

const fetchOptions = async () => {
    try {
        showReconnectIcon.value = false
        localOptions.value = []

        let rep = await http.post(props.api, {}, false)
        if (rep.resultcode == '0') {
            localOptions.value = rep.data.map(item => ({
                value: String(Object.values(item)[0]),
                label: String(Object.values(item)[1] || Object.values(item)[0])
            }))
        } else {
            throw new Error(rep.resultmessage)
        }
    } catch (error) {
        showReconnectIcon.value = true
        console.error('Error fetching options:', error)
    }
}

onMounted(async () => {
    fetchOptions()
})

const errorCaptured = async (err, vm, info) => {
    console.error(`my-apiform Error: ${vm.$options.name};message: ${err.toString()};info: ${info}`)
    return false // 可以返回 true 或 false 來決定是否繼續往上傳遞錯誤
}
/*
<my-apiform
type= "select"
v-model="inputs.location"
api="/api/dependentdropdown/kindhead"
>
</my-apiform>
*/
</script>

<style scoped>
.is-error {
    border-color: #f56c6c;
}
</style>
