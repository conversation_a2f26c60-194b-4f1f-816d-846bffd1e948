# TASK003 - 找教練功能（搜尋列表與教練個人頁面）

## 任務描述

實作找教練功能，包含教練搜尋列表頁面、篩選排序功能，以及教練個人頁面展示。此功能是平台的核心功能之一，讓使用者能夠找到合適的滑雪教練。

### 主要功能內容：

1. 教練搜尋列表頁面（含篩選、排序、分頁）
2. 教練個人頁面（展示資料、證照、評價）
3. 教練影片展示功能
4. 教練評價系統展示
5. 預約功能入口（導向預約頁面）

## 狀態

-   [x] 計劃中
-   [ ] 測試單元編寫中
-   [ ] 開發中
-   [ ] 完成

## 驗收標準

-   [ ] 教練列表頁面能正常顯示所有教練資訊
-   [ ] 可以依據技能等級、價格、地區等條件篩選教練
-   [ ] 可以依據評價、價格、經驗等條件排序教練
-   [ ] 教練個人頁面能完整展示教練資訊
-   [ ] 教練證照資料能正確顯示
-   [ ] 教練評價能正常顯示且分頁
-   [ ] 教練教學影片能正常播放
-   [ ] 預約按鈕能正確導向預約流程
-   [ ] 響應式設計在各裝置正常顯示

## 注意事項

-   教練資料需要從 coach 資料表讀取
-   證照資料從 coachCertification 資料表讀取
-   評價資料從 review 資料表讀取
-   教練頭像存放在 `public/images/coach/` 目錄
-   教練教學影片使用 YouTube 連結，不儲存檔案
-   列表頁面需要實作分頁功能
-   搜尋功能要考慮效能優化
-   未登入使用者也可以瀏覽教練資訊
