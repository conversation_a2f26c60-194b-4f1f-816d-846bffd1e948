<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/*swagger api document start*/

/**
 * @OA\Schema(
 *   schema="bookings",
 *      allOf={
 *         @OA\Schema( @OA\Property(property="id", type="integer", description="預約編號", example="10001" )),
 *         @OA\Schema( @OA\Property(property="member_id", type="integer", description="會員編號", example="10001" )),
 *         @OA\Schema( @OA\Property(property="course_id", type="integer", description="課程編號", example="10001" )),
 *         @OA\Schema( @OA\Property(property="coach_id", type="integer", description="教練編號", example="10001" )),
 *         @OA\Schema( @OA\Property(property="booking_date", type="string", format="date", description="預約日期", example="2024-01-01" )),
 *         @OA\Schema( @OA\Property(property="booking_time", type="string", description="預約時間", example="09:00" )),
 *         @OA\Schema( @OA\Property(property="duration", type="integer", description="課程時長(分鐘)", example="60" )),
 *         @OA\Schema( @OA\Property(property="participants", type="integer", description="參與人數", example="2" )),
 *         @OA\Schema( @OA\Property(property="status", type="integer", description="預約狀態", example="1" )),
 *         @OA\Schema( @OA\Property(property="total_amount", type="number", format="decimal", description="總金額", example="1500.00" )),
 *         @OA\Schema( @OA\Property(property="paid_amount", type="number", format="decimal", description="已付金額", example="500.00" )),
 *         @OA\Schema( @OA\Property(property="payment_method", type="integer", description="付款方式", example="1" )),
 *         @OA\Schema( @OA\Property(property="snow_coin_used", type="integer", description="使用雪幣數量", example="50" )),
 *         @OA\Schema( @OA\Property(property="special_requirements", type="string", description="特殊需求", example="需要兒童裝備" )),
 *         @OA\Schema( @OA\Property(property="notes", type="string", description="備註", example="預約備註說明" )),
 *         @OA\Schema( @OA\Property(property="cancelled_reason", type="string", description="取消原因", example="天氣因素" )),
 *         @OA\Schema( @OA\Property(property="cancelled_at", type="string", format="datetime", description="取消時間", example="2024-01-01 12:00:00" )),
 *         @OA\Schema( @OA\Property(property="completed_at", type="string", format="datetime", description="完成時間", example="2024-01-01 16:00:00" )),
 *         @OA\Schema( @OA\Property(property="created_at", type="string", format="datetime", description="建立時間", example="2024-01-01 10:00:00" )),
 *         @OA\Schema( @OA\Property(property="updated_at", type="string", format="datetime", description="更新時間", example="2024-01-01 11:00:00" )),
 *      }
 *)
 */
/*swagger api document end*/

/**
 * 預約資料模型
 *
 * 管理會員預約課程的完整流程，包括預約、付款、課程進行、完成等狀態
 */
class bookings extends Model {
    use HasFactory;

    /**
     * 資料表名稱
     */
    protected $table = 'bookings';

    /**
     * 主鍵設定
     */
    protected $primaryKey = 'id';

    /**
     * 可填寫欄位
     */
    protected $fillable = [
        'member_id',
        'course_id',
        'coach_id',
        'booking_date',
        'booking_time',
        'duration',
        'participants',
        'status',
        'total_amount',
        'paid_amount',
        'payment_method',
        'snow_coin_used',
        'special_requirements',
        'notes',
        'cancelled_reason',
        'cancelled_at',
        'completed_at',
    ];

    /**
     * 資料型別轉換
     */
    protected $casts = [
        'booking_date' => 'date',
        'duration' => 'integer',
        'participants' => 'integer',
        'status' => 'integer',
        'total_amount' => 'decimal:2',
        'paid_amount' => 'decimal:2',
        'payment_method' => 'integer',
        'snow_coin_used' => 'integer',
        'cancelled_at' => 'datetime',
        'completed_at' => 'datetime',
    ];

    /*Relations start*/
    /*Relations end*/

    /**
     * 模型啟動方法
     *
     * 定義模型的生命週期事件處理
     */
    public static function boot() {
        parent::boot();

        static::creating(function ($model) {
            // 建立預約時初始化預設值
            if (!isset($model->status)) {
                $model->status = 1; // 預設為待確認
            }
            if (!isset($model->participants)) {
                $model->participants = 1;
            }
            if (!isset($model->snow_coin_used)) {
                $model->snow_coin_used = 0;
            }
            if (!isset($model->paid_amount)) {
                $model->paid_amount = 0;
            }
        });

        static::updating(function ($model) {
            // 預約狀態變更時記錄日誌
            if ($model->isDirty('status')) {
                \Log::info('預約狀態變更', [
                    'booking_id' => $model->id,
                    'old_status' => $model->getOriginal('status'),
                    'new_status' => $model->status
                ]);
            }
        });

        static::deleted(function ($model) {
            /*Del Relations start*/
            // 刪除預約時處理相關資料

            // 1. 退還已使用的雪幣
            if ($model->snow_coin_used > 0 && $model->status != 6) { // 非已完成狀態才退還
                $snowCoinTransaction = new \App\Models\snow_coin_transactions([
                    'member_id' => $model->member_id,
                    'transaction_type' => 2, // 退款
                    'amount' => $model->snow_coin_used,
                    'description' => "預約取消退還雪幣 (預約編號: {$model->id})",
                    'status' => 2 // 已完成
                ]);
                $snowCoinTransaction->save();
            }

            // 2. 刪除相關評價記錄
            \App\Models\reviews::where('booking_id', $model->id)->delete();
            /*Del Relations end*/
        });
    }

    // === 關聯關係 ===

    /**
     * 所屬會員關聯
     * 預約屬於哪個會員
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function member() {
        return $this->belongsTo(members::class, 'member_id');
    }

    /**
     * 所屬課程關聯
     * 預約的是哪個課程
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function course() {
        return $this->belongsTo(courses::class, 'course_id');
    }

    /**
     * 所屬教練關聯
     * 預約的教練
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function coach() {
        return $this->belongsTo(coaches::class, 'coach_id');
    }

    /**
     * 預約評價關聯
     * 這個預約的評價記錄
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function reviews() {
        return $this->hasMany(reviews::class, 'booking_id');
    }

    /**
     * 雪幣交易記錄關聯
     * 與此預約相關的雪幣交易
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function snowCoinTransactions() {
        return $this->hasMany(snow_coin_transactions::class, 'related_id')
            ->where('related_type', 'booking');
    }

    // === 查詢範圍 ===

    /**
     * 查詢範圍：按狀態篩選
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $status
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByStatus($query, $status) {
        return $query->where('status', $status);
    }

    /**
     * 查詢範圍：有效預約（非取消）
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query) {
        return $query->whereNotIn('status', [5, 7]); // 非取消、非退款
    }

    /**
     * 查詢範圍：今日預約
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeToday($query) {
        return $query->whereDate('booking_date', today());
    }

    /**
     * 查詢範圍：即將到來的預約
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeUpcoming($query) {
        return $query->where('booking_date', '>=', today())
            ->whereIn('status', [2, 3, 4]); // 已確認、進行中、已完成
    }

    /**
     * 查詢範圍：按日期範圍篩選
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $startDate
     * @param string $endDate
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByDateRange($query, $startDate, $endDate) {
        return $query->whereBetween('booking_date', [$startDate, $endDate]);
    }

    /**
     * 查詢範圍：按教練篩選
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $coachId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByCoach($query, $coachId) {
        return $query->where('coach_id', $coachId);
    }

    /**
     * 查詢範圍：按會員篩選
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $memberId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByMember($query, $memberId) {
        return $query->where('member_id', $memberId);
    }

    // === 存取器 ===

    /**
     * 取得預約狀態名稱
     *
     * @return string
     */
    public function getStatusNameAttribute() {
        $statuses = [
            1 => '待確認',
            2 => '已確認',
            3 => '進行中',
            4 => '已完成',
            5 => '已取消',
            6 => '未到場',
            7 => '已退款'
        ];
        return $statuses[$this->status] ?? '未知';
    }

    /**
     * 取得付款方式名稱
     *
     * @return string
     */
    public function getPaymentMethodNameAttribute() {
        $methods = [
            1 => '信用卡',
            2 => '銀行轉帳',
            3 => '現金',
            4 => '雪幣',
            5 => '組合付款'
        ];
        return $methods[$this->payment_method] ?? '未知';
    }

    /**
     * 取得預約完整時間
     *
     * @return string
     */
    public function getFullBookingTimeAttribute() {
        return $this->booking_date->format('Y-m-d') . ' ' . $this->booking_time;
    }

    /**
     * 檢查是否可以取消
     *
     * @return bool
     */
    public function getCanCancelAttribute() {
        // 24小時前可以取消，且狀態為待確認或已確認
        $canCancelTime = $this->booking_date->startOfDay()->subHours(24);
        return now() < $canCancelTime && in_array($this->status, [1, 2]);
    }

    /**
     * 檢查是否可以修改
     *
     * @return bool
     */
    public function getCanModifyAttribute() {
        // 48小時前可以修改，且狀態為待確認或已確認
        $canModifyTime = $this->booking_date->startOfDay()->subHours(48);
        return now() < $canModifyTime && in_array($this->status, [1, 2]);
    }

    /**
     * 取得未付金額
     *
     * @return float
     */
    public function getUnpaidAmountAttribute() {
        return max(0, $this->total_amount - $this->paid_amount);
    }

    /**
     * 檢查是否已付清
     *
     * @return bool
     */
    public function getIsFullyPaidAttribute() {
        return $this->paid_amount >= $this->total_amount;
    }

    /**
     * 取得預約時間狀態
     *
     * @return string
     */
    public function getTimeStatusAttribute() {
        $bookingDateTime = $this->booking_date->setTimeFromTimeString($this->booking_time);
        $now = now();

        if ($bookingDateTime < $now) {
            return '已過期';
        } elseif ($bookingDateTime->diffInHours($now) <= 2) {
            return '即將開始';
        } else {
            return '尚未開始';
        }
    }

    /**
     * 取得總節省金額（雪幣折抵）
     *
     * @return float
     */
    public function getTotalSavingsAttribute() {
        // 假設 1 雪幣 = 1 元
        return $this->snow_coin_used;
    }

    /**
     * 取得預約摘要資訊
     *
     * @return array
     */
    public function getBookingSummaryAttribute() {
        return [
            'id' => $this->id,
            'course_name' => $this->course->course_name ?? '',
            'coach_name' => $this->coach->name ?? '',
            'booking_time' => $this->full_booking_time,
            'participants' => $this->participants,
            'status' => $this->status_name,
            'total_amount' => $this->total_amount,
            'can_cancel' => $this->can_cancel,
        ];
    }
}
