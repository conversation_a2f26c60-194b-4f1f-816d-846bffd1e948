<template>
    <img
        v-if="showImage"
        :src="currentSrc"
        v-bind="$attrs"
        @error="handleImageError"
        ref="imageRef"
        @load="loading = false"
    />
    <div v-if="loading">loading...</div>
    <slot></slot>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { utils } from '@/utils'
const props = defineProps({
    src: {
        type: String,
        required: true
    },
    noimage: {
        type: String,
        required: false
    }
})

const imageRef = ref<HTMLImageElement | null>(null)
const isOriginalImageLoaded = ref(true)
const loading = ref(true)
//const WEB_FOLDER = import.meta.env.VITE_WEB_FOLDER || ''
const showImage = ref(true)
const currentSrc = computed(() => {
    if (isOriginalImageLoaded.value) {
        return utils.getConfig('API_BASE') + props.src
    } else if (props.noimage) {
        return utils.getConfig('API_BASE') + props.noimage
    }
    return ''
})

const handleImageError = () => {
    loading.value = false // 加载失败时更新 loading 状态
    if (isOriginalImageLoaded.value) {
        isOriginalImageLoaded.value = false
        if (!props.noimage) {
            showImage.value = false
        }
    } else {
        //console.error('Both original and fallback images failed to load')
        showImage.value = false
    }
}
const loadImage = () => {
    if (imageRef.value) {
        imageRef.value.src = utils.getConfig('API_BASE') + props.src
        isOriginalImageLoaded.value = true
    }
}

onMounted(loadImage)

const errorCaptured = async (err, vm, info) => {
    console.error(`my-img Error: ${vm.$options.name};message: ${err.toString()};info: ${info}`)
    return false // 可以返回 true 或 false 來決定是否繼續往上傳遞錯誤
}
/*
<my-img
width="576"
height="324"
:src="`images/product/${rs.img.split(',')[0]}`"
noimage="images/no-picture.gif"
/>
*/
</script>
