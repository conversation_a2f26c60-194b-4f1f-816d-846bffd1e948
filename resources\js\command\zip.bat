chcp 65001
@echo off
setlocal enabledelayedexpansion


cd..\..\..\
cd
set currentDir=%cd%

:: 顯示變數值
echo Current:%currentDir%

:: 設定7-Zip路徑
set ZIP_PATH="C:\Program Files\7-Zip\7z.exe"

:: 檢查7-Zip是否存在
if not exist %ZIP_PATH% (
    echo Error: 找不到7-Zip程式，請確認安裝路徑
    echo 預設路徑: %ZIP_PATH%
    pause
    exit /b 1
)

:: 設定來源和目標路徑
set SOURCE_DIR=C:\Users\<USER>\Downloads\.output\public
set TARGET_ZIP=%currentDir%\public\public.zip
echo SOURCE_DIR:%SOURCE_DIR%
echo TARGET_ZIP:%TARGET_ZIP%
:: 檢查來源目錄是否存在
if not exist "%SOURCE_DIR%" (
    echo Error: 找不到來源目錄 %SOURCE_DIR%
    pause
    exit /b 1
)

:: 如果目標zip檔已存在，先刪除
if exist "%TARGET_ZIP%" (
    echo 刪除既有的壓縮檔...
    del "%TARGET_ZIP%"
    del %currentDir%\public\_nuxt /Q
)


:: 執行壓縮
echo 正在壓縮 %SOURCE_DIR% 的內容到 %TARGET_ZIP%...
cd %SOURCE_DIR%

%ZIP_PATH% a %TARGET_ZIP% ".\*" -r

:: 檢查壓縮結果
if %ERRORLEVEL% equ 0 (
    echo 壓縮完成！
) else (
    echo Error: 壓縮過程發生錯誤
)
cd

rmdir /s /q %currentDir%\resources\js\dist
xcopy C:\Users\<USER>\Downloads\.output\public %currentDir%\public /E /I /Y /Q
REM echo 複製到Downloads\.output完成！


REM pause