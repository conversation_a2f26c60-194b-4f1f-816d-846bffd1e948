<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Repositories\viewcountRepository;

class viewcountSeeder extends extends baseSeeder
{
    private $viewcountRepo;

    public function __construct(viewcountRepository $viewcountRepo)
    {
        $this->viewcountRepo = $viewcountRepo;
    }
    /**
     * Run the database seeds.
     */
    public function run()
    {
        
        $this->viewcountRepo->select()
        //->myWhere('kind|S', $kind, 'del', 'Y')
        ->delete();
        

        $this->faker = \Faker\Factory::create('zh_TW');
        // $items = ['www.yahoo.com.tw', 'www.google.com'];
        // foreach ($items as $k => $v) {
        //     $data = [
        //     'kind' => $kind,
        //     'kindtitle' => $v,
        //   ];
        //   $this->viewcountRepo->create($data);
        // }
    }
}
