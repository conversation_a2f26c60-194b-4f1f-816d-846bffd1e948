<?php

namespace App\Http\Controllers\api\admin;

use PF, PT;
use Exception, DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;


class mainController extends Controller {

    private $data;
    private $xmlDoc;




    /**
     *TODO 建構子
     */
    public function __construct() {

        //$this->limit="xx";
        parent::__construct();
        //將request全部導入到$this->data變數中
        $this->data = PF::requestAll($this->data);
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
    }



    public function index(Request $request) {
        $authOkCount = 0;
        $Menuxml = $this->data['xmldoc']->xpath('//參數設定檔/權限/選單');

        $menu = array();
        $navs = [];
        foreach ($Menuxml as $key => $rs1) {
            $menu1 = array();

            //$isopen = '1' == $rs1['IsOpen'] ? 'menu-open' : '';
            //IsOpen

            $menu1['title'] = strval($rs1['主選單名稱']);
            $MenuContext2 = $rs1->xpath('KIND');

            if (count($MenuContext2) > 0) {

                $i3 = 0;
                foreach ($MenuContext2 as $key2 => $rs2) {
                    $nodisplay = strval($rs2['nodisplay']);

                    if ('1' == $nodisplay) {
                        continue;
                    }
                    $MenuContext3 = $rs2->xpath('KIND');
                    $displayflag = 0;



                    $isLimit2 = false;
                    if (count($MenuContext3) > 0) {
                        foreach ($MenuContext3 as $key3 => $rs3) {
                            if (PT::checkRoleLimits($rs3['角色'], $rs3->傳回值)) {
                                $isLimit2 = true;
                                break;
                            }
                        }
                    } else {
                        $isLimit2 = PT::checkRoleLimits($rs2['角色'], $rs2->傳回值);
                    }
                    if ($isLimit2) {
                        $authOkCount++;
                        if ('' != trim(strval($rs2->網址)) || count($MenuContext3) > 0) {

                            $MenuContext3 = $rs2->xpath("KIND");
                            $menu2 = [];
                            $menu2['title'] = strval($rs2->資料);
                            if ('' != trim($rs2->網址)) {
                                $menu2['url'] = strval($rs2->網址);
                            } else {
                                if (count($MenuContext3) > 0) {


                                    foreach ($MenuContext3 as $key3 => $rs3) {

                                        if (PT::checkRoleLimits($rs3['角色'], $rs3->傳回值)) {
                                            $menu3 = [];
                                            $menu3['title'] = strval($rs3->資料);
                                            $menu3['url'] = strval($rs3->網址);
                                            $menu2['data'][] = $menu3;
                                            $authOkCount++;
                                        }
                                    }
                                }
                            }

                            $menu1['data'][] = $menu2;
                        }
                    }
                }
            }
            $menu[] = $menu1;
        }
        foreach ($Menuxml as $key => $rs1) {
            //$navs[strval($rs1->傳回值)] = strval($rs1['主選單名稱']) . " / " . strval($rs1->資料);

            $MenuContext2 = $rs1->xpath('KIND');
            foreach ($MenuContext2 as $key2 => $rs2) {
                $navs[strval($rs2->傳回值)] = strval($rs1['主選單名稱']) . " / " .  strval($rs2->資料);

                $MenuContext3 = $rs2->xpath('KIND');
                foreach ($MenuContext3 as $key3 => $rs3) {
                    $navs[strval($rs3->傳回值)] = strval($rs1['主選單名稱']) . " / " . strval($rs2->資料) . " / " . strval($rs3->資料);

                    $MenuContext4 = $rs3->xpath('KIND');
                    foreach ($MenuContext4 as $key4 => $rs4) {
                        $navs[strval($rs4->傳回值)] = strval($rs1['主選單名稱']) . " / " . strval($rs2->資料) . " / " . strval($rs3->資料) . " / " . strval($rs4->資料);
                    }
                }
            }
        }
        if ($authOkCount == 2) {
            abort(401, '你不是系統管理員，無法進入系統！');
        }


        // $Menuxml = $this->data['xmldoc']->xpath('//參數設定檔/權限/選單');
        // $x = 0;
        // // $_SESSION['status'] = 999;
        // $menu = array();
        // foreach ($Menuxml as $key => $row) {
        //     $menu1 = array();
        //     //$menu[] = $row['主選單名稱'];
        //     $menu1['title'] = strval($row['主選單名稱']);

        //     $MenuContext2 = $row->xpath('KIND');
        //     if (count($MenuContext2) > 0) {
        //         foreach ($MenuContext2 as $key1 => $row2) {
        //             $MenuContext1 = $row2->xpath('KIND[count(@nodisplay)=0]');
        //             $displayflag = 0;
        //             if (\Gate::check('isAdminRole',['999'])){
        //                 $displayflag = 1;
        //             } else {
        //                 if (0 == count($MenuContext1)) {

        //                     if ((PF_splitCompare($_SESSION['userlimit'], strval($row2->傳回值)) && '' != $MenuContext[$y]->傳回值) || ('999' == $_SESSION['status'] || '0' == strval($row2->權限檢查))) {
        //                         $displayflag = 1;
        //                     }
        //                 } else {
        //                     for ($z = 0; $z < count($MenuContext1); ++$z) {
        //                         if (('999' == $_SESSION['status'] || '0' == strval($row2->權限檢查)) || (PF_splitCompare($_SESSION['userlimit'], strval($row2->傳回值)) && '' != strval($row2->傳回值))) {
        //                             $displayflag = 1;
        //                             break;
        //                         }
        //                     }
        //                 }
        //             }

        //             if (1 == $displayflag) {
        //                 $menu2 = [];
        //                 $menu2['title'] = strval($row2->資料);
        //                 if ('' != trim(strval($row2->網址))) {
        //                     $menu2['url'] = strval($row2->網址);
        //                 }
        //                 $MenuContext3 = $row2->xpath('KIND');
        //                 if (count($MenuContext3) > 0) {
        //                     foreach ($MenuContext3 as $key1 => $row3) {
        //                         if (('999' == $_SESSION['status'] || '0' == strval($row3->權限檢查)) || (PF::splitCompare($_SESSION['userlimit'], strval($row3->傳回值)) && '' != strval($row3->傳回值))) {
        //                             $menu3 = [];
        //                             $menu3['title'] = strval($row3->資料);
        //                             $menu3['url'] = strval($row3->網址);
        //                             $menu2['data'][] = $menu3;
        //                         }
        //                     }
        //                 }
        //                 $menu1['data'][] = $menu2;
        //             }
        //         }
        //     }
        //     $menu[] = $menu1;
        // }

        $this->jsondata['data']['data'] = $menu;

        $this->jsondata['data']['navs'] = $navs;

        return $this->apiResponse($this->jsondata);
    }
}
