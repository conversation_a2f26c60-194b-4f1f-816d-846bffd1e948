<script type="application/ld+json">
{

    "@context": "https://schema.org",
    "@type": "Organization",

    "url": "{{ Request::getUri() }}",
    "sameAs": [
        "{{ url('/') }}/"
    ],
    "logo": "{{ url('/') }}/images/logo.png",
    "name": "{{ $title }}",
    "description": "{{config('config.name')}}-{{ $title }}",
    "email": "{{config('config.email')}}",
    "telephone": "+886{{ $field3 }}",
    "address": {
        "@type": "PostalAddress",
        "streetAddress": "{{ $field5 }}",
        "addressLocality": "",
        "addressCountry": "",
        "addressRegion": "",
        "postalCode": ""
    },
    "vatID": "",
    "iso6523Code": ""
}
</script>
