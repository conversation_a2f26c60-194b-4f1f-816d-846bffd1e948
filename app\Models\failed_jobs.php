<?php
namespace App\Models;
use DB;
use PF;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/*swagger api document start*/
/**
 * @OA\Schema(
 *   schema="failed_jobs",
 *      allOf={
 *         @OA\Schema( @OA\Property(property="id", type="integer",description="自動編號", example=""  )),
*         @OA\Schema( @OA\Property(property="uuid", type="string",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="connection", type="string",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="queue", type="string",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="payload", type="string",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="exception", type="string",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="failed_at", type="string",description="", example=""  )),

 *      }
 *)
 */
/*swagger api document end*/
class failed_jobs extends baseModel
{
    //use SoftDeletes;//軟刪除
    use HasFactory;
    public $timestamps = true;
    public $table = 'failed_jobs';
    public $primaryKey = 'id';
    //public $incrementing = false;//取消自動編號
    protected $casts = [
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];
      //欄位必填
    public $rules = [
		'id' => 'required',

    ];
    //欄位屬於檔案類型
    public $fieldFiles = [
        
       // 'imgs' => 'public/images/failed_jobs',
    ];
    public $fieldInfo = [
'id'=>['title'=>'自動編號','type'=>'bigint(20) unsigned'],//
'uuid'=>['title'=>'','type'=>'varchar(190)'],//
'connection'=>['title'=>'','type'=>'text'],//
'queue'=>['title'=>'','type'=>'text'],//
'payload'=>['title'=>'','type'=>'longtext'],//
'exception'=>['title'=>'','type'=>'longtext'],//
'failed_at'=>['title'=>'','type'=>'timestamp'],//
];
    //public $timestamps = false;//不使用 timestamps 相關字段
    //日期欄位的儲存格式。'Y-m-d' or 'U' or ...
    //protected $dateFormat = 'Y-m-d';
    protected $fillable = ['uuid','connection','queue','payload','exception','failed_at']; //可充許傳入的欄位
    protected $guarded =[];   //拒絶修改的欄位(fillable,guarded都設已fillable為準)
    protected $dates = ['failed_at'];

    // public function orderdetail() //父對子 一對多 ordergroup.php
    // {
    //     return $this->hasMany('App\Models\orderdetail');
    // }
    // public function ordergroup() //子對父 多對一 orderdetail.php
    // {
    //     return $this->belongsTo(ordergroup::class, 'ordergroup_id');
    // }
    // public function product() {//父對子  一對一 orderdetail.php
    //     return $this->hasOne(product::class, 'id', 'product_id');
    // }
    public static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            //$model->uuid = (string) Uuid::generate();
        });
        self::created(function ($model) {
                //$model->uuid = (string) Uuid::generate();
        });
        static::updating(function ($model) {
            // do some logging
        });
        self::updated(function ($model) {
            // $model->getChanges() 可以獲取到本次更新的欄位名稱和新值。
            // if ($model->isDirty('欄位名稱') && $model->欄位名稱 == "1") {// '欄位名稱' 的值已被修改
            //     $model->getOriginal('欄位名稱');   //原來的資料
            //     $model->toArray();
            // }
        });
        static::deleting(function ($model) {
                parent::delFieldFile($model); //刪fieldFiles定義的欄位檔案
                // $rows = \App\Models\failed_jobsitem::selectRaw('id');
                // $rows->where('failed_jobs_id', $model->id);
                // $rows->chunk(200, function ($rows) {
                // foreach ($rows as $rs) {
                //     $rs->delete();
                // }
        });
        static::deleted(function ($model) {

        });
    }

}