<?php

namespace App\Http\Controllers\api\admin;

use PF, PT;
use Exception, DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\adminuser;
use App\Http\Controllers\Controller\api\admin;

/***
"功能名稱":"管理人員",
"資料表":"adminuser",
"建立時間":"2024-06-11 13:01:42 ",
 ***/
class usereditController extends Controller {

    private $data;
    private $xmlDoc;


    public function __construct() {

        //$this->limit="xx";
        parent::__construct();
        //將request全部導入到$this->data變數中
        $this->data = PF::requestAll($this->data);
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');

        //$this->data['nav'] = PT::nav($this->data['xmldoc'],"adminuser",$this->data['nav']);
        $this->data['displaynames'] = adminuser::getFieldTitleArray();
    }



    /**
     * @OA\Post(
     *     path="/api/admin/useredit/show",security={{"bearerAuth":{}}},operationId="",tags={"後台/管理人員"},summary="單筆顯示",description="",
     *     @OA\RequestBody(required=true,
     *      @OA\JsonContent(
     *      allOf={

     *         @OA\Schema(@OA\Property(property="id",description="編號",type="integer",example="1",)),

     *     })
     *   ,),

     *   @OA\Response(response=200,description="回覆",
     *     @OA\JsonContent(type="object",
     *      @OA\Property(property="resultcode",type="integer",description="訊息代碼",),
     *      @OA\Property(property="resultmessage",type="string",description="訊息內容"),

     *      @OA\Property(property="data", type="object",
     *      allOf={
     *         @OA\Schema(ref="#/components/schemas/adminuser"),
     *         @OA\Schema(type="object",@OA\Property(property="", type="string",description="系列", example="") ),

     *     })

     *     ,)
     *),)
     */


    public function show($request) {


        $rows = \App\Models\adminuser::selectRaw('adminuser.*');
        $rows->where('id', '=', \Auth::guard('admin')->id());
        $rs = $rows->firstOrFail();


        unset($rs->password);
        unset($rs->api_token);
        unset($rs->remember_token);
        unset($rs->lastlogin_ip);

        $this->jsondata['data'] = $rs;

        return $this->apiResponse($this->jsondata);
    }


    /**
     * @OA\Post(
     *     path="/api/admin/adminuser/store",security={{"bearerAuth":{}}},operationId="",tags={"後台/管理人員"},summary="新增/編輯",description="編號有值代表編輯,沒有代表新增",
     *     @OA\RequestBody(required=true,

     *      @OA\JsonContent(
     *      allOf={
     *         @OA\Schema(ref="#/components/schemas/adminuser"),
     *         @OA\Schema(type="object",@OA\Property(property="", type="string",description="系列", example="") ),
     *     })

     *   ,),
     *   @OA\Response(response=200,description="回覆",
     *     @OA\JsonContent(type="object",
     *      @OA\Property(property="resultcode",type="integer",description="訊息代碼",),
     *      @OA\Property(property="resultmessage",type="string",description="訊息內容"),
     *        @OA\Property(property="data", type="object",
     *         allOf={
     *             @OA\Schema(@OA\Property(property="id", type="integer",description="編號", example="10101") ),
     *         }
     *        )
     *     ),)
     *),)
     */


    public function store(Request $request) {

        $edit = $request->input('id');
        //FIXME 那些欄位為必填判斷
        $validators = null;
        $validators['account'] = ['required']; //帳號
        $validators['name'] = ['required']; //姓名
        if ('' == $edit) {
            $validators['password'] = ['required', 'min:8']; //密碼
        } else {
            $validators['password'] = ['nullable', 'min:8']; //密碼
        }
        $validators['email'] = ['nullable', 'email']; //EMAIL
        //$validators['failcount'] = ['required']; //登入錯誤次數
        if ($validators != null) {
            $validator = \Validator::make($request->all(), $validators);
            $validator->setAttributeNames($this->data['displaynames']);
            if ($validator->fails()) {
                throw new \CustomException(implode(',', $validator->messages()->all()));
            }
        }
        $inputs = $request->all();
        //$inputs['account']=$this->data['account'];//帳號-
        //$inputs['name']=$this->data['name'];//姓名-
        if ($this->data['password'] != "") {
            $inputs['password'] = \Hash::make($this->data['password']); //密碼-
        } else {
            unset($inputs['password']); //密碼-
        }
        //$inputs['email']=$this->data['email'];//EMAIL-
        //$inputs['role']=$this->data['role'];//角色-[998:經銷商 ; 999:管理者 ; ]
        //$inputs['online']=$this->data['online'];//開啟-
        //$inputs['failcount']=$this->data['failcount'];//登入錯誤次數-




        //PF::printr($inputs); exit();
        $rows = adminuser::selectRaw('adminuser.*');
        $rows->myWhere('id|N', \Auth::guard('admin')->id(), 'edit', 'Y');
        $rs = $rows->firstOrFail();
        $rs->update($inputs);
        $this->jsondata['resultmessage'] = '更新成功';

        $this->jsondata['data']['id'] = $edit;
        return $this->apiResponse($this->jsondata);
    }
}
