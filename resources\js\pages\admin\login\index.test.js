import { mount } from '@vue/test-utils'
import { ElButton, ElForm, ElFormItem, ElInput, ElSwitch } from 'element-plus'
import { createPinia } from 'pinia'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import LoginView from './index.vue'

// Mock the router
vi.mock('vue-router', () => ({
    useRouter: () => ({
        push: vi.fn()
    })
}))

// Mock the store
vi.mock('@/stores', () => ({
    useDataStore: () => ({
        config: {
            name: 'Test Admin'
        }
    })
}))

// Mock utils
vi.mock('@/utils', () => ({
    utils: {
        location: vi.fn()
    }
}))

// Mock http
vi.mock('@/utils/http', () => ({
    http: {
        getLoading: () => false,
        post: vi.fn()
    }
}))

describe('LoginView', () => {
    let wrapper

    beforeEach(() => {
        const pinia = createPinia()
        wrapper = mount(LoginView, {
            global: {
                plugins: [pinia],
                components: {
                    ElButton,
                    ElForm,
                    ElFormItem,
                    ElInput,
                    ElSwitch
                },
                stubs: {
                    'el-icon': true
                }
            }
        })
    })

    it('renders properly', () => {
        expect(wrapper.find('.login-container').exists()).toBe(true)
        expect(wrapper.find('.title').text()).toBe('Test Admin')
    })

    it('has required form fields', () => {
        expect(wrapper.find('[placeholder="帳號"]').exists()).toBe(true)
        expect(wrapper.find('[placeholder="密碼"]').exists()).toBe(true)
        expect(wrapper.find('.el-switch').exists()).toBe(true)
    })

    it('updates v-model when input changes', async () => {
        const accountInput = wrapper.find('[placeholder="帳號"]')
        await accountInput.setValue('testuser')
        expect(wrapper.vm.inputs.account).toBe('testuser')

        const passwordInput = wrapper.find('[placeholder="密碼"]')
        await passwordInput.setValue('password123')
        expect(wrapper.vm.inputs.password).toBe('password123')
    })

    it('handles form submission', async () => {
        // Set form values
        await wrapper.setData({
            inputs: {
                account: 'testuser',
                password: 'password123',
                iskeep: true
            }
        })

        // Trigger form submission
        const submitButton = wrapper.findAll('button').find(btn => btn.text().includes('登入'))
        await submitButton.trigger('click')

        // Add assertions based on your form submission logic
        // You might want to check if http.post was called with correct parameters
        // or if proper navigation occurred after successful login
    })

    it('navigates to home page when clicking 回首頁', async () => {
        const homeButton = wrapper.findAll('button').find(btn => btn.text().includes('回首頁'))
        await homeButton.trigger('click')
        expect(wrapper.vm.utils.location).toHaveBeenCalledWith('./')
    })
})
