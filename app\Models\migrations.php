<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use PF;
use DB;
use Illuminate\Support\Facades\Schema;

class migrations extends baseModel
{
    
    public $tabletitle = '';
    public $table = 'migrations';
    public $primaryKey = 'id';

      //欄位必填
      public $rules = [
		'id' => 'required',


    ];
    public $fieldInfo = [
'id'=>['title'=>'自動編號','type'=>'int(10) unsigned'],//
'migration'=>['title'=>'','type'=>'varchar(190)'],//
'batch'=>['title'=>'','type'=>'int(11)'],//
];
    //public $timestamps = false;//不使用 timestamps 相關字段
    //日期欄位的儲存格式。'Y-m-d' or 'U' or ...
    //protected $dateFormat = 'Y-m-d';
    protected $fillable = ['migration','batch']; //可充許傳入的欄位
    protected $guarded =[];   //拒絶修改的欄位(fillable,guarded都設已fillable為準)
    
    protected $dates = [];

  

//   public function __construct() {
//         $this->fillable = parent::getfillables();//接受$request->all();
//         //$this->fillable =array_keys($this->fieldInfo);
//         parent::__construct();        
//         parent::setFieldInfo($this->fieldInfo);
//   }         
    
    // public function ordergroup()//父對子 一對多
    // {
    //     return $this->hasMany('App\Models\ordergroupitem');
    // }
    // public function kindhead()//子對父 多對一
    // {
    //  return $this->belongsTo(kindhead::class,'kindheadid');
    // }
    public static function boot()
    {
        parent::boot();
        
        static::creating(function ($model) {
            //$model->uuid = (string) Uuid::generate();
        });
        self::created(function ($model) {          
                //$model->uuid = (string) Uuid::generate();
        });
        static::updating(function ($model) {
            // do some logging
        });
        self::updated(function ($model) {          

        });
         static::deleting(function ($model) {



          
        });
        static::deleted(function ($model) {
            
        });

    }	


}