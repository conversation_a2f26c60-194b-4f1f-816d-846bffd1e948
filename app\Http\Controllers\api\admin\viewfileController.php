<?php

namespace App\Http\Controllers\api\admin;

use PF;
use PT;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Validator;

class viewfileController extends Controller {
    private $fieldnicknames;
    private $data;
    private $xmldoc;
    private $filename = '';

    /**
     *建構子.
     */
    public function __construct() {
        //$this->limit="xx";
        parent::__construct();

        $this->data = PF::requestAll($this->data);

        // $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');

        $this->data['hiddens'] = array();

        $this->fieldnicknames = [
            'body' => '內容',
        ];
        if ('zh' != PF::xmlSearch($this->data['xmldoc'], '//參數設定檔/語系/KIND/傳回值', '資料', 'zh')) {
            if ('' == $this->data['alg']) {
                $this->data['alg'] = 'zh';
            }
            $items = explode('/', $this->data['edit']);
            $f = 'views/' . $items[0] . '/' . $this->data['alg'] . '/' . $items[1] . '.blade.php';
        } else {
            $f = 'views/' . $this->data['edit'] . '.blade.php';
            // $this->filename = resource_path('views/'.$this->data['edit'].'.blade.php');
        }

        if (\File::exists(resource_path($f))) {
            $this->filename = resource_path($f);
        } elseif (\File::exists(storage_path($f))) {
            $this->filename = storage_path($f);
        } else {
            throw new \CustomException('no file');
        }
    }

    /**
     * 資料單一詳細頁
     *
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request) {


        $edit = $request->input('edit');

        $validators = null;
        $validators['edit'] = 'required';
        $validator = Validator::make($request->all(), $validators);
        $validator->setAttributeNames($this->fieldnicknames);
        if ($validator->fails()) {
            throw new \CustomException(implode(',', $validator->messages()->all()));
        }

        $body = \File::get($this->filename);

        $type = PF::xmlSearch($this->data['xmldoc'], '//參數設定檔/權限/選單/KIND/傳回值', '形態', $request->input('edit'));

        if ($type == $request->input('edit')) {
            $type = PF::xmlSearch($this->data['xmldoc'], '//參數設定檔/權限/選單/KIND/KIND/傳回值', '形態', $request->input('edit'));
        }

        if ('' == $type) {
            $type = 'html';
        }

        $this->jsondata['data']['type'] = $type;

        // $body=str_replace("@extends","###extends",$body);
        // $body=str_replace("@section","###section",$body);
        //$body=str_replace("@","___",$body);
        $this->jsondata['data']['body'] = $body;
        return $this->apiResponse($this->jsondata);
    }

    /**
     * 資料新增編輯寫入.
     *
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request) {
        $edit = $request->input('edit');
        $validators = null;
        $validators['edit'] = 'required';
        $validators['body'] = 'required';
        $validator = Validator::make($request->all(), $validators);
        $validator->setAttributeNames($this->fieldnicknames);
        if ($validator->fails()) {
            return view('errors.validatorback')->withErrors($validator);
        }

        $this->data['body'] = str_replace('&#39;', "'", $this->data['body']);
        $this->data['body'] = str_replace('&quot;', "'", $this->data['body']);
        $this->data['body'] = str_replace('-&gt;', '->', $this->data['body']);
        $this->data['body'] = str_replace('&amp;', '&', $this->data['body']);

        //$this->data['body'] = str_replace('@end', "\n@end", $this->data['body']);

        \File::put($this->filename, $this->data['body']);
        $this->jsondata['resultmessage'] = '更新成功';
        //\Artisan::call('config:cache');

        return response()->json(
            $this->jsondata,
            200,
            ['Content-Type' => 'application/json; charset=utf-8'],
            JSON_UNESCAPED_UNICODE
        );
    }
}
