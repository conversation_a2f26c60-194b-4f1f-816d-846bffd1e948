// src/utils/http.ts
import { utils } from '@/utils'
import { ref } from 'vue'
export const createHttp = () => {
    const loading = ref(false) // 每个 HTTP 实例的加载状态

    return {
        setLoading(value) {
            loading.value = value
        },
        getLoading() {
            return loading.value
        },
        getRequestConfig(url: string) {
            let api_base = utils.getConfig('API_BASE') // 读取 API_BASE

            if (utils.isEmpty(api_base) && process.env.NODE_ENV === 'development') {
                const url = useRequestURL()
                let host = window.location.host
                host = host.split(':')[0]
                api_base = `http://${host}:81/`
                if (window.location.protocol === 'https:') {
                    api_base = `${window.location.protocol}//${host}/`
                }
            }
            const store = useDataStore()
            let headers = {
                'Content-Type': 'application/json'
            }

            if (url.includes('/admin/')) {
                headers = {
                    ...headers, // 保留原有的 headers
                    Authorization: `Bearer ${store.adminuser?.api_token}`
                }
            } else if (url.includes('/membercenter/')) {
                headers = {
                    ...headers, // 保留原有的 headers
                    Authorization: `Bearer ${store.member?.api_token}`
                }
            }

            return { api_base, headers }
        },
        async postEncrypt(url: string, postData: any, isloading = true) {
            return this.postClient(url, { encryptDataJson: crypto.encryptDataJson(postData) }, isloading)
        },

        async post(url: string, postData: any, isloading = true) {
            if (process.client) {
                return this.postClient(url, postData, isloading)
            }
            try {
                if (isloading) {
                    loading.value = true
                }
                if (process.env.NODE_ENV === 'development') {
                    process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0'
                }
                const { api_base, headers } = this.getRequestConfig(url)

                const { data, pending, error, refresh } = await useFetch(url, {
                    baseURL: api_base,
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify(postData),
                    timeout: 120 * 1000 // 设置超时时间
                })
                if (error.value) {
                    console.error(['error', error])
                    throw new Error(error.value.message)
                }

                return data.value
            } catch (error: any) {
                if (error.code == 'ERR_NETWORK') {
                    utils
                        .confirm('您想重新連接嗎？')
                        .then(async result => {
                            this.post(url, postData, isloading)
                        })
                        .catch(error => {
                            // 处理取消重连的情况
                        })
                }
                throw new Error(error)
            } finally {
                loading.value = false
            }
        },
        async postClient(url: string, postData: any, isloading = true) {
            try {
                if (isloading) {
                    loading.value = true
                }
                const { api_base, headers } = this.getRequestConfig(url)

                const response = await $fetch.raw(url, {
                    baseURL: api_base,
                    method: 'POST',
                    headers: headers,
                    // credentials: 'include',
                    // responseType: 'json',
                    body: JSON.stringify(postData),
                    timeout: 120 * 1000 // 设置超时时间
                })

                if (typeof response._data?.resultcode != 'undefined') {
                    if ([401].includes(response._data.resultcode)) {
                        const store = useDataStore()
                        if (url.indexOf('api/admin') > -1) {
                            store.adminuser.api_token = ''
                            utils.location('/admin/login')
                        } else if (url.indexOf('api/membercenter') > -1) {
                            store.member.api_token = ''
                            utils.location('/member/login')
                        }
                    }
                }
                // if (error) {
                //     console.log(['error', error])
                //     throw new Error(error.value.message)
                //}
                // console.log(['response', response._data])

                return response._data
            } catch (error: any) {
                if (error.code == 'ERR_NETWORK') {
                    utils
                        .confirm('您想重新連接嗎？')
                        .then(async result => {
                            this.post(url, postData, isloading)
                        })
                        .catch(error => {
                            // 处理取消重连的情况
                        })
                }
                throw new Error(error)
            } finally {
                loading.value = false
            }
        },
        /**
         * 发送信息请求
         * @param {string} url - 请求的 URL
         * @param {boolean} [isloading=false] - 是否显示加载状态
         * @returns {Promise<Object>} 返回服务器响应的数据
         */
        async info(url: string, isloading = false) {
            try {
                const { data: responseData, error } = await useFetch(url, {
                    method: 'POST',
                    body: {}
                })

                if (error.value) {
                    throw new Error(error.value.message)
                }

                if (responseData.value.resultcode == '0') {
                    return responseData.value.data
                }
            } catch (error: any) {
                console.log(error)
            }
        },

        /**
         * 发送下载请求
         * @param {string} url - 请求的 URL
         * @param {Object} inputs - 要发送的数据
         * @param {boolean} [isloading=true] - 是否显示加载状态
         * @throws {Error} 捕获下载过程中的错误
         */
        async download(url: string, inputs: any, isloading = true) {
            if (isloading) {
                loading.value = true
            }
            try {
                const { api_base, headers } = this.getRequestConfig(url)

                // 使用原生 fetch API 來確保正確獲取標頭
                const response = await fetch(api_base + url, {
                    method: 'POST',
                    headers: {
                        ...headers,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(inputs),
                    signal: AbortSignal.timeout(120000) // 120秒超時
                })

                if (!response.ok) {
                    throw new Error(`下載失敗：${response.status} ${response.statusText}`)
                }

                // 從 Content-Disposition 標頭中提取檔案名稱
                const filename = this.parseFilenameFromHeader(response.headers.get('content-disposition'))

                // 獲取 blob 數據並創建下載
                const blob = await response.blob()
                this.triggerDownload(blob, filename)
            } catch (error: any) {
                if (error.name === 'TimeoutError') {
                    utils.formElError('下載超時，請稍後再試')
                } else {
                    utils.formElError(error)
                }
                console.error('下載錯誤:', error)
            } finally {
                loading.value = false
            }
        },

        /**
         * 從 Content-Disposition 標頭解析檔案名稱
         * @param {string} contentDisposition - Content-Disposition 標頭值
         * @returns {string} 解析出的檔案名稱
         */
        parseFilenameFromHeader(contentDisposition: string | null): string {
            if (!contentDisposition) {
                return 'download.xlsx'
            }

            // 解析優先級：filename*=UTF-8'' > filename*=utf-8' > filename=
            const patterns = [
                /filename\*=UTF-8''([^;\s]+)/i, // 標準國際化格式
                /filename\*=utf-8'([^;\s]+)/i, // Laravel Excel 格式
                /filename=['"]?([^'";]+)['"]?/i // 基本格式
            ]

            for (const pattern of patterns) {
                const match = contentDisposition.match(pattern)
                if (match && match[1]) {
                    try {
                        let filename = decodeURIComponent(match[1])
                        // 清理檔案名稱並確保有副檔名
                        filename = filename.replace(/^["']|["']$/g, '')
                        return filename.includes('.') ? filename : `${filename}.xlsx`
                    } catch (e) {
                        console.warn('檔案名稱解碼失敗:', e)
                        continue
                    }
                }
            }

            return 'download.xlsx'
        },

        /**
         * 觸發檔案下載
         * @param {Blob} blob - 檔案數據
         * @param {string} filename - 檔案名稱
         */
        triggerDownload(blob: Blob, filename: string): void {
            const downloadUrl = window.URL.createObjectURL(blob)

            try {
                // 創建臨時下載連結
                const link = document.createElement('a')
                link.href = downloadUrl
                link.download = filename
                link.style.display = 'none'

                // 觸發下載
                document.body.appendChild(link)
                link.click()

                // 清理 DOM
                document.body.removeChild(link)
            } finally {
                // 確保 URL 對象被釋放
                window.URL.revokeObjectURL(downloadUrl)
            }
        },
        getTimestamp: async () => {
            try {
                const http = createHttp()
                let rep = await http.post('api/utils/timestamp', {})

                if (rep.resultcode == '0') {
                    return rep.data.timestamp
                } else {
                    throw new Error(rep.resultmessage)
                }
            } catch (error: any) {
                utils.formElError(error)
                console.error(error)
            }
        }
    }
}
