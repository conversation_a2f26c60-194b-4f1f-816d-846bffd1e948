<template>
    <my-breadcrumb id="useredit"></my-breadcrumb>

    <el-form ref="formEl" :model="inputs" v-loading="http.getLoading()" @submit.prevent="onSubmit">
        <div class="form-group row">
            <label class="col-md-2">帳號<span class="text-danger p-1"></span></label>
            <div class="col-md-10">
                {{ inputs.account }}
            </div>
        </div>
        <div class="form-group row">
            <label class="col-md-2">姓名<span class="text-danger p-1">*</span></label>
            <div class="col-md-10">
                <el-form-item
                    prop="name"
                    :rules="[
                        {
                            required: true,
                            message: '姓名 未填'
                        }
                    ]"
                >
                    <el-input v-model="inputs.name" type="text" />
                </el-form-item>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-md-2">密碼<span class="text-danger p-1"></span></label>
            <div class="col-md-10">
                <el-form-item
                    prop="password"
                    :rules="[
                        {
                            required: false,
                            message: '密碼 未填'
                        },
                        {
                            min: 8,
                            max: 20,
                            message: '長度在 8 到 20 个字符',
                            trigger: 'blur'
                        }
                    ]"
                >
                    <el-popover
                        placement="top-start"
                        title="密碼規格"
                        :width="400"
                        trigger="hover"
                        content="密碼長度必須為8~20位, 其中必須包含至少一位數字、一位英文，若需有特殊符號僅限於 ! @ # $ % & *"
                    >
                        <template #reference>
                            <el-input v-model="inputs.password" show-password type="password" placeholder="密碼"> </el-input>
                        </template>
                    </el-popover>
                </el-form-item>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-md-2">EMAIL<span class="text-danger p-1"></span></label>
            <div class="col-md-10">
                <el-form-item
                    prop="email"
                    :rules="[
                        {
                            required: false,
                            message: 'EMAIL 未填'
                        },
                        {
                            type: 'email',
                            message: 'EMAIL 格式錯誤'
                        }
                    ]"
                >
                    <el-input v-model="inputs.email" type="email" placeholder="ex <EMAIL>" />
                </el-form-item>
            </div>
        </div>
        <div align="center">
            <button type="submit" class="btn btn-primary">確定</button>
             
            <button type="reset" class="btn btn-secondary" @click="formEl.resetFields()">取消</button>
        </div>
    </el-form>
</template>

<script setup lang="ts">
import { ref, reactive, getCurrentInstance, onMounted } from 'vue'
import { useRouter } from 'vue-router'

import { createHttp } from '@/utils/http' //http套件
const http = createHttp()

import { utils } from '@/utils'
//const emit = defineEmits(["updateNavTitle"]);
const router = useRouter()
const emits = defineEmits(['close-modal']) //接受外面送來的觸發的參數
const props = defineProps(['edit']) //接受外面送來的參數

const formEl = ref(null)
// 使用 reactive 定義對象
const datas = ref([])
const inputs = reactive({
    account: '',
    name: '',
    password: '',
    password_confirmation: '',
    email: '',
    role: '',
    online: '',
    failcount: ''
})
const templetedata = reactive({})
const getData = async () => {
    try {
        let rep = await http.post('api/admin/useredit/show', {})
        if (rep.resultcode == '0') {
            Object.assign(inputs, rep.data)
            //console.log(["inputs", inputs]);
        } else {
            throw new Error(rep.resultmessage)
        }
    } catch (error: any) {
        utils.formElError(error)
        console.error(error)
    } finally {
    }
}
const onSubmit = async () => {
    if (!formEl.value) return

    try {
        const valid = await formEl.value.validate()
        if (valid) {
            try {
                let rep = await http.post('api/admin/useredit/store', inputs)

                if (rep.resultcode == '0') {
                    utils.toast(rep.resultmessage)
                    //router.go(-1);
                    //router.push("/member/login");
                    //emits("closed-dialog", true);
                } else {
                    throw new Error(rep.resultmessage)
                }
            } catch (error) {
                utils.alert(error.message, 'Error', 'error')
                console.error(error)
            }
        }
    } catch (error) {
        console.error('Validation error:', error)
    }
}

onMounted(async () => {
    getData()
})
// defineExpose({
//     inputs,
// }); //父可以使用自己的參數
</script>
<style scoped></style>
