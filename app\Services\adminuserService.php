<?php

namespace App\Services;

use PF;

/***
"功能名稱":"服務層-管理人員權限",
"資料表":"",
"備註":" ",
"建立時間":"2022-01-18 16:52:26",
***/

class adminuserService
{
    public function __construct()
    {
    }

    public function checkLimits($controllers)
    {
        if (request()->is('api/*')) {
            if ('999' != \request()->adminuser['role']) {
                //$items=explode("chr(13).chr(10)",$s);
                $lastpath = \Request::segment(4);
                if ('' == $lastpath) {
                    $lastpath = 'index';
                }

                $userlimits = explode(',', \request()->adminuser['userlimit']);

                foreach ($controllers as $k => $v) {
                    if (PF::isEmpty($k)) {
                        if (in_array($lastpath, $v)) {
                            return true;
                        }
                    } elseif (0 == count($v)) {
                        if (in_array($k, $userlimits)) {
                            return true;
                        }
                    } else {
                        if (in_array($k, $userlimits)) {
                            if (in_array($lastpath, $v)) {
                                return true;
                            }
                        }
                    }
                }

                throw new \CustomException('no auth limit');
            }
        }
    }
}
