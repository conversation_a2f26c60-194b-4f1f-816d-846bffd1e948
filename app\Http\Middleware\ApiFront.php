<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ApiFront extends baseMiddleware {
    /**
     * 處理傳入的請求。
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     *
     * @return mixed
     */
    public function handle(Request $request, Closure $next) {

        // 調用父類的 handle 方法
        $response = parent::handle($request, $next);


        return $response;
    }
}
