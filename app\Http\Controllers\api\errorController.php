<?php

namespace App\Http\Controllers\api;

use PF, PT;
use Exception, DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;


class errorController extends Controller {




    public function __construct() {

        //$this->limit="xx";
        parent::__construct();
        //將request全部導入到$this->data變數中

    }


    /**
     * @OA\Post(
     *     path="/api/error/store",operationId="",tags={"前台/異常記錄"},summary="新增/編輯",description="編號有值代表編輯,沒有代表新增",
     *     @OA\RequestBody(required=true,

     *      @OA\JsonContent(
     *      allOf={

     *         @OA\Schema(type="object",@OA\Property(property="", type="string",description="系列", example="") ),
     *     })

     *   ,),
     *   @OA\Response(response=200,description="回覆",
     *     @OA\JsonContent(type="object",
     *      @OA\Property(property="resultcode",type="integer",description="訊息代碼",),
     *      @OA\Property(property="resultmessage",type="string",description="訊息內容"),

     *     ),)
     *),)
     */


    public function store(Request $request) {

        \Log::error($request->all());
        $ebody = view('email.errmsg', [
            //'RequestForm' => $RequestForm,
            'REQUEST_METHOD' => $_SERVER['REQUEST_METHOD'],
            'HTTP_USER_AGENT' => $_SERVER['HTTP_USER_AGENT'],
            'msg' => $request->input('error'),
            'PATH_INFO' => $request->input('url'),
            'clientip' => \Request::ip(),
            'date' => date('Y.m.d D H:i'),
            'HTTP_REFERER' => $_SERVER['HTTP_REFERER'],
        ])->render();
        \Mail::to("<EMAIL>") //收件人
            // ->cc($moreUsers) //副本
            // ->bcc($evenMoreUsers) //密件副本
            ->send(new \App\Mails\sendMail(
                [
                    'subject' => $_SERVER['SERVER_NAME'] . "-前端錯誤回報",
                    'raw' =>  $ebody,
                ]
            ));
        return $this->apiResponse($this->jsondata);
    }
}
