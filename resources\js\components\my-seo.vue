<template></template>
<script setup lang="ts">
import { computed, watch } from 'vue'
import { useSeoMeta } from '#app'

const store = useDataStore()
import { toRefs, isRef } from 'vue'
const props = defineProps({
    defaultTitle: String,
    title: [Object, String],
    keyword: [Object, String],
    description: [Object, String],
    image: String,
    url: String,
    type: String
})

const localTitle = computed(() => {
    try {
        let title = props.title
        if (isRef(props.title)) {
            title = props.title.value
        }
        if (utils.isEmpty(title) && utils.isEmpty(props.defaultTitle)) {
            return store.config.title
        } else if (!utils.isEmpty(title) && !utils.isEmpty(props.defaultTitle)) {
            return title + ' | ' + props.defaultTitle + ' | ' + store.config.title
        } else if (utils.isEmpty(title) && !utils.isEmpty(props.defaultTitle)) {
            return props.defaultTitle + ' | ' + store.config.title
        } else if (!utils.isEmpty(title)) {
            return title + ' | ' + store.config.title
        }
        return store.config.title
    } catch (error) {
        return store.config?.title
    }
})
const localDescription = computed(() => {
    try {
        let description = props.description
        if (isRef(props.description)) {
            description = props.description.value
        }
        if (utils.isEmpty(description)) {
            description = store.config.description
        }
        if (!utils.isEmpty(description)) {
            description = utils.noHtml(description)
        }
        return description
    } catch (error) {
        return store.config?.description
    }
})
const localKeyword = computed(() => {
    if (utils.isEmpty(props.keyword)) {
        return store.config.keyword
    }
    return store.config.keyword
})
// 定義 SEO Meta 資料
useSeoMeta({
    title: localTitle,
    meta: [
        { name: 'keywords', content: localKeyword },
        { name: 'description', content: localDescription },
        { name: 'og:title', content: localTitle },
        { name: 'og:description', content: localDescription },
        { name: 'og:image', content: props.image || '' },
        { name: 'og:url', content: props.url || '' },
        { name: 'og:type', content: props.type || 'website' }
    ],
    link: [{ rel: 'canonical', href: props.url || '' }]
})
</script>
