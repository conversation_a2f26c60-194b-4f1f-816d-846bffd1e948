<template>
    <div v-loading="http.getLoading()">
        {{ data?.created_at }}
        {{ data?.title }}
        {{ data.memo }}

        <div v-html="data.body"></div>
    </div>
</template>
<script setup lang="ts">
import { onMounted, reactive } from 'vue'

const store = useDataStore()
const http = createHttp()
const router = useRouter()
const route = useRoute()
const props = defineProps({
    id: {
        type: Number,
        required: true
    }
})

// 初始化資料
let data = reactive<news>({

})

const inputs = reactive({
    id: route.params.id
})

const getData = async () => {
    try {
        // 明確指定 API 回應類型
        let rep = (await http.post('api/news/show', inputs)) as ApiResponse

        if (rep.resultcode == '0') {
            Object.assign(data, rep.data)
        } else {
            throw new Error(rep.resultmessage)
        }
    } catch (error: any) {
        utils.formElError(error)
        console.error(error)
    } finally {
    }
}

onMounted(async () => {
    getData()
})
</script>

<style lang="scss"></style>
