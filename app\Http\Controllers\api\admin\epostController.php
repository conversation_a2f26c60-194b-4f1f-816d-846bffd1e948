<?php

namespace App\Http\Controllers\api\admin;

use PF, PT;
use Exception, DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\epost;
use App\Http\Controllers\Controller\api\admin;

/***
"功能名稱":"上稿系統",
"資料表":"epost",
"建立時間":"2024-10-27 21:27:44 ",
 ***/
class epostController extends Controller {

    private $data;
    private $xmlDoc;


    public function __construct() {

        //$this->limit="xx";
        parent::__construct();
        //將request全部導入到$this->data變數中
        $this->data = PF::requestAll($this->data);
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');

        //$this->data['nav'] = PT::nav($this->data['xmldoc'],"epost",$this->data['nav']);
        $this->data['displaynames'] = epost::getFieldTitleArray();
    }





    /**
     * @OA\Post(
     *     path="/api/admin/epost/show",security={{"bearerAuth":{}}},operationId="",tags={"後台/上稿系統"},summary="單筆顯示",description="",
     *     @OA\RequestBody(required=true,
     *      @OA\JsonContent(
     *      allOf={

     *         @OA\Schema(@OA\Property(property="id",description="編號",type="integer",example="1",)),

     *     })
     *   ,),

     *   @OA\Response(response=200,description="回覆",
     *     @OA\JsonContent(type="object",
     *      @OA\Property(property="resultcode",type="integer",description="訊息代碼",),
     *      @OA\Property(property="resultmessage",type="string",description="訊息內容"),

     *      @OA\Property(property="data", type="object",
     *      allOf={
     *         @OA\Schema(ref="#/components/schemas/epost"),
     *         @OA\Schema(type="object",@OA\Property(property="", type="string",description="系列", example="") ),

     *     })

     *     ,)
     *),)
     */


    public function show($request) {



        $rows = \App\Models\epost::selectRaw('epost.*');
        $rows->where('epostid', '=', $request->input('edit'));
        $rs = $rows->firstOrFail();
        $rs = PF::jsonToRs($rs, $rs->jsonbody);
        unset($rs->jsonbody);
        /*
             unset($rs->password);
             unset($rs->api_token);
             unset($rs->remember_token);
             unset($rs->lastlogin_ip);
             */
        $this->jsondata['data'] = $rs;

        return $this->apiResponse($this->jsondata);
    }


    /**
     * @OA\Post(
     *     path="/api/admin/epost/store",security={{"bearerAuth":{}}},operationId="",tags={"後台/上稿系統"},summary="新增/編輯",description="編號有值代表編輯,沒有代表新增",
     *     @OA\RequestBody(required=true,

     *      @OA\JsonContent(
     *      allOf={
     *         @OA\Schema(ref="#/components/schemas/epost"),
     *         @OA\Schema(type="object",@OA\Property(property="", type="string",description="系列", example="") ),
     *     })

     *   ,),
     *   @OA\Response(response=200,description="回覆",
     *     @OA\JsonContent(type="object",
     *      @OA\Property(property="resultcode",type="integer",description="訊息代碼",),
     *      @OA\Property(property="resultmessage",type="string",description="訊息內容"),
     *        @OA\Property(property="data", type="object",
     *         allOf={
     *             @OA\Schema(@OA\Property(property="id", type="integer",description="編號", example="10101") ),
     *         }
     *        )
     *     ),)
     *),)
     */


    public function store(Request $request) {

        $edit = $request->input('edit');
        //FIXME 那些欄位為必填判斷
        $validators = null;
        if ($validators != null) {
            $validator = \Validator::make($request->all(), $validators);
            $validator->setAttributeNames($this->data['displaynames'] == null ? [] : $this->data['displaynames']);
            if ($validator->fails()) {
                throw new \CustomException(implode(',', $validator->messages()->all()));
            }
        }


        $inputs = $request->all();
        $inputs['epostid'] = $edit;

        $rows = \App\Models\epost::updateOrCreate(
            [
                'epostid' => $edit
            ],
            $inputs
        );

        $this->jsondata['resultmessage'] = '更新成功';



        return $this->apiResponse($this->jsondata);
    }
    /**
     * @OA\Post(
     *     path="/api/admin/epost/destroy",security={{"bearerAuth":{}}},operationId="",tags={"後台/上稿系統"},summary="刪除",description="",
     *   @OA\RequestBody(required=true,@OA\MediaType(mediaType="application/json",@OA\Schema(
     *         @OA\Property(property="del",description="要刪除的編號",type="integer",example="1",description="多筆中間用逗號",),

     *       ),
     *   ),),
     *   @OA\Response(response=200,description="回覆",
     *     @OA\JsonContent(type="object",
     *      @OA\Property(property="resultcode",type="integer",description="訊息代碼",),
     *      @OA\Property(property="resultmessage",type="string",description="訊息內容"),
     *     ),)
     *),)
     */

    public function destroy(Request $request) {
        $rows = epost::selectRaw('epost.id');
        $rows->myWhere('id|ININT', $this->data['del'], 'del', 'Y');
        //$rows->delete();
        $rows->chunk(200, function ($rows) {
            foreach ($rows as $rs) {
                $rs->delete();
            }
        });


        $this->jsondata['resultmessage'] = '刪除成功';
        return $this->apiResponse($this->jsondata);
    }
}
