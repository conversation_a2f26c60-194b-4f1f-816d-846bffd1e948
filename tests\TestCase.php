<?php

namespace Tests;

use Illuminate\Foundation\Testing\TestCase as BaseTestCase;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use PF;

abstract class TestCase extends BaseTestCase {
    use CreatesApplication;

    public function setUp(): void {
        parent::setUp();

        // 安全檢查：確保測試環境使用正確的資料庫連接
        $this->ensureTestingDatabase();
    }
    public function tearDown(): void {
        parent::tearDown();
        //$folder = storage_path('views/' . $this->domain->id);
        //\File::deleteDirectory($folder);
    }
    public function checkJson($response) {
        if ($response->getStatusCode() > 400) {
            echo $response->getContent();
            $this->assertTrue(false);
        }
        $json = json_decode($response->getContent(), true);
        if ($json['resultcode'] != 0) {
            echo $json['resultmessage'];
            $this->assertTrue(false);
        }
        $response->assertStatus(200)->assertJson([
            'resultcode' => 0,
        ]);
    }
    public function checkHtml($response) {

        if ($response->getStatusCode() > 400) {
            $msg = $this->parseMessage($response->getContent());
            if (PF::isEmpty($msg)) {
                echo "error msg:" . $response->getContent();
            } else {
                echo "error msg:" . $msg;
            }
            $this->assertTrue(false);
        } else {
            //echo $response->getContent();
            $msg = $this->parseMessage($response->getContent());
            //echo $msg;
            if (\Str::contains($msg, ['Content Error'])) {
                echo $msg;
                $this->assertTrue(false);
            }
        }
    }
    public function checkfile($f) {
        if (\File::exists($f)) {
            $this->assertTrue(true);
            \File::delete($f);
        }
    }
    public function parseMessage($content) {
        if (\Str::contains($content, ['mb-4 lead'])) {
            $msg = \Str::between($content, '<div class="mb-4 lead">', '<button');
            $msg = str_replace("</div>", "", $msg);
            return "Content Error : " . $msg;
        } else if (\Str::contains($content, ['字段是必須的'])) {
            $msg = \Str::between($content, 'msg+=\'', '\r\n\';');
            $msg = str_replace("</div>", "", $msg);
            return "Content Error : " . $msg;
        } else {
            return $content;
        }
    }
    //$jsonbody_order = $this->getHtmlValue($response, '//input[@name="jsonbody_order"]');
    public function getHtmlValue($response, $query) {
        $html = $response->getContent();
        $dom = new \DOMDocument();

        // 禁用 libxml 错误处理并加载 HTML
        libxml_use_internal_errors(true);
        $dom->loadHTML($html);
        libxml_clear_errors();

        // 创建 DOMXPath 实例
        $xpath = new \DOMXPath($dom);

        // 使用 XPath 查询查找具有 name="xx" 属性的 input 元素
        $input = $xpath->query($query)->item(0);

        // 检查元素是否存在并获取其 value 属性值
        if ($input) {
            $value = $input->getAttribute('value');
            //echo "The value of 'xx' is: " . htmlspecialchars($value);
        } else {
            //  echo "Input element with name 'xx' not found.";
        }
        return $value;
    }

    /**
     * 確保測試環境使用正確的資料庫連接
     * 防止意外操作生產資料庫
     */
    protected function ensureTestingDatabase(): void {
        $connection = Config::get('database.default');
        $database = Config::get('database.connections.' . $connection . '.database');

        // 檢查是否為測試環境
        if (app()->environment() !== 'testing') {
            throw new \Exception('測試必須在 testing 環境下執行！目前環境：' . app()->environment());
        }

        // 檢查是否使用測試資料庫連接
        if ($connection !== 'mysql_testing') {
            throw new \Exception('測試必須使用 mysql_testing 連接！目前使用：' . $connection);
        }

        // 檢查資料庫名稱是否為測試資料庫
        if (!in_array($database, ['testing_database', ':memory:']) && !str_contains($database, 'test')) {
            throw new \Exception('資料庫名稱必須包含 test 或為 testing_database！目前資料庫：' . $database);
        }

        // 輸出確認資訊（僅在開發時）
        if (env('APP_DEBUG', false)) {
            echo "\n✅ 測試安全檢查通過：使用 {$connection} 連接到 {$database} 資料庫\n";
        }
    }
}
