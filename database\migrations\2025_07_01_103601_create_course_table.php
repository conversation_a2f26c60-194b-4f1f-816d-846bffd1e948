<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
//php artisan migrate:refresh --path=/database/migrations/_create_course_table.php
return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        if (!Schema::hasTable('course')) {
            Schema::create('course', function (Blueprint $table) {
                //$table->engine = 'MyISAM';
                $table->increments('id')->from(10000)->comment('課程編號');
                $table->unsignedInteger('coach_id')->comment('教練編號');
                $table->string('title', 100)->comment('課程標題');
                $table->text('description')->nullable()->comment('課程描述');
                $table->tinyInteger('difficulty_level')->comment('課程難度'); // 1:初學者 2:初級 3:中級 4:高級 5:專業級
                $table->tinyInteger('course_status')->default(0)->comment('課程狀態'); // 0:草稿 1:發布 2:暫停 9:停用
                $table->decimal('price', 8, 2)->comment('課程價格');
                $table->tinyInteger('duration_hours')->comment('課程時數');
                $table->tinyInteger('max_students')->default(1)->comment('最大學員數');
                $table->string('location', 100)->nullable()->comment('上課地點');
                $table->text('equipment_required')->nullable()->comment('需要設備');
                $table->text('course_outline')->nullable()->comment('課程大綱');
                $table->string('images')->nullable()->comment('課程圖片');
                $table->decimal('rating', 3, 2)->default(0)->comment('課程評分');
                $table->unsignedInteger('total_bookings')->default(0)->comment('預約總數');
                $table->timestamp('created_at')->useCurrent()->comment('建立時間');
                $table->timestamp('updated_at')->useCurrentOnUpdate()->nullable()->comment('更新時間');

                // 建立索引
                $table->index(['coach_id']);
                $table->index(['difficulty_level']);
                $table->index(['course_status']);
                $table->index(['price']);
                $table->index(['rating']);
            });
            \DB::statement("ALTER TABLE course COMMENT 'XX'");
        }
        /*
        $table->unsignedBigInteger('activitysession_id')->comment('場次');
        $table->foreign('activitysession_id')->references('id')->on('activitysession')->onDelete('cascade');

        $table->string('kind',50)->index()->comment('種類');
        $table->mediumText('body')->nullable()->comment('說明');
        $table->dateTime('begindate')->nullable()->comment('開始時間');
        $table->integer('hits')->default(0)->comment('點率次數');
        $table->float('sortnum', 5, 3)->nullable()->comment('排序號碼');
        $table->integer('adminsuer_id')->nullable()->comment('編輯人員');
        $table->string('adminuser_name', 50)->nullable()->comment('編輯人員');
        $table->string('edit_account',50)->comment('修改人');
        $table->string('account',50)->unique();;
        $table->timestamps('reviewed_at')->default('now');
        $table->unique(array('kind', 'kindid'));
        $table->index(array('kind', 'kindid'));
        $table->softDeletes();

        */
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::dropIfExists('course');
    }
};
