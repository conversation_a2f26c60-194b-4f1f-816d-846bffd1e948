<template>
    <div class="container-fluid p-1">
        <nav aria-label="Page navigation example">
            <ul class="pagination justify-content-end">
                <!-- 上一頁按鈕 -->
                <!-- 假設這是第一頁，所以上一頁按鈕是禁用的 -->
                <li class="page-item di" :class="`page-item ${data.current_page == 1 ? 'disabled' : ''}`">
                    <a class="page-link" href="#" @click="onPageChange(state.previous)" aria-label="Previous">
                        <span aria-hidden="true">&laquo;</span>
                    </a>
                </li>

                <!-- 頁碼 (simplePaginate 通常不顯示具體頁碼，只顯示上一頁/下一頁) -->
                <!-- 這裡為了符合圖片，顯示當前頁碼 1 -->
                <li class="page-item active" aria-current="page">
                    <span class="page-link">{{ data.current_page }}</span>
                </li>

                <!-- 下一頁按鈕 -->
                <!-- 假設有下一頁 (根據您提供的 next_page_url 片段) -->
                <li class="page-item" v-if="state.next != null">
                    <!-- 請將 href="#" 替換成實際的 next_page_url -->
                    <a class="page-link" href="#" @click="onPageChange(state.next)" aria-label="Next">
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                </li>
                <!-- 如果沒有下一頁，下一頁按鈕應該是禁用的，像這樣:
    <li class="page-item disabled">
      <a class="page-link" href="#" aria-label="Next">
        <span aria-hidden="true">&raquo;</span>
      </a>
    </li>
    -->
            </ul>
        </nav>
    </div>
    <slot></slot>
</template>
<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch, onErrorCaptured } from 'vue'

import { useWindowSize } from '@vueuse/core'

const emits = defineEmits(['current-change'])
const state = reactive({
    //不用加.value
    previous: 1 as number,
    next: 1 as number
})
const props = defineProps({
    data: {
        type: Object,
        required: true,
        default: 0
    }
})

const onPageChange = async val => {
    //console.log(["onPageChan"]);

    emits('current-change', val)
    window.scrollTo(0, 0)
    try {
        document.querySelector('.el-main').scrollTop = 0
    } catch (error) {}
    try {
        const element = document.querySelector('a[name="top"]')
        if (element) {
            element.scrollIntoView({ behavior: 'smooth', block: 'start' })
        }
    } catch (error) {
        //console.clear();
        //console.log(['error', error])
    }
    //document.querySelector(".el-main").scrollTo({ top: 0, behavior: "smooth" });

    //window.scrollTo({ top: 0, behavior: "smooth" });
}

watchEffect(async () => {
    if (props.data != null) {
        state.previous = props.data.prev_page_url ? new URL(props.data.prev_page_url).searchParams.get('page') : 1
        state.next = props.data.next_page_url ? new URL(props.data.next_page_url).searchParams.get('page') : 1
    }
})
// const currentPage = computed({
//     get() {
//         return props.page
//     },
//     set: val => {
//         //emits('current-change', val)
//     },
// })

onErrorCaptured((err, instance, info) => {
    throw new Error('my-pagination:' + err.message + '\nStack trace:' + err.stack)
})
</script>

<style scoped>
.el-pagination {
    padding: 15px;
}
</style>
