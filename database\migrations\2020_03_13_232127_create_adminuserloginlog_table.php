<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateAdminuserloginlogTable extends Migration {
    /**
     * Run the migrations.
     */
    public function up() {
        if (!Schema::hasTable('adminuserloginlog')) {
            Schema::create('adminuserloginlog', function (Blueprint $table) {
                $table->increments('id');

                $table->string('account', 50)->comment('帳號');

                $table->string('clientip', 255)->comment('IP');
                $table->string('loginstatus', 50)->nullable()->comment('登入狀態');

                $table->dateTime('logouttime')->nullable()->comment('時間');
                //$table->timestamps();

                $table->timestamps();
            });
            \DB::statement("ALTER TABLE adminuserloginlog COMMENT '管理人員登入記錄'");
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down() {
        Schema::dropIfExists('adminuserloginlog');
    }
}
