<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use DB;
use PF;
//縣市
class city1 extends baseModel {


    public $tabletitle = '縣市';
    public $table = 'city1';
    public $primaryKey = 'city1id';

    //欄位必填
    public $rules = [
        'city1title' => 'required',
        'city1id' => 'required',

    ];
    public $fieldInfo = [
'city1title'=>['title'=>'縣市','type'=>'varchar(50)'],//
'sortnum'=>['title'=>'排序號碼','type'=>'double(5,3)'],//
'city1id'=>['title'=>'自動編號','type'=>'bigint(20) unsigned'],//
'online'=>['title'=>'上下架','type'=>'int(11)'],// //上架[1],下架[0],
'entitle'=>['title'=>'英文','type'=>'varchar(100)'],//
'partid'=>['title'=>'地區編號','type'=>'int(11)'],//
];
    //日期欄位的儲存格式。'Y-m-d' or 'U' or ...
    //protected $dateFormat = 'Y-m-d';

    protected $fillable = ['city1title','sortnum','online','entitle','partid']; //可充許傳入的欄位
    protected $guarded = [];   //拒絶修改的欄位(fillable,guarded都設已fillable為準)
    protected $dates = [];

    // 父對子(一對多)
    public function children() {
        return $this->hasMany('App\Models\city2', 'city1title', 'city1title')->orderBy('city2title');
        //return $this->hasMany(city2::class)->where('city2.city1title', '=', 'city1title')->orderBy('city2title');
    }
    public static function boot() {
        parent::boot();

        static::creating(function ($model) {
            //$model->uuid = (string) Uuid::generate();
        });
        self::created(function ($model) {
            //$model->uuid = (string) Uuid::generate();
        });
        static::updating(function ($model) {
            // do some logging
        });
        self::updated(function ($model) {
        });
        static::deleting(function ($model) {
            //  DB::table('city1')->select()
            // ->myWhere('city1id|N', $model->city1id, "city1id", 'Y')
            // ->delete();




        });
        static::deleted(function ($model) {
        });
    }
}
