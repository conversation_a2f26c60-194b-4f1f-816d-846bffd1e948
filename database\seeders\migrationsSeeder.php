<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Repositories\migrationsRepository;

class migrationsSeeder extends Seeder
{
    private $migrationsRepo;

    public function __construct(migrationsRepository $migrationsRepo)
    {
        $this->migrationsRepo = $migrationsRepo;
    }
    /**
     * Run the database seeds.
     */
    public function run()
    {
        
        // $this->migrationsRepo->select()
        // //->myWhere('kind|S', $kind, 'del', 'Y')
        // ->delete();
        

        //$this->faker = \Faker\Factory::create('zh_TW');
        // $items = ['www.yahoo.com.tw', 'www.google.com'];
        // foreach ($items as $k => $v) {
        //     $data = [
        //     'kind' => $kind,
        //     'kindtitle' => $v,
        //   ];
        //   $this->migrationsRepo->create($data);
        // }
    }
}
