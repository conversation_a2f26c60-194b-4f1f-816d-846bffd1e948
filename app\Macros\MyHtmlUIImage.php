<?php

namespace App\Macros;

use PF;
use Exception;
use Intervention\Image\Facades\Image;

/***
"功能名稱":"共用類別-Html Image",
"備註":" ",
"建立時間":"2022-01-18 13:26:42",
 ***/
class MyHtmlUIImage {
    public $thumbratio = 100;

    protected $arr;

    public function __construct($form, $arr) {
        $this->arr = $arr;
    }

    public function createHtml() {
        try {
            $noimg = "";
            $html = "";
            if (isset($this->arr['noimg']) && $this->arr['noimg'] != "") {
                $noimg = $this->arr['noimg'];
            }
            //$storage = base_path('storage/app/public/');
            $storage = base_path('public/');
            //PF::printr($storage);
            $this->arr['filename'] = trim($this->arr['filename']);

            if (substr_count($this->arr['filename'], ',') > 0) {
                $filenameSplit = explode(',', $this->arr['filename']);
                $this->arr['filename'] = $filenameSplit[0];
            }


            $denyarrs = ['<', '>', ',', ':'];

            if (in_array($this->arr['filename'], $denyarrs)) {
                $html .= __('格式錯誤');

                return null;
            }

            if ('/' != substr($this->arr['folder'], -1)) {
                $this->arr['folder'] .= '/';
            }
            if ('' == $this->arr['filename'] && $noimg == "") {
                return null;
            }


            //   if (PHP_OS == 'Linux') {
            $filename = $storage . $this->arr['folder'] . '/' . iconv('utf-8', 'big5', $this->arr['filename']);
            // } else {
            //     $filename = $storage.$this->arr['folder'].'/'.iconv('utf-8', 'big5', $this->arr['filename']);
            // }

            if ($this->arr['filename'] == "" || false == file_exists($filename)) {

                if ('' != $noimg) {

                    $this->arr['folder'] = 'images/';
                    $this->arr['filename'] = $noimg;
                } else {
                    return null;
                }
            }

            $sfolder = $storage . $this->arr['folder'];
            $sfolder = str_replace('//', '/', $sfolder);
            $FName = $sfolder . '/' . $this->arr['filename'];


            if (0 == substr_count($FName, '.') || '' == $FName || is_null($FName)) {
                return null;
            }
            $FName = str_replace('//', '/', $FName);
            //PF_print($this->arr["filename);
            //if (PHP_OS == 'WINNT') {
            //%B4I%A4h%A9%F4

            $hrefname = asset($this->arr['folder'] . urlencode(mb_convert_encoding($this->arr['filename'], 'big5', 'utf-8')));

            // } else {
            //     $hrefname = $storage.$this->arr['folder'].rawurlencode(mb_convert_encoding($this->arr['filename'], 'big5', 'utf-8'));
            //}
            //PF_print($hrefname);
            //\PF::printr($FName);
            //        $html.=substr($FName, strrpos($FName, ".")+1);
            $ext = substr(strtolower($FName), strrpos($FName, '.') + 1);
            switch ($ext) {
                case 'gif':
                case 'jpg':
                case 'jpeg':
                case 'bmp':
                case 'png':
                case 'webp':
                case 'ico':

                    if ($ext != "ico" && (0 == substr_count($this->arr['width'], '%')) && ('' != $this->arr['width'] || '' != $this->arr['height']) && '0' != $this->arr['thumbflag']) {
                        $thumbtaget = '/images/thumb/' . $this->arr['width'] . '_' . $this->arr['height'] . '_';

                        $file = public_path($thumbtaget . iconv('utf-8', 'big5', $this->arr['filename']));

                        if (false == file_exists($file)) {
                            $sourcefile = $storage . $this->arr['folder'] . '/' . iconv('utf-8', 'big5', $this->arr['filename']);
                            $imagedetails = getimagesize($sourcefile);
                            //PF::printr($imagedetails);
                            $orgwidth = $imagedetails[0];

                            $orgheight = $imagedetails[1];
                            // PF::printr($orgwidth);
                            // PF::printr($orgheight);

                            if ((null != $this->arr['width'] && $this->arr['width'] < $orgwidth) || (null != $this->arr['height'] && $this->arr['height'] < $orgheight)) {
                                $fileSize = \File::size($sourcefile);
                                // PF::printr($fileSize);
                                // PF::printr(1024*1024 * 5);
                                if ($fileSize < 1024 * 1024 * 5) {
                                    Image::make($sourcefile)->resize($this->arr['width'], $this->arr['height'], function ($constraint) {
                                        // 若圖片較小，不需要將圖片放大
                                        $constraint->upsize();
                                        // 等比例縮放
                                        $constraint->aspectRatio();
                                    })->save($storage . $thumbtaget . iconv('utf-8', 'big5', $this->arr['filename']), 100);
                                } else {
                                    throw new \CustomException('Thumbnails are not supported if the image file exceeds 5M');
                                }
                            } else {
                                copy($FName, $storage . $thumbtaget . iconv('utf-8', 'big5', $this->arr['filename']));
                            }
                        }
                        if (in_array($ext, ['ico', 'webp'])) {
                            $thumbtaget = $this->arr['folder'];
                        } else {

                            if (PHP_OS == 'WINNT') {
                                $FName = asset($thumbtaget . rawurlencode($this->arr['filename']));
                            } else {
                                $FName = asset($thumbtaget . rawurlencode(mb_convert_encoding($this->arr['filename'], 'big5', 'utf-8')));
                            }
                        }
                        //PF::printr($FName);
                        if ('' == $this->arr['fixedsize']) {
                            $this->arr['width'] = '';
                            $this->arr['height'] = '';
                        }
                    } else {
                        $thumbflag = 0;
                        if (PHP_OS == 'WINNT') {
                            $FName = url($this->arr['folder'] . rawurlencode($this->arr['filename']));
                        } else {
                            $FName = url($this->arr['folder'] . rawurlencode(mb_convert_encoding($this->arr['filename'], 'big5', 'utf-8')));
                        }

                        $thumbflag = 0;
                    }
                    //urlencode(mb_convert_encoding($this->arr["filename, "big5", "utf-8"))
                    if (null != $this->arr['ishref'] && true == $this->arr['ishref']) {
                        $html .= '<a href="' . $hrefname . '" target="_blank">';
                    }

                    if (null != $this->arr['isdisplayname'] && true == $this->arr['isdisplayname']) {
                        $html .= $this->arr['filename'];
                    } else {
                        $html .= '<img  src="' . $FName . '" border="0" align="absmiddle"';
                        foreach ($this->arr as $_key => $_value) {
                            if (false == in_array($_key, ['filename', 'folder', 'filename', 'width', 'height', 'noimg'])) {
                                $html .= ' ' . $_key . '="' . $_value . '"';
                            }
                        }
                        $html .= '/>';

                        if (null != $this->arr['ishref'] && true == $this->arr['ishref']) {
                            $html .= '</a>';
                        }
                    }
                    break;
                case 'mp4':
                    if ('' == $this->arr['autostart']) {
                        $this->arr['autostart'] = 0;
                    }
                    $html . '<video width="' . $this->arr['width'] . '" height="' . $this->arr['height'] . '" controls ';
                    if ('1' == $this->arr['autoStart']) {
                        $html .= 'autoplay';
                    }
                    $html .= '>';
                    $html .= '<source src="' . $FName . '" type="video/mp4">';
                    $html .= 'Your browser does not support the video tag.';
                    $html .= '</video>';
                    break;
                default:
                    //$html .= __('檔案下載').':<a href="'.$hrefname.'" target="_blank">';
                    $html .= '<a href="' . $hrefname . '" target="_blank">';
                    if ('' != $this->arr['displayname']) {
                        $html .= $this->arr['displayname'];
                    } else {
                        $html .= '<img width="30" src="' . asset('/') . '/images/icon/' . substr(strrchr(strrchr($this->arr['filename'], '.'), '.'), 1) . '.gif">';
                    }
                    $html .= '</a>';
            }
        } catch (\Exception $e) {
            $html .= $e->getMessage();
        }

        return $html;
    }
}
