<template>
    <div v-loading="http.getLoading()">
        <my-breadcrumb id="adminuserloginlog" refs="breadcrumb"></my-breadcrumb>
        <my-search
            :searchNames="{
                '': '全部',
                'adminuserloginlog.account': '帳號',
                'adminuserloginlog.clientip': 'IP',
                'adminuserloginlog.loginstatus': '登入狀態'
            }"
            :searchDateNames="{
                'adminuserloginlog.logouttime': '時間',
                'adminuserloginlog.created_at': '建立日期'
            }"
            @onSearch="onSearch"
        ></my-search>

        <div class="table-responsive-md">
            <table class="table table-striped table-hover table-bordered table-fixed">
                <thead>
                    <tr>
                        <th width="" @click="onSort('account')" class="sortable">帳號</th>
                        <th width="" @click="onSort('clientip')" class="sortable">IP</th>
                        <th width="" @click="onSort('loginstatus')" class="sortable">登入狀態</th>
                        <th width="" @click="onSort('logouttime')" class="sortable">時間</th>
                        <th width="" @click="onSort('created_at')" class="sortable">建立日期</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="(rs, index) in data.data">
                        <td>
                            {{ rs.account }}
                            <!--帳號-->
                        </td>
                        <td>
                            {{ rs.clientip }}
                            <!--IP-->
                        </td>
                        <td>
                            {{ rs.loginstatus }}
                            <!--登入狀態-->
                        </td>
                        <td>
                            {{ rs.logouttime }}
                            <!--時間-->
                        </td>
                        <td>
                            {{ rs.created_at }}
                            <!--建立日期-->
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <my-pagination
            :total.number="data.total"
            v-model:page="inputs.page"
            @current-change="onPageChange"
        ></my-pagination>
        <my-dialog ref="myDialog" @closed-dialog="closedDialog"> </my-dialog>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, shallowRef } from 'vue'

const http = createHttp() //http套件
const router = useRouter()
const store = useDataStore()
import MyDialog from '@/components/my-dialog.vue' //modal套件
const myDialog = ref('')
//const emits = defineEmits(["close"]); //接受外面送來的觸發的參數
const props = defineProps({
    // bxx_id: {
    //     default: "",
    //     type: Number,
    //     required: true,
    // },
}) //接受外面送來的參數

// 使用 reactive 定義對象
const data = reactive({
    //不用加.value
    data: []
})
const edit = ref('')

const inputs = reactive({
    search: '',
    searchname: '',
    searchdatename: '',
    page: 1,
    sortname: '',
    sorttype: '',
    del: []
})
//分頁事件
function onPageChange(page) {
    inputs.page = page
    getData()
}
//排序事件
const onSort = async column => {
    inputs.page = 1
    inputs.sortname = column
    inputs.sorttype = inputs.sorttype == 'asc' ? 'desc' : 'asc'
    getData()
}

//搜尋事件
const onSearch = async searchdatas => {
    inputs.page = 1
    inputs.sortname = ''
    Object.assign(inputs, searchdatas)
    getData()
}

//接收到modal關閉視窗事件
const closedDialog = (isReload = false) => {
    if (isReload) {
        getData()
    }
}

//下載資料
const getData = async () => {
    try {
        let rep = await http.post('api/admin/adminuserloginlog', inputs)
        //debugger;
        if (rep.resultcode == '0') {
            Object.assign(data, rep.data)
        } else {
            throw new Error(rep.resultmessage)
        }
    } catch (error: any) {
        utils.formElError(error)
        console.error(error)
    } finally {
    }
}
onMounted(async () => {
    getData()
})
</script>
<style scoped></style>
