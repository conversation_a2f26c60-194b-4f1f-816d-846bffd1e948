import { ref } from 'vue';

export const fields = ref([
    {
        label: "訊息種類",
        name: "kindid",
        kind: "my-xmlform",
        props: {
            type: "select",
            xpath: "訊息種類",
        },
        rules: [
            {
                required: true,
                message: "種類 未填",
            },
        ],
        value: "",
        isedit: true,
        islist: true,
        listname: 'kindtitle',
        issearch: true
    },
    {
        label: "標題",
        name: "title",
        kind: "el-input",
        rules: [
            {
                required: true,
                message: "標題 未填",
            },
        ],
        value: "",
        isedit: true,
        islist: true,
        issearch: true

    },
    {
        label: "圖",
        name: "field1",
        kind: "my-upload",
        props: {
            width: "380",
            height: "280",

            folder: "images/news",
        },
        rules: [
            {
                required: false,
                message: "圖 未上傳",
            },
        ],
        value: "",
        isedit: true,
        islist: false
    },
    {
        label: "本文",
        name: "body",
        kind: "my-ckeditor",
        props: {},
        rules: [
            {

                required: true,
                message: "本文 未填",
            },
        ],
        value: "",
        isedit: true,
        islist: false,
        issearch: true
    },
    {
        label: "本文",
        name: "body",
        kind: "el-input",
        props: {
            type: "textarea",
            rows: 10,
        },
        rules: [
            {


                required: true,
                message: "本文 未填",
            },
        ],
        value: "",
        isedit: true,
        islist: false,
        issearch: true
    },
    {
        label: "連結",
        name: "memo",
        kind: "el-input",
        props: { kind: "url", placeholder: "https://www.yahoo.com.tw" },
        rules: [
            {
                required: false,
                message: "連結 未填",
            },
        ],
        value: "",
        isedit: true,
        islist: false
    },


    {
        label: "開始日期",
        name: "begindate",
        kind: "my-dateform",
        props: {
            type: "date",
        },
        rules: [
            {
                required: false,
                message: "建立日期 未填",
            },
        ],
        value: "",
        isedit: true,
        islist: true,
        issearch: true

    },
    {
        label: "點閱數",
        name: "hits",
        kind: "el-input",
        props: {},
        rules: [
            {
                kind: "number",
            },
        ],
        value: "",
        isedit: false,
        islist: true,
        issearch: false
    },
    {
        label: "結束日期",
        name: "closedate",
        kind: "my-dateform",
        props: {
            type: "date",
        },
        rules: [
            {
                required: false,
                message: "建立日期 未填",
            },
        ],
        value: "",
        isedit: true,
        islist: true,
        issearch: true

    },
    {
        label: "順序",
        name: "boardsort",
        kind: "el-input",
        props: {
            type: "number",
        },
        rules: [
            {
                required: false,
                message: "順序 未填",
            },
        ],
        value: "",
        isedit: true,
        islist: true,
        issearch: false,
        memo: '數字小到大'

    },
    {
        label: "建立日期",
        name: "created_at",
        kind: "my-dateform",
        props: {
            type: "date",
        },
        rules: [
            {
                required: false,
                message: "建立日期 未填",
            },
        ],
        value: "",
        islist: true,
        issearch: true

    },
])