# .prettierrc.yaml
semi: false # 在每個語句的末尾添加分號
singleQuote: true # 使用單引號來包裹字符串
trailingComma: none # 不允許在多行數組、對象等結構中添加尾隨逗號
endOfLine: auto # 自動檢測並使用適合當前操作系統的換行符
arrowParens: avoid # 在箭頭函數中，如果只有一個參數，則省略括號
tabWidth: 4 # 設置縮進的寬度為 4 個空格
printWidth: 140 # 設置每行代碼的最大長度為 140 個字符
bracketSpacing: true # 在對象字面量中的大括號內添加空格
vueIndentScriptAndStyle: false # 在 Vue 文件中，不對 <script> 和 <style> 標籤內的內容進行額外的縮進
proseWrap: always # 在格式化 Markdown 或其他類似文本時，始終換行
quoteProps: as-needed # 只有在必要時才對對象的屬性名進行引號包裹
