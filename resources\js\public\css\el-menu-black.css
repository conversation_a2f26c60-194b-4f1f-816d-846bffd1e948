/* 基本樣式 */
.header-container {
    /* display: flex; */
    /* 使用 Flexbox */
    justify-content: space-between;
    /* 左右内容分别置于两侧 */
    align-items: center;
    background-color: #333;
    font-size: 24px;
    overflow: hidden;
    width: 100%;
    /* 清除浮动 */
}

.company-logo {

    color: #fff;
    text-decoration: none;
    font-size: 1.2em;
    white-space: nowrap;

    float: left;
    /* 左浮动 */

    /* 左侧内容宽度 */

    /* 确保内边距和边框不会增加宽度 */
    padding: 10px;
    /* 可选：内边距 */
}
.hamburger-icon {
    float: right;
    /* 右浮动 */

    /* 右侧内容宽度 */
    box-sizing: border-box;
    /* 确保内边距和边框不会增加宽度 */
    padding: 10px;
    /* 可选：内边距 */
}

.el-menu-black {

    flex-wrap: wrap;
    background-color: #333 !important;
    border-bottom: none !important;
    flex: 1;
    display: flex !important;
    justify-content: center !important;
}

/*mobile  */
@media screen and (max-width: 724px) and (min-width: 0) {
    /* .header-container {
        flex-direction: column;
        align-items: flex-start;
    } */
    .pc {
        display: none !important;
    }
    .mobile {
        display: block !important;
    }
    .el-menu-black {
        flex-direction: column;
        align-items: flex-start;
        width: 100%;
    }
    .el-menu-black .el-menu-item,
    .el-menu-black .el-sub-menu {
        width: 100%;
        text-align: left;
    }

    .el-menu--popup {
        width: 100%;
    }
}
/*pc*/
@media screen and (min-width: 725px) {
    .pc {
        display: bloack !important;
    }
    .mobile {
        display: none !important;
    }
}

/* Logo active 狀態 */
.company-logo.is-active {
    color: #409eff !important;
}

.el-menu-black {
    flex-wrap: wrap;
    /* 讓選單項目自動換行 */
    background-color: #333 !important;
    border-bottom: none !important;
    flex: 1;
    display: flex !important;
    justify-content: center !important;
}

/* 當 Logo 激活時，取消所有選單項目的 hover 效果 */
.el-menu-black.logo-active > .el-menu-item:hover,
.el-menu-black.logo-active > .el-sub-menu:hover > .el-sub-menu__title {
    background-color: #333 !important;
    color: #fff !important;

}

/* 正常的選單 hover 效果 */
.el-menu-black > .el-menu-item:hover:not(.is-active),
.el-menu-black > .el-sub-menu:hover > .el-sub-menu__title {
    background-color: #333 !important;
}
.el-menu{
    background-color: #333 !important;
}
.el-menu a,.el-menu a:hover {
    color: #fff !important;
    /* 滑過時仍保持白色 */
}

/* 其他選單樣式保持不變 */
.el-menu-black > .el-menu-item,
.el-menu-black > .el-sub-menu > .el-sub-menu__title {
    color: #fff !important;
    font-size: 20px;
}

.el-menu--popup {
    background-color: #333 !important;
    border: none !important;
}

.el-menu--popup .el-menu-item {
    color: #fff !important;
}

/* 當 Logo 激活時，取消下拉選單的 hover 效果 */
.logo-active .el-menu--popup .el-menu-item:hover {
    background-color: #333 !important;
    /* 深灰色背景 */
    color: #fff !important;
    /* 滑過時字體保持白色 */
}

/* 正常的下拉選單 hover 效果 */
.el-menu--popup .el-menu-item:hover {
    background-color: #333 !important;
}

.el-popper.is-pure {
    background-color: #333 !important;
    border: 1px solid #333 !important;
}

.el-popper.is-pure .el-popper__arrow::before {
    background-color: #333 !important;
    border: 1px solid #333 !important;
}

/* 對次選單背景顏色應用更高優先級 */
.el-menu--popup,
.el-menu--popup .el-menu-item {
    background-color: #333 !important;
    /* 強制黑色背景 */
    color: #fff !important;
    /* 保證文字顏色為白色 */
}

/* 在滑過次選單項目時應用的效果 */
.el-menu--popup .el-menu-item:hover {
    background-color: #333 !important;
    /* 深灰色滑過效果 */
}

/* 激活狀態的背景顏色 */
.el-menu--popup .el-menu-item.is-active {
    background-color: #333 !important;
    /* 激活狀態深灰色背景 */
    color: #fff !important;
    /* 激活狀態字體白色 */
}
/* 針對超連結加強優先級 */
.el-menu--popup .el-menu-item a {
    color: #fff !important;
    /* 強制連結文字為白色 */
}

.el-menu--popup .el-menu-item a:hover {
    color: #fff !important;
    /* 滑過時仍保持白色 */
}

.el-menu--popup .el-menu-item.is-active a {
    color: #fff !important;
    /* 激活狀態連結文字為白色 */
}

@media screen and (max-width: 724px) and (min-width: 0) {
    .pc {
        display: none !important;
    }

    .mobile {
        display: block !important;
    }
}

/*pc*/
@media screen and (min-width: 725px) {
    .pc {
        display: bloack !important;
    }

    .mobile {
        display: none !important;
    }
}
