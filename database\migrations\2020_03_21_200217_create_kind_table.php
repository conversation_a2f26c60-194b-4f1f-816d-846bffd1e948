<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateKindTable extends Migration {
    /**
     * Run the migrations.
     */
    public function up() {
        if (!Schema::hasTable('kind')) {
            Schema::create('kind', function (Blueprint $table) {
                $table->increments('id')->from(10000);

                $table->string('kind', 20)->index()->comment('種類');
                $table->string('kindtitle')->comment('標題');
                for ($i = 1; $i <= 3; ++$i) {
                    $table->string('kindfield' . $i)->nullable()->comment('其他文字欄位' . $i);
                }
                $table->mediumText('kindbody')->nullable()->comment('本文');

                $table->integer('kindint1')->comment('其他數字欄位1');
                $table->integer('kindint2')->comment('其他數字欄位2');
                $table->integer('kindint3')->comment('其他數字欄位3');

                $table->string('alg', 5)->comment('語系');
                $table->float('kindsortnum', 7, 3)->nullable()->comment('排序號碼');

                $table->timestamps();

                $table->unique(array('kind', 'kindtitle'));
            });
            \DB::statement("ALTER TABLE kind COMMENT '種類'");
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down() {
        Schema::dropIfExists('kind');
    }
}
