<?php

namespace Tests\Feature\api\admin;


/***
"功能名稱":"與我聯絡 單位測試",
"資料表":"feedback",
"建立時間":"2024-10-28 08:51:41 ",
 ***/
//command "php artisan test --filter tests/Feature/admin/feedbackTest.php
class feedbackTest extends baseTest {
  public $feedback;
  public function setUp(): void {
    parent::setUp();

    $this->feedback = \App\Models\feedback::factory()->create([
      //  'member_id' => $this->member->id,
    ]); //編輯日期
  }
  /**
   * 測試資料列表功能.
   *
   * @return void
   */
  public function test_列表index與我聯絡() {


    $datas = [

      'header' =>
      array(
        'HTTP_Authorization' => 'Bearer ' . $this->adminuser->api_token,
        'CONTENT_TYPE' => 'application/json',
      ),
      'url' => '/api/admin/feedback',
      'raw' =>
      array(
        'page' => 1,
        'pagesize' => 10,
        'searchname' =>  'name',
        'search' => $this->feedback->name,
      ),
      'post' => NULL,

    ];

    $response = $this->withHeaders($datas['header'])
      ->json('POST', $datas['url'], $datas['raw']);
    // echo $response->getStatusCode();
    //echo "response" . $response->getContent();

    $this->checkJson($response);

    //檢查資料表資料是否存在
    $this->assertDatabaseHas('feedback', [
      'id' => $this->feedback->id,
    ]);
    //檢查回傳資料是否存在
    $response
      ->assertStatus(200)
      ->assertJsonPath('data.data.0.name', $this->feedback->name);
    $jsonArray = $response->json();
    $this->assertGreaterThan(1, count($jsonArray['data']));
  }

  /**
   * 測試資料新增編輯寫入功能.
   *
   * @return void
   */
  public function test_編輯store與我聯絡() {
    \PF::printr(["this->feedback->id", $this->feedback->id]);
    $datas = [

      'header' =>
      array(
        'HTTP_Authorization' => 'Bearer ' . $this->adminuser->api_token,
        'CONTENT_TYPE' => 'application/json',
      ),
      'url' => '/api/admin/feedback/store',
      'raw' =>
      array(
        'name' => $this->myFaker->getName(), //姓名-
        'email' => $this->myFaker->getEmail(), //返回一個隨機郵箱,//電子信箱-
        'tel' => $this->myFaker->getTel(), //電話-
        'mobile' => $this->myFaker->getMobile(), //行動電話-
        'memo' => $this->faker->realText(200), //詢問及意見回饋-
        'retitle' => $this->faker->realText(20), //回覆標題-
        'rebody' => $this->faker->realText(200), //回覆訊息-
        'redate' => $this->myFaker->getDate(), //回覆日期-
        'member_id' => $this->member->id,
        //會員編號-
        'alg' => $this->faker->realText(20), //語系-
        'adminuser_id' => '1', //編輯人員-
        'adminuser_name' => 'admin', //編輯人員-
        'created_at' => now(), //建立日期-
        'id' => $this->feedback->id,

      ),
      'post' => NULL,

    ];

    $response = $this->withHeaders($datas['header'])
      ->json('POST', $datas['url'], $datas['raw']);
    // echo $response->getStatusCode();
    //echo "response" . $response->getContent();

    $this->checkJson($response);
    //檢查資料表資料是否存在
    $this->assertDatabaseHas('feedback', [
      'name'       => $datas['raw']['name'],
      'email'       => $datas['raw']['email'],
      'tel'       => $datas['raw']['tel'],
      'mobile'       => $datas['raw']['mobile'],


    ]);
  }
  /**
   * 測試資料刪除功能.
   *
   * @return void
   */
  public function test_刪除destroy與我聯絡() {

    $this->feedback = \App\Models\feedback::factory()->create([]);
    $datas = [
      'header' =>
      array(
        'HTTP_Authorization' => 'Bearer ' . $this->adminuser->api_token,
        'CONTENT_TYPE' => 'application/json',
      ),
      'url' => '/api/admin/feedback/destroy',
      'raw' =>
      array(
        'del' => $this->feedback->id,
      ),
    ];
    $response = $this->withHeaders($datas['header'])
      ->json('POST', $datas['url'], $datas['raw']);

    // echo $response->getStatusCode();
    echo $response->getContent();

    $this->checkJson($response);
    $response->assertStatus(200)->assertJson([
      'resultcode' => 0,
      'resultmessage' => '刪除成功'
    ]);
    $this->assertDatabaseMissing('feedback', [
      'id'       => $this->feedback->id,
    ]);
  }
}
