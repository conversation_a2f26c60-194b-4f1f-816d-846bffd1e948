<template>
    <div class="inline">
        <el-select v-model="localModelValue1">
            <el-option value="">{{ local1DefaultText }}</el-option>
            <el-option
                :key="rs.id"
                v-for="(rs, index) in local1Options"
                :value="rs.value"
                :label="rs.label"
            >
            </el-option>
        </el-select>

        <template v-if="props.name2 != null">
            <el-select v-model="localModelValue2">
                <el-option value="">{{ local2DefaultText }}</el-option>
                <el-option
                    :key="rs.id"
                    v-for="(rs, index) in local2Options"
                    :value="rs.value"
                    :label="rs.label"
                >
                </el-option>
            </el-select>
        </template>
        <slot></slot>
    </div>
</template>

<script setup lang="ts">
import {
    ref,
    reactive,
    computed,
    onMounted,
    defineProps,
    defineEmits,
    watch,
    getCurrentInstance,
    onErrorCaptured
} from 'vue'

import { createHttp } from '@/utils/http'
const http = createHttp()

const emit = defineEmits(['update:name1', 'update:name2'])
const props = defineProps({
    name1: {
        type: String,
        required: true
    },
    name2: {
        type: String,
        required: true
    },
    api1: {
        type: String,
        required: true
    },
    api2: {
        type: String,
        required: true
    },
    field1: {
        type: String,
        required: true
    },
    field2: {
        type: String,
        required: true
    }
})
let local1Name = ref('')
let local2Name = ref('')
let local1DefaultText = ref('Loading')
let local2DefaultText = ref('Select')

const localModelValue1 = computed({
    get() {
        if (props.name1 != '' && props.name2 != '') {
            fetch2Options(props.name1)
        }
        return String(props.name1)
    },
    set(val) {
        emit('update:name1', String(val) || '')
        //console.clear();
        //console.log(['props.name', props.name1])
        if (val != '') {
            local2DefaultText.value = 'Loading'
            localModelValue2.value = ''
            fetch2Options(val)
        } else {
            local2Options.value = []
        }

        //選擇後觸發,並通知父層
    }
})
const localModelValue2 = computed({
    get() {
        return String(props.name2)
    },
    set(val) {
        emit('update:name2', String(val) || '')
    }
})
let local1Options = ref([])
let local2Options = ref([])
let local3Options = ref([])

const fetch1Options = async () => {
    try {
        let rep = await http.post(props.api1, {}, false)
        if (rep.resultcode == '0') {
            rep.data.forEach(function (rs, index) {
                let value = ''
                let label = ''
                if (Object.keys(rs).length == 1) {
                    value = String(Object.values(rs)[0])
                    label = value
                } else {
                    value = String(Object.values(rs)[0])
                    label = String(Object.values(rs)[1])
                }
                local1Options.value.push({
                    value: '' + value,
                    label: label
                })
            })
        } else {
            throw new Error(rep.resultmessage)
        }

        local1DefaultText.value = 'Select'
        //console.log(["local1Options.value", local1Options.value]);
    } catch (error) {
        console.error('Error fetching options:', error)
    }
}
const fetch2Options = async val => {
    try {
        local2Options.value = []

        local2DefaultText.value = 'Loading'
        let dict = {}
        dict[props.field1] = val

        let rep = await http.post(props.api2, dict, false)
        if (rep.resultcode == '0') {
            rep.data.forEach(function (rs, index) {
                let value = ''
                let label = ''
                if (Object.keys(rs).length == 1) {
                    value = String(Object.values(rs)[0])
                    label = value
                } else {
                    value = String(Object.values(rs)[0])
                    label = String(Object.values(rs)[1])
                }
                local2Options.value.push({
                    value: '' + value,
                    label: label
                })
            })
        } else {
            throw new Error(rep.resultmessage)
        }

        local2DefaultText.value = 'Select'
        //console.log(["local2Options.value", local2Options.value]);
    } catch (error) {
        console.error('Error fetching options:', error)
    }
}

onMounted(async () => {
    fetch1Options()
})

const errorCaptured = async (err, vm, info) => {
    console.error(
        `my-apiselectform Error: ${vm.$options.name};message: ${err.toString()};info: ${info}`
    )
    return false // 可以返回 true 或 false 來決定是否繼續往上傳遞錯誤
}
/*
 <my-apiselectform
            v-model:name1="inputs.city1"
            v-model:name2="inputs.city2"
            @update:name1="console.log($event)"
            @update:name2="console.log($event)"
            field1="city1"
            field2="city2"
            api1="/api/dependentdropdown/city1"
            api2="/api/dependentdropdown/city2"
        />
*/
</script>
