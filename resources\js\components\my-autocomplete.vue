<!-- DateInput.vue -->
<template>
    <ClientOnly fallback-tag="span" fallback="Loading">
        <el-autocomplete
            v-model="localModelValue"
            :fetch-suggestions="querySearchAsync"
            placeholder="Please input"
            :trigger-on-focus="false"
        />
        <slot></slot>
    </ClientOnly>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onErrorCaptured } from 'vue'
import { createHttp } from '@/utils/http'
const http = createHttp()
import { utils } from '@/utils'

// 定义组件属性
const props = defineProps(['modelValue', 'api'])

const emits = defineEmits(['update:modelValue', 'change'])

const state = ref('')

interface LinkItem {
    value: string
    name: string
}

const allInfos = ref<LinkItem[]>([])

let timeout: ReturnType<typeof setTimeout>
const querySearchAsync = (queryString, cb) => {
    //allInfos是怎么来，是从父组件传过来的还是在自己组件调用api接口拿到的还是其他
    //我这里的allInfos是从父组件传过来的，在这里也举例子组件从父组件传值
    let results = allInfos.value
    results = queryString ? results.filter(createFilter(queryString)) : results
    console.log(['results', results])
    //cb是回调函数，返回筛选出的结果数据到输入框下面的输入列表
    cb(results)
}
const createFilter = (queryString: string) => {
    return (data: LinkItem) => {
        return data.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0
    }
}

const handleSelect = (item: Record<string, any>) => {
    emits('update:modelValue', item.id)
    //console.log(item);
}
onMounted(async () => {
    try {
        let rep = await http.post(props.api, {}, false)

        if (rep.resultcode == '0') {
            allInfos.value = rep.data.map(rs => {
                return {
                    value: rs.title,
                    id: '' + rs.id
                }
            })
        } else {
            throw new Error(rep.resultmessage)
        }
    } catch (error) {
        utils.formElError(error)
        console.error(error)
    } finally {
    }
})

const localModelValue = computed({
    get() {
        return props.modelValue
    },
    set: val => {
        emits('update:modelValue', val)
    }
})
const errorCaptured = async (err, vm, info) => {
    console.error(
        `my-autocomplete Error: ${vm.$options.name};message: ${err.toString()};info: ${info}`
    )
    return false // 可以返回 true 或 false 來決定是否繼續往上傳遞錯誤
}
</script>
