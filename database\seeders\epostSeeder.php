<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

/***
"功能名稱":"seeder - 建立上稿系統假資料",
"資料表":"epost",
"備註":" ",
"建立時間":"2022-01-18 17:05:17",
***/
class epostSeeder extends baseSeeder
{
    private $epostRepo;

    public function __construct(epostRepository $boardRepo)
    {
        parent::__construct();
        $this->epostRepo = $epostRepo;
    }

    /**
     * Run the database seeds.
     */
    public function run()
    {
        $this->epostRepo->select()
        //->myWhere('kind|S', $kind, 'del', 'Y')
        ->delete();

        $this->faker = \Faker\Factory::create('zh_TW');
        $items = ['en', 'zh'];
        foreach ($items as $k => $v) {
            $body = <<<EOF
            test
            EOF;
            $data = [
            'epostid' => 'insideabout',
            'epostbody' => $body,
            'alg' => $v,
          ];
            $this->epostRepo->create($data);
        }
    }
}
