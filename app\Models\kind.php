<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

//類別
class kind extends baseModel
{
    public $tabletitle = '類別';
    public $table = 'kind';
    public $primaryKey = 'id';

    //欄位必填
    public $rules = [
        'kindtitle' => 'required',
'kind' => 'required',
    ];
    public $fieldInfo = [
'id'=>['title'=>'自動編號','type'=>'int(10) unsigned'],//
'kind'=>['title'=>'種類','type'=>'varchar(20)'],//
'kindtitle'=>['title'=>'標題','type'=>'varchar(190)'],//
'kindfield1'=>['title'=>'其他文字欄位1','type'=>'varchar(190)'],//
'kindfield2'=>['title'=>'其他文字欄位2','type'=>'varchar(190)'],//
'kindfield3'=>['title'=>'其他文字欄位3','type'=>'varchar(190)'],//
'kindbody'=>['title'=>'本文','type'=>'mediumtext'],//
'kindint1'=>['title'=>'其他數字欄位1','type'=>'int(11)'],//
'kindint2'=>['title'=>'其他數字欄位2','type'=>'int(11)'],//
'kindint3'=>['title'=>'其他數字欄位3','type'=>'int(11)'],//
'alg'=>['title'=>'語系','type'=>'varchar(5)'],//
'kindsortnum'=>['title'=>'排序號碼','type'=>'double(7,3)'],//
'created_at'=>['title'=>'建立時間','type'=>'timestamp'],//
'updated_at'=>['title'=>'編輯時間','type'=>'timestamp'],//
];
    //日期欄位的儲存格式。'Y-m-d' or 'U' or ...
    //protected $dateFormat = 'Y-m-d';
    protected $fillable = ['kind','kindtitle','kindfield1','kindfield2','kindfield3','kindbody','kindint1','kindint2','kindint3','alg','kindsortnum','created_at','updated_at']; //可充許傳入的欄位
    protected $guarded = [];
    protected $dates = ['created_at','updated_at'];

    /*Relations start*/
    /*Relations end*/
    public static function boot() {
        parent::boot();

        static::creating(function ($model) {
            //$model->uuid = (string) Uuid::generate();
        });
        self::created(function ($model) {
            //$model->uuid = (string) Uuid::generate();
        });
        static::updating(function ($model) {
            // do some logging
        });
        self::updated(function ($model) {
        });
        static::deleting(function ($model) {
        });
        static::deleted(function ($model) {
            /*Del Relations start*/
            /*Del Relations end*/
        });
    }
}
