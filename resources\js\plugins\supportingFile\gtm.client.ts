// plugins/gtm.client.ts

// 定義全域類型
declare global {
    interface Window {
        dataLayer: any[]
    }
}

export default defineNuxtPlugin(nuxtApp => {
    // 只在客戶端執行
    if (!process.client) return

    // 從運行時配置中獲取 GTM ID
    const config = useRuntimeConfig()
    const GTM_ID = config.public.GTM_ID as string

    if (window.location.pathname.startsWith('/admin')) {
        return
    }
    // 確保 GTM ID 存在
    if (!GTM_ID) {
        console.error('[GTM] ERROR: Missing GTM_ID in runtimeConfig.public')
        console.log('[GTM] Available config:', config.public)
        return
    }

    console.log(`[GTM] Starting initialization with ID: ${GTM_ID}`)

    // 1. 首先初始化 dataLayer 並添加配置
    window.dataLayer = window.dataLayer || []

    // 2. 關鍵設置: 告訴 GTM 這是一個 SPA，啟用路由監聽
    window.dataLayer.push({
        'gtm.start': new Date().getTime(),
        event: 'gtm.js',
        // 非常重要: 這會讓 GTM 自動監聽 history 變化
        vars: {
            routeTracking: true
        }
    })

    console.log('[GTM] Initial dataLayer push completed')

    // 3. 插入 GTM 腳本 - 使用標準方法
    const initGtm = () => {
        try {
            // 避免重複初始化
            if (document.querySelector(`script[src*="googletagmanager.com/gtm.js?id=${GTM_ID}"]`)) {
                console.log('[GTM] Already initialized')
                return
            }

            // A. 使用推薦的 GTM 初始化代碼
            const script = document.createElement('script')
            script.async = true
            script.src = `https://www.googletagmanager.com/gtm.js?id=${GTM_ID}`

            // 添加載入事件監聽器
            script.onload = () => {
                console.log('[GTM] Script loaded successfully')
            }
            script.onerror = error => {
                console.error('[GTM] Script load error:', error)
            }

            document.head.appendChild(script)
            console.log('[GTM] Script tag added to head')

            // B. 添加 noscript 元素
            const noscript = document.createElement('noscript')
            noscript.innerHTML = `
        <iframe src="https://www.googletagmanager.com/ns.html?id=${GTM_ID}"
        height="0" width="0" style="display:none;visibility:hidden"></iframe>
      `
            document.body.prepend(noscript)

            console.log('[GTM] Initialization completed')
        } catch (error) {
            console.error('[GTM] Initialization error:', error)
        }
    }

    // 4. 路由切換時手動推送事件
    const trackPageView = () => {
        // 排除後台頁面
        if (window.location.pathname.startsWith('/admin')) {
            console.log('[GTM] Skipping admin page tracking')
            return
        }

        try {
            console.log('[GTM] Tracking page view for:', window.location.pathname)

            // B. 再推送標準的頁面瀏覽事件 (部分配置可能依賴這個)
            const pageViewData = {
                event: 'page_view',
                page_title: document.title,
                page_location: window.location.href,
                page_path: window.location.pathname + window.location.search
            }

            window.dataLayer.push(pageViewData)
            console.log('[GTM] Page view event pushed:', pageViewData)
        } catch (error) {
            console.error('[GTM] Error tracking page view:', error)
        }
    }

    // 5. 提供 GTM 推送函數
    const gtmPush = (data: any) => {
        window.dataLayer = window.dataLayer || []
        window.dataLayer.push(data)
        console.log('[GTM] Pushed data:', data)
    }

    // 6. 應用掛載後初始化 GTM
    nuxtApp.hook('app:mounted', () => {
        console.log('[GTM] App mounted')
        initGtm()

        // 首次訪問也推送頁面瀏覽事件
        setTimeout(() => {
            console.log('[GTM] Triggering initial page view')
            trackPageView()
        }, 1000) // 給足夠時間讓 GTM 完全初始化
    })

    // 7. 使用 Vue Router 監聽頁面切換 - 修正版本
    const router = useRouter()
    if (router) {
        router.afterEach((to, from) => {
            console.log('[GTM] Route changed from:', from.path, 'to:', to.path)

            // 等待 DOM 更新
            setTimeout(() => {
                trackPageView()
            }, 100)
        })
    } else {
        console.warn('[GTM] Router not available, falling back to history API')

        // 備用方案：監聽 popstate 事件
        window.addEventListener('popstate', () => {
            console.log('[GTM] Popstate event detected')
            setTimeout(() => {
                trackPageView()
            }, 100)
        })
    }

    // 8. 測試 GTM 連接的函數
    const testGtmConnection = () => {
        window.dataLayer.push({
            event: 'test_event',
            test_time: new Date().toString()
        })
        console.log('[GTM] Test event sent')

        // 特別觸發 history 變化事件
        window.dataLayer.push({
            event: 'gtm.historyChange',
            'gtm.newUrl': window.location.href
        })
        console.log('[GTM] History change event sent')
    }

    // 提供 API
    nuxtApp.provide('gtm', gtmPush)
    nuxtApp.provide('gtmTest', testGtmConnection)

    // 提供檢查 dataLayer 的方法
    nuxtApp.provide('gtmDebug', () => {
        console.log('Current dataLayer:', window.dataLayer)
        console.log('GTM Scripts:', document.querySelectorAll('script[src*="googletagmanager.com"]'))
    })
})
