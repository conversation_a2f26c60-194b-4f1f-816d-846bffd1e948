<template>
    <div class="container-fluid p-1">
        <el-pagination
            v-model:current-page="page"
            v-bind="props"
            background
            :page-size="pagesize"
            :size="paginationSize"
            style="justify-content: right"
            layout="total,prev, pager, next"
            :total="localTotal"
            :pager-count="pagercount"
            @current-change="onPageChange"
            v-if="localTotal > 0"
        />
    </div>
    <slot></slot>
</template>
<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch, onErrorCaptured } from 'vue'

import { useWindowSize } from '@vueuse/core'

const { width, height } = useWindowSize()
const emits = defineEmits(['current-change'])
const page = defineModel('page', { required: true, type: Number, default: 1 })
const props = defineProps({
    total: {
        type: Number,
        required: true,
        default: 0
    },

    pagercount: {
        //頁碼數量
        type: Number,
        default: 5
    },

    pagesize: {
        //每頁數量
        type: Number,
        required: false,
        default: 10
    }
})
type PaginationSize = 'small' | 'default' | 'large'
const paginationSize = computed<PaginationSize>(() => (width.value < 768 ? 'small' : 'default'))
const onPageChange = async val => {
    //console.log(["onPageChan"]);

    emits('current-change', val)
    window.scrollTo(0, 0)
    try {
        document.querySelector('.el-main').scrollTop = 0
    } catch (error) {}
    try {
        const element = document.querySelector('a[name="top"]')
        if (element) {
            element.scrollIntoView({ behavior: 'smooth', block: 'start' })
        }
    } catch (error) {
        //console.clear();
        //console.log(['error', error])
    }
    //document.querySelector(".el-main").scrollTo({ top: 0, behavior: "smooth" });

    //window.scrollTo({ top: 0, behavior: "smooth" });
}
const localTotal = computed({
    get() {
        return props.total
    },
    set: val => {
        emits('current-change', val)
    }
})
// const currentPage = computed({
//     get() {
//         return props.page
//     },
//     set: val => {
//         //emits('current-change', val)
//     },
// })

onErrorCaptured((err, instance, info) => {
    throw new Error('my-pagination:' + err.message + '\nStack trace:' + err.stack)
})
</script>

<style scoped>
.el-pagination {
    padding: 15px;
}
</style>
