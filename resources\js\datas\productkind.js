import { ref } from 'vue';

export const fields = ref([

    {
        label: "標題",
        name: "kindtitle",
        kind: "el-input",
        rules: [
            {
                required: true,
                message: "標題 未填",
            },
        ],
        value: "",
        islist: true,
        isedit: true,
        issearch: true

    },
    {
        label: "順序",
        name: "kindsortnum",
        kind: "el-input",
        type: "number",
        rules: [
            {
                required: false,
                message: "標題 未填",
            },
        ],
        value: "",
        islist: true,
        isedit: true,
        issearch: true

    },

    {
        label: "建立日期",
        name: "created_at",
        kind: "my-dateform",
        type: "date",
        props: {
            kind: "date",
        },
        rules: [
            {
                required: false,
                message: "建立日期 未填",
            },
        ],
        value: "",
        islist: true,
        isedit: false,
        issearch: true

    },
]);