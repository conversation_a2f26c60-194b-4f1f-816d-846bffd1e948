# TASK009 - 教練入口（Coach Zone）

## 任務描述

建立完整的教練入口功能，包含教練註冊申請、證照上傳認證，以及教練後台管理系統。此功能讓教練能夠在平台上提供滑雪教學服務。

### 主要功能內容：

1. 教練註冊與證照上傳
2. 教練後台管理
    - 開課設定
    - 接單通知與學生聯繫
    - 雪幣購買曝光
    - 我的推薦碼

## 狀態

-   [x] 計劃中
-   [ ] 測試單元編寫中
-   [ ] 開發中
-   [ ] 完成

## 驗收標準

-   [ ] 教練註冊申請流程完整
-   [ ] 證照上傳功能正常且安全
-   [ ] 管理員審核機制運作正常
-   [ ] 教練開課設定功能完整
-   [ ] 接單通知系統運作正常
-   [ ] 學生聯繫功能正常
-   [ ] 雪幣曝光購買功能正常
-   [ ] 教練推薦碼功能正常
-   [ ] 教練後台儀表板資訊完整
-   [ ] 所有頁面響應式設計良好

## 注意事項

-   教練資料存放在 coach 資料表
-   證照資料存放在 coachCertification 資料表
-   證照檔案存放在 `public/images/coach/` 目錄
-   教練需要審核通過才能開課
-   教練註冊頁面放在 `member` 目錄（未登入可申請）
-   教練後台頁面需要教練身份驗證
-   接單通知使用 Email 發送
-   雪幣曝光功能與雪幣系統整合
-   推薦碼功能與會員推薦系統整合
-   需要建立教練權限管理機制
