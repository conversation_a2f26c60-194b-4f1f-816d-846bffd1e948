<script type="application/ld+json">

{
    "@context": "https://schema.org",
    "@type": "ProfilePage",
    "dateCreated": "{{ $rs->created_at!="" ? $rs->created_at->toIso8601String() : now()->toIso8601String() }}",
    "dateModified": "{{ $rs->created_at!="" ? $rs->created_at->toIso8601String() : now()->toIso8601String() }}",
    "mainEntity": {
    "@type": "Person",
    "name": "{{ $rs->title }}",
    "alternateName": "",
    "identifier": "",
    "description": "{{ PF::noHtml($rs->description) }}",
    "sameAs": [
        "{{ Request::getUri() }}"
    ]
    }
}

</script>
