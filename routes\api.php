<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

$prefixs = config('app.locales');

foreach ($prefixs as $prefix) {
    Route::group(['middleware' => 'ApiFront', 'prefix' => $prefix . '/member', 'namespace' => 'member'], function () use ($prefix) {
        Route::any(
            '/{controller?}/{action?}/{arg?}',
            function (Request $request, $controller = 'index', $action = 'index', $arg = null) use ($prefix) {
                App::setLocale($prefix);

                return \App::Make('App\\Http\\Controllers\api\member\\' . $controller . 'Controller')->callAction($action, array($arg ?: $request, $request));
            }
        );
    });
    Route::group(['middleware' => 'ApiAuthMember', 'prefix' => $prefix . '/membercenter/', 'namespace' => 'membercenter'], function () use ($prefix) {
        Route::any(
            '/{controller?}/{action?}/{arg?}',
            function (Request $request, $controller = 'index', $action = 'index', $arg = null) use ($prefix) {
                App::setLocale($prefix);

                return \App::Make('App\\Http\\Controllers\api\membercenter\\' . $controller . 'Controller')->callAction($action, array($arg ?: $request, $request));
            }
        );
    });

    Route::group(['middleware' => 'ApiFront', 'prefix' => $prefix], function () use ($prefix) {
        Route::match(['get', 'post'], '/', '\App\Http\Controllers\indexController@index');
        Route::any('/{controller?}/{action?}/{arg?}', function (Request $request, $controller = 'index', $action = 'index', $arg = null) use ($prefix) {
            App::setLocale($prefix);

            return \App::Make('App\\Http\\Controllers\\api\\' . $controller . 'Controller')->callAction($action, array($arg ?: $request, $request));
        });
    });
}
// Route::middleware('auth:api')->get('/user', function (Request $request) {
//     return $request->user();
// });

// 滑雪平台 API 路由
Route::group(['prefix' => 'api'], function () {
    // 公開路由 - 不需要登入
    Route::post('member/register', 'api\memberController@register');
    Route::post('member/login', 'api\memberController@login');
    Route::post('course/list', 'api\courseController@list');
    Route::post('course/detail', 'api\courseController@detail');
    Route::post('course/featured', 'api\courseController@featured');
    Route::post('course/coach-courses', 'api\courseController@coachCourses');
    Route::post('coach/list', 'api\coachController@list');
    Route::post('coach/detail', 'api\coachController@detail');
    Route::post('review/course-reviews', 'api\reviewController@courseReviews');
    Route::post('review/coach-reviews', 'api\reviewController@coachReviews');

    // 需要會員登入的路由
    Route::middleware(['auth:sanctum'])->group(function () {
        // 會員相關
        Route::post('member/profile', 'api\memberController@profile');
        Route::post('member/update-profile', 'api\memberController@updateProfile');
        Route::post('member/logout', 'api\memberController@logout');

        // 預約相關
        Route::post('booking/create', 'api\bookingController@create');
        Route::post('booking/list', 'api\bookingController@list');
        Route::post('booking/detail', 'api\bookingController@detail');
        Route::post('booking/cancel', 'api\bookingController@cancel');

        // 評價相關
        Route::post('review/create', 'api\reviewController@create');
        Route::post('review/my-reviews', 'api\reviewController@myReviews');

        // 教練相關（需要教練權限）
        Route::post('coach/apply', 'api\coachController@apply');
        Route::post('coach/my-courses', 'api\coachController@myCourses');
        Route::post('coach/create-course', 'api\coachController@createCourse');
        Route::post('coach/update-course', 'api\coachController@updateCourse');
        Route::post('coach/my-bookings', 'api\coachController@myBookings');
        Route::post('coach/confirm-booking', 'api\coachController@confirmBooking');
    });
});


Route::group(['middleware' => 'ApiAuthAdmin', 'prefix' => 'admin/', 'namespace' => 'admin'], function () {
    Route::any(
        '{controller?}/{action?}/{arg?}',
        function (Request $request, $controller = 'index', $action = 'index', $arg = null) {
            return \App::Make('App\\Http\\Controllers\\api\\admin\\' . $controller . 'Controller')->callAction($action, array($arg ?: $request, $request));
        }
    );
});
Route::group(['middleware' => 'ApiFront', 'prefix' => 'member/', 'namespace' => 'member'], function () {
    Route::any(
        '{controller?}/{action?}/{arg?}',
        function (Request $request, $controller = 'index', $action = 'index', $arg = null) {
            return \App::Make('App\\Http\\Controllers\\api\\member\\' . $controller . 'Controller')->callAction($action, array($arg ?: $request, $request));
        }
    );
});
Route::group(['middleware' => 'ApiAuthMember', 'prefix' => 'membercenter/', 'namespace' => 'membercenter'], function () {
    Route::any(
        '{controller?}/{action?}/{arg?}',
        function (Request $request, $controller = 'index', $action = 'index', $arg = null) {
            return \App::Make('App\\Http\\Controllers\\api\\membercenter\\' . $controller . 'Controller')->callAction($action, array($arg ?: $request, $request));
        }
    );
});

// Route::any('put/{any?}', 'api\putController@index') // 匹配任何 /api/put 開頭的路徑，包含多層次參數
//     ->where('any', '.*');

Route::group(['middleware' => 'ApiFront', 'prefix' => '/'], function () {
    Route::any(
        '{controller?}/{action?}/{arg?}',
        function (Request $request, $controller = 'index', $action = 'index', $arg = null) {
            if (str_contains($action, '.')) {
                return $action . ' - not found.';
            }

            return \App::Make('App\\Http\\Controllers\\api\\' . $controller . 'Controller')->callAction($action, array($arg ?: $request, $request));
        }
    );
});
