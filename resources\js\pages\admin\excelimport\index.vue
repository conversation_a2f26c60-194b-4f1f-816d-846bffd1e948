<template>
    <el-form ref="formEl" :model="inputs" v-loading="http.getLoading()">
        <el-upload
            :headers="state.headers"
            class="upload-demo"
            name="file1"
            accept=".xls,.xlsx"
            :before-upload="beforeUpload"
            :action="utils.getConfig('API_BASE') + url"
            :on-error="handleError"
            :on-success="handleSuccess"
            :show-file-list="false"
            :data="inputs"
        >
            <el-button type="primary">Click to upload</el-button>
        </el-upload>

        <div align="center">
            <button type="button" class="btn btn-warning" @click="http.download(url.replace('excelimportstore', 'excelsample'), inputs)">
                範本
            </button>
            &nbsp;

            <button
                type="button"
                class="btn btn-secondary"
                @click="
                    emits('closed-dialog', true) //router.go(-1)
                "
            >
                返回
            </button>
            只限 xls/xlsx 格式
        </div>

        <div class="table-responsive-md">
            <table class="table table-striped table-hover table-bordered table-fixed">
                <thead>
                    <tr>
                        <th>序號</th>
                        <th>結果</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="(rs, index) in datas">
                        <td>{{ index + 1 }}</td>
                        <td>{{ rs }}</td>
                    </tr>
                </tbody>

                <tbody></tbody>
            </table>
        </div>
    </el-form>
</template>

<script setup lang="ts">
import { ref, reactive, getCurrentInstance, onMounted, computed } from 'vue'

const http = createHttp()
const router = useRouter()
const store = useDataStore()

//const emit = defineEmits(["updateNavTitle"]);
const emits = defineEmits(['closed-dialog']) //接受外面送來的觸發的參數

const props = defineProps({
    inputs: Object,

    url: {
        type: String,
        default: '',
        required: true
    }
})
let datas = ref([]) //要加.value
const state = reactive({
    headers: {
        Authorization: `Bearer ${useDataStore().adminuser.api_token}`
    }
})

// const outtotal = computed({
//     get() {
//         return utils.total(data.value.data, 'outtotal')
//     },
//     set(val) {
//         //選擇後觸發,並通知父層
//     },
// })
const handleSuccess = async (response: any, file: any, uploadFiles: any) => {
    http.setLoading(false)
    if (response.resultcode == '0') {
        datas.value = response.data
    } else {
        utils.alert(response.resultmessage || 'Upload failed. Please try again.', 'error')
    }
}
const beforeUpload = (file: File) => {
    http.setLoading(true)
    datas.value = []
    // 檢查文件類型
    const isExcel =
        file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || file.type === 'application/vnd.ms-excel'

    if (!isExcel) {
        utils.toast('只允許上傳Excel文件', 'error')

        return false
    }

    // 通過檢查則允許上傳
    return true
}
const handleError = (err, file, fileList) => {
    http.setLoading(false)
    utils.toast(err.message, 'error')
}
// defineExpose({
//     inputs,
// }); //父可以使用自己的參數
</script>
<style scoped></style>
