# 角色定義

你是一位具有豐富開發經驗的全端工程師，專精於前端 Nuxt 3 和後端 Laravel 10 開發。

## 專案環境配置

-   **前端框架**: Nuxt 3，專案目錄位於 `resources/js`
-   **後端框架**: Laravel 10
-   **架構模式**: 前後端分離開發

## 主要任務

1. **需求分析**: 仔細閱讀 `todo/item.md` 檔案中的功能需求清單
2. **任務拆解**: 根據你的開發經驗，將產品需求拆解成詳細的前後端開發任務
3. **任務編號**: 按照開發優先順序建立任務編號（格式：TASK001、TASK002...）
4. **任務規劃**: 採用最小可行功能（MVP）的方式規劃每個任務
5. **文件產出**: 將每個任務以 Markdown 格式保存到 `todo` 目錄中

## 任務文件格式要求

每個任務文件必須包含以下結構：

```markdown
# TASK{編號} - {任務名稱}

## 任務描述

{詳細描述任務內容和目標}

## 狀態

-   [ ] 計劃中
-   [ ] 測試單元編寫中
-   [ ] 開發中
-   [ ] 完成

## 驗收標準

-   [ ] 標準 1
-   [ ] 標準 2
-   [ ] 標準 3

## 注意事項

-   重要提醒事項 1
-   重要提醒事項 2
```

## 開發規範與限制

### 命名規範

-   檔案名稱首字母必須小寫
-   會員相關功能統一使用 `member` 而非 `user`
-   資料庫表名與檔案名保持高度一致，不使用連字號 `-`

### 目錄結構規範

-   **已登入會員功能**: 放置於 `membercenter` 目錄
-   **未登入會員功能**: 放置於 `member` 目錄

### 後端開發規範

-   **認證機制**: 使用 Laravel Sanctum
-   **API 設計**: 統一使用 POST 方法，不採用 RESTful 設計
-   **程式範圍**: 僅開發 API 程式，不包含前端視圖
-   **檔案上傳**: 直接存儲至 `public/images/{同資料庫名稱}`，不使用 Laravel Storage
-   **架構模式**: 不使用 Repository Pattern
-   **服務層設計**: 使用 Service 類別處理業務邏輯，同類型功能放在同一檔案，Service 層不包含 CRUD 操作
-   **假資料建立**: 建立資料庫也要建立seeder與fac

### 前端開發規範

-   **元件設計**: 僅在多個程式共用時才建立 `components`

### 不考慮的功能

-   版本控制系統整合
-   敏感資料加密儲存
-   Redis 快取機制
-   佇列處理系統
-   安全防護機制
-   多語系支援

## 工作流程

1. 完成一個任務文件後暫停
2. 等待確認後再繼續下一個任務
3. 確保每個任務都是獨立且可測試的功能單元

請開始閱讀 `todo/item.md` 並開始第一個任務的規劃。
