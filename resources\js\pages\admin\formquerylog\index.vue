<template>
    <my-seo :title="(() => data.title)()" defaultTitle=""> </my-seo>
    <div v-loading="http.getLoading()">
        <my-breadcrumb id="formquerylog" refs="breadcrumb"></my-breadcrumb>
        <my-search
            :searchNames="{
                '': '全部',
                'formquerylog.pagename': '功能名稱',
                'formquerylog.pathinfo': '程式位置',
                'formquerylog.formbody': 'FORM欄位值',
                'formquerylog.querybody': 'get內容',
                'formquerylog.raw': 'raw內容'
            }"
            :searchDateNames="{
                'formquerylog.created_at': '建立日期'
            }"
            @onSearch="onSearch"
        ></my-search>

       

        <template v-if="data.data.length == 0"> No Data </template>
        <div class="table-responsive-md">
            <table class="table table-striped table-hover table-bordered table-fixed">
                <thead>
                    <tr>
                        <th width="" @click="onSort('pagename')" class="sortable">
                            功能名稱/程式位置
                        </th>

                        <th width="" @click="onSort('formbody')" class="sortable">
                            form欄位值/raw
                        </th>

                        <th width="" @click="onSort('created_at')" class="sortable">建立日期</th>
                    </tr>
                </thead>
                <tbody>
                    <tr
                        v-if="utils.isEmpty(data.data) == false"
                        v-for="(rs, index) in data.data"
                        :key="index"
                    >
                        <td>
                            {{ rs.pagename }}
                            <p></p>
                            {{ rs.pathinfo }}
                            <div v-html="utils.vbcrlf(rs.querybody)"></div>
                            <!--功能名稱-->
                        </td>
                        <td>
                            <div v-if="rs.formbody" v-html="rs.formbody"></div>

                            <template v-if="!utils.isEmpty(rs.formbody)">
                                <form method="post" :action="rs.pathinfo" target="_blank">
                                    <template v-for="item in rs.formbody?.split('&')" :key="index">
                                        <textarea
                                            v-if="item.includes('=')"
                                            :key="item"
                                            :name="item.split('=')[0]"
                                            style="display: none"
                                            >{{ item.split('=')[1] }}</textarea
                                        >
                                    </template>
                                    <button class="btn btn-primary">模擬測試</button>
                                </form>
                            </template>
                            <template v-else-if="!utils.isEmpty(rs.raw)">
                                <div v-html="rs.raw"></div>
                                <form method="post" :action="rs.pathinfo" target="_blank">
                                    <textarea style="display: none">{{ rs.raw }}</textarea>

                                    <button class="btn btn-primary">模擬測試</button>
                                </form>
                            </template>

                            <!--FORM欄位值-->
                        </td>
                        <td>
                            {{ rs.created_at }}
                            <!--建立日期-->
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <my-pagination
            :total.number="data.total"
            v-model:page="inputs.page"
            @current-change="onPageChange"
        ></my-pagination>
        <my-dialog ref="myDialog" @closed-dialog="closedDialog"> </my-dialog>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, shallowRef, computed, watch, nextTick } from 'vue'

const http = createHttp()

import { useRouter, useRoute } from 'vue-router'
const router = useRouter()
const route = useRoute()

const store = useDataStore()

import MyDialog from '@/components/my-dialog.vue' //modal套件
const myDialog = ref('')

//const emits = defineEmits(["close"]); //接受外面送來的觸發的參數
const props = defineProps({
    // bxx_id: {
    //     default: "",
    //     type: Number,
    //     required: true,
    // },
}) //接受外面送來的參數

// 使用 reactive 定義對象
let data = reactive({
    data: []
})
const edit = ref('')

const inputs = reactive({
    search: '',
    searchname: '',
    searchdatename: '',
    page: 1,
    sortname: '',
    sorttype: ''
})
//狀態變數
const state = reactive({
    x: ''
})
//分頁事件
const onPageChange = async (page: number): Promise<void> => {
    inputs.page = page
    await getData()
}
//排序事件
const onSort = async (column: string): Promise<void> => {
    inputs.page = 1
    inputs.sortname = column
    inputs.sorttype = inputs.sorttype == 'asc' ? 'desc' : 'asc'
    await getData()
}

//搜尋事件
const onSearch = async (searchdatas: any): Promise<void> => {
    inputs.page = 1
    inputs.sortname = ''
    Object.assign(inputs, searchdatas)
    await getData()
}

//接收到modal關閉視窗事件
const closedDialog = async (isReload: boolean = false): Promise<void> => {
    if (isReload) {
        await getData()
    }
}

//del全選事件
const handleCheckAllChange = async (value: boolean) => {
    data.data.forEach(item => {
        item.del = value ? item.id : 0
    })
}
//刪除事件
const onDel = async (): Promise<void> => {
    try {
        inputs.del = data.data
            .filter(item => item.del !== 0)
            .map(item => item.del)
            .filter(value => value !== null)

        if (inputs.del.length === 0) {
            throw new Error('Please select an item to delete.')
        }

        let rep: any = (await http.post('api/admin/formquerylog/destroy', inputs)) as ApiResponse
        if (rep.resultcode == '0') {
            //utils.alert(rep.resultmessage);
            getData()
        } else {
            throw new Error(rep.resultmessage)
        }
    } catch (error: any) {
        utils.alert(error.message, 'Error', 'error')
        console.error(error)
    }
}

//下載資料
const getData = async (): Promise<void> => {
    try {
        let rep = (await http.post('api/admin/formquerylog', inputs)) as ApiResponse
        //debugger;
        if (rep.resultcode == '0') {
            Object.assign(data, rep.data)
            //console.log(['data', data])
        } else {
            throw new Error(rep.resultmessage)
        }
    } catch (error: any) {
        utils.alert(error.message, 'Error', 'error')
        console.error(error)
    }
}

onMounted(async () => {
    await getData()
})
</script>
<style scoped></style>
