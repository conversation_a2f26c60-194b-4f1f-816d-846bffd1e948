<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// Route::group(['prefix'=>'news'], function(){
//     Route::get('/', 'newsController@index');
//     Route::get('create', 'newsController@create');
//     Route::get('show/{id}', 'newsController@show');
//     Route::get('edit/{id}', 'newsController@edit');
//     Route::get('update/{id}', 'newsController@update');
// });

//會自動產生index,create,store,show,update,destory,edit
//Route::resource('photos', 'PhotoController');
// Route::resource('admin/news', 'admin/newsController');
//except除了create', 'edit
//Route::resource('crud', 'Api\CRUDController')->except('create', 'edit');
// 只充許
// Route::resource('photos', 'PhotoController')->only([
//     'index', 'show'
// ]);
//多筆
// Route::resources([
//     'photos' => 'PhotoController',
//     'posts' => 'PostController',
// ]);
// Route::redirect('/', 'admin');


// Route::group(['middleware' => 'AuthAdmin', 'prefix' => 'admincontrol/', 'namespace' => 'admin'], function () {
//     Route::match(['get', 'post'], '/', 'adminloginController@index');
//     Route::any(
//         '/{controller?}/{action?}/{arg?}',
//         function (Request $request, $controller = 'index', $action = 'index', $arg = null) {
//             if (\Str::contains($controller, ['.'])) {
//                 echo "no found";
//                 exit();
//             }
//             return \App::Make('App\\Http\\Controllers\admin\\' . $controller . 'Controller')->callAction($action, array($arg ?: $request, $request));
//         }
//     );
// });
// Route::group(['middleware' => 'AuthAdmin', 'prefix' => 'admin', 'namespace' => 'admin'], function () {
//     Route::match(['get', 'post'], '/', '\App\Http\Controllers\admin\adminloginController@index');
//     Route::any(
//         '/{controller?}/{action?}/{arg?}',
//         function (Request $request, $controller = 'index', $action = 'index', $arg = null) {
//             if (\Str::contains($controller, ['.'])) {
//                 echo "no found";
//                 exit();
//             }
//             return \App::Make('App\\Http\\Controllers\admin\\' . $controller . 'Controller')->callAction($action, array($arg ?: $request, $request));
//         }
//     );
// });
// $controllers = ['a', 'test', 'demoev', 'sso', 'zip', 'update', 'zipcode'];
// foreach ($controllers as $k => $v) {
//     Route::any(
//         $v . '/{action?}/{arg?}',
//         function (Request $request, $action = 'index', $arg = null) use ($v) {

//             $controllerClass = 'App\\Http\\Controllers\\' . $v . 'Controller';

//             if (class_exists($controllerClass) && method_exists($controllerClass, $action)) {
//                 return \App::Make($controllerClass)->callAction($action, array($arg ?: $request, $request));
//             } else {
//                 abort(404, 'Action not found');
//             }
//         }
//     );
// };

Route::get('/sitemap.xml', function () {
    return redirect('/sitemap');
});

Route::group([
    'middleware' => 'Front',
    'prefix' => '/'
], function () {
    Route::any(
        '/{controller?}/{action?}/{arg?}',
        function (Request $request, $controller = 'index', $action = 'index', $arg = null) {
            if (\Str::contains($controller, ['.', 'build'])) {
                echo "no found";
                exit();
            }
            // 将控制器名称转换为 StudlyCaps 格式
            $controllerClassName = 'App\\Http\\Controllers\\' . $controller . 'Controller';

            // 检查控制器类是否存在
            if (class_exists($controllerClassName)) {
                return \App::make('App\\Http\\Controllers\\' . $controller . 'Controller')->callAction($action, array($arg ?: $request, $request));
            }



            // 直接调用 IndexController 的 index 方法
            return \App::make('App\\Http\\Controllers\\indexController')->index($request, $arg);
        }
    );
});
