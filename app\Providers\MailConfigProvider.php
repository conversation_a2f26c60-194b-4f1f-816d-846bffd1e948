<?php

namespace App\Providers;

use Illuminate\Support\Facades\App;
use Illuminate\Support\ServiceProvider;
use PF;
class MailConfigProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register()
    {
    }

    /**
     * Bootstrap services.
     */
    public function boot()
    {
        // get email view data in provider class

        $config = array(
                        'driver' => env('MAIL_MAILER', 'smtp'),
                        'host' => PF::getConfig('smtp_host'),
                        'port' =>  PF::getConfig('smtp_port'),
                        'username' =>  PF::getConfig('smtp_username'),
                        'password' =>  PF::getConfig('smtp_passowrd'),
                        'encryption' => PF::getConfig('smtp_encryption'),
                        'from' => array('address' => PF::getFromEmail(), 'name' => PF::getConfig('name')),
                    );
              
        \Config::set('mail', $config);
    }
}
