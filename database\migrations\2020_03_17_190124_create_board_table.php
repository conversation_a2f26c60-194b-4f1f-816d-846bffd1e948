<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateBoardTable extends Migration {
    /**
     * Run the migrations.
     */
    public function up() {
        if (!Schema::hasTable('board')) {
            Schema::create('board', function (Blueprint $table) {
                $table->increments('id')->from(10000);
                $table->string('kind')->index()->comment('種類');

                $table->integer('kind_id')->nullable()->comment('種類編號');
                $table->string('title', 500)->comment('標題');
                $table->string('memo', 500)->nullable()->comment('描述');
                $table->mediumText('body')->nullable()->comment('本文');
                for ($i = 1; $i <= 10; ++$i) {
                    $table->string('field' . $i)->nullable()->comment('其他欄位' . $i);
                }
                $table->dateTime('begindate')->nullable()->comment('開始時間');
                $table->dateTime('closedate')->nullable()->comment('結束時間');
                $table->integer('hits')->default(0)->comment('點率次數');
                $table->float('boardsort', 7, 3)->nullable()->comment('排序號碼');
                $table->string('location', 50)->nullable()->comment('位置');
                $table->integer('adminuser_id')->nullable()->comment('編輯人員');
                $table->string('adminuser_name', 50)->nullable()->comment('編輯人員');
                $table->string('alg', 5)->nullable()->comment('語系');

                $table->timestamps();
                $table->index(array('kind', 'kind_id'));
            });
            \DB::statement("ALTER TABLE board COMMENT '訊息公告'");
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down() {
        Schema::dropIfExists('board');
    }
}
