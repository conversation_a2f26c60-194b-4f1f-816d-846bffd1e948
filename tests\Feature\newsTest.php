<?php

namespace Tests\Feature;

class newsTest extends baseTest {


    public function setUp(): void {
        parent::setUp();

        $this->board = \App\Models\board::factory()->create([
            'kind' => 'news',
        ]);
    }


    public function test_index列表() {

        $datas = [
            'header' =>
            array(
                //'CONTENT_TYPE' => 'multipart/form-data'
                'CONTENT_TYPE' => 'text/html; charset=UTF-8',
            ),
            'url' => '/news/index',

            'post' => array(),

        ];
        $response = $this->withHeaders($datas['header'])->post($datas['url'], $datas['post']);

        // echo $response->getStatusCode();
        // echo "response" . $response->getContent();

        $this->checkHtml($response);
        $response->assertSee($this->board->title);



        $this->assertTrue(true);
    }
}
