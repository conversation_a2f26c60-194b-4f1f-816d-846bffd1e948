<template>
    <my-breadcrumb id="adminuser"></my-breadcrumb>

    <el-form ref="formEl" style="width: 98%" :model="inputs" v-loading="http.getLoading()" @submit.prevent="onSubmit">
        <div class="form-group row">
            <label class="col-md-2">帳號<span class="text-danger p-1">*</span></label>
            <div class="col-md-10">
                <el-form-item
                    prop="account"
                    :rules="[
                        {
                            required: true,
                            message: '帳號 未填'
                        }
                    ]"
                >
                    <el-input v-model="inputs.account" type="text" />
                </el-form-item>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-md-2">姓名<span class="text-danger p-1">*</span></label>
            <div class="col-md-10">
                <el-form-item
                    prop="name"
                    :rules="[
                        {
                            required: true,
                            message: '姓名 未填'
                        }
                    ]"
                >
                    <el-input v-model="inputs.name" type="text" />
                </el-form-item>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-md-2">密碼<span class="text-danger p-1"></span></label>
            <div class="col-md-10">
                <el-form-item
                    prop="password"
                    :rules="[
                        {
                            required: edit == '' ? true : false,
                            message: '密碼 未填'
                        },
                        {
                            min: 8,
                            max: 20,
                            message: '長度在 8 到 20 个字符',
                            trigger: 'blur'
                        }
                    ]"
                >
                    <el-popover
                        placement="top-start"
                        title="密碼規格"
                        :width="400"
                        trigger="hover"
                        content="密碼長度必須為8~20位, 其中必須包含至少一位數字、一位英文，若需有特殊符號僅限於 ! @ # $ % & *"
                    >
                        <template #reference>
                            <el-input
                                v-model="inputs.password"
                                show-password
                                type="password"
                                placeholder="密碼"
                            >
                            </el-input>
                        </template>
                    </el-popover>
                </el-form-item>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-md-2">确認密碼<span class="text-danger p-1"></span></label>
            <div class="col-md-10">
                <el-form-item prop="password_confirmation">
                    <el-input
                        type="password"
                        v-model="inputs.password_confirmation"
                        autocomplete="off"
                    ></el-input>
                </el-form-item>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-md-2">EMAIL<span class="text-danger p-1"></span></label>
            <div class="col-md-10">
                <el-form-item
                    prop="email"
                    :rules="[
                        {
                            required: false,
                            message: 'EMAIL 未填'
                        },
                        {
                            type: 'email',
                            message: 'EMAIL 格式錯誤'
                        }
                    ]"
                >
                    <el-input
                        v-model="inputs.email"
                        type="email"
                        placeholder="ex <EMAIL>"
                    />
                </el-form-item>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-md-2">角色<span class="text-danger p-1">*</span></label>
            <div class="col-md-10">
                <el-form-item
                    prop="role"
                    :rules="[
                        {
                            required: false,
                            message: '角色 未選'
                        }
                    ]"
                >
                    <my-xmlform
                        @change="console.log($event)"
                        v-model="inputs.role"
                        type="radio"
                        xpath="角色"
                    />
                </el-form-item>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-md-2">開啟<span class="text-danger p-1"></span></label>
            <div class="col-md-10">
                <el-form-item
                    prop="online"
                    :rules="[
                        {
                            required: false,
                            message: '開啟 未填'
                        }
                    ]"
                >
                    <el-switch :active-value="1" :inactive-value="0" v-model="inputs.online" />
                </el-form-item>
            </div>
        </div>
        <div class="form-group row" v-if="edit != ''">
            <label class="col-md-2">登入錯誤次數<span class="text-danger p-1"></span></label>
            <div class="col-md-10">
                <el-form-item
                    prop="failcount"
                    :rules="[
                        {
                            required: true,
                            message: '登入錯誤次數 未填'
                        }
                    ]"
                >
                    <el-input v-model.number="inputs.failcount" type="number" />
                </el-form-item>
            </div>
        </div>

        <div align="center">
            <button type="submit" class="btn btn-primary">確定</button>
             
            <button type="reset" class="btn btn-secondary" @click="formEl.resetFields()">取消</button>
             
            <button type="button" class="btn btn-secondary" @click="emits('closed-dialog', false)">返回</button>
        </div>
    </el-form>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick, onMounted, computed } from 'vue'

const http = createHttp()
const router = useRouter()
const route = useRoute()
const store = useDataStore()

//const emit = defineEmits(["updateNavTitle"]);
const emits = defineEmits(['closed-dialog']) //接受外面送來的觸發的參數
const props = defineProps({
    edit: {
        default: ''
    }
}) //接受外面送來的參數

const formEl = ref(null)
// 使用 reactive 定義對象
const datas = ref([])
const inputs = reactive({
    account: '',
    name: '',
    password: '',
    email: '',
    role: '',
    online: '',
    failcount: ''
})

const templetedata = reactive({})
const getData = async () => {
    try {
        let rep = await http.post('api/admin/adminuser/show', {
            id: props.edit
        })
        if (rep.resultcode == '0') {
            Object.assign(inputs, rep.data)
            // Object.keys(rep.data).forEach((key) => {
            //     inputs[key] = rep.data[key];
            // });
            //console.log(["inputs", inputs]);
        } else {
            throw new Error(rep.resultmessage)
        }
    } catch (error: any) {
        utils.message(error.message)
        console.error(error)
    }
}
const onSubmit = async (): Promise<void> => {
    if (!formEl.value) return

    try {
        const valid = await formEl.value.validate()
        if (!valid) return // 直接返回，无需再嵌套try

            let rep = await http.post('api/admin/adminuser/store', inputs)

            if (rep.resultcode == '0') {
                utils.toast(rep.resultmessage)
                //router.go(-1);
            //router.push("/member/login");
                emits('closed-dialog', true)
            } else {
                throw new Error(rep.resultmessage)
            }
        } catch (error: any) {
            console.error(error)
        utils.formElError(error)
        }
    }
const validatePasswordConfirmation = (rule: any, value: string, callback: (error?: Error) => void) => {
    if (inputs.password != '' && value !== inputs.password) {
        callback(new Error('密碼不匹配'))
    } else {
        callback()
}
}
// const outtotal = computed({
//     get() {
//         return utils.total(data.value.data, 'outtotal')
//     },
//     set(val) {
//         //選擇後觸發,並通知父層
//     },
// })
onMounted(async () => {
    //document.addEventListener("keyup", onSubmit);
    if (typeof props.edit != 'undefined') {
        if (props.edit != '') {
            await getData()
        }
    }
})
// defineExpose({
//     inputs,
// }); //父可以使用自己的參數
</script>
<style scoped></style>
