<template>
    <div v-loading="http.getLoading()">
        <my-breadcrumb :id="kind" refs="breadcrumb"></my-breadcrumb>

        <my-search
            :searchNames="searchNames"
            :searchDateNames="searchDateNames"
            @onSearch="onSearch"
        ></my-search>

        <div class="d-flex justify-content-between">
            <div v-if="[999].includes(store.adminuser?.role)">
                <my-buttondel @click="onDel"></my-buttondel>
            </div>
            <div></div>
            <div>
                <my-buttonadd
                    @click="myDialog.open('admin/kind/edit', { ...props }, 0)"
                ></my-buttonadd>
            </div>
        </div>
        <div class="table-responsive-md">
            <table class="table table-striped table-hover table-bordered table-fixed">
                <thead>
                    <tr>
                        <th align="center" width="90">
                            <el-checkbox
                                @change="handleCheckAllChange"
                                v-show="[999].includes(store.adminuser?.role)"
                            ></el-checkbox>
                        </th>
                        <th
                            width=""
                            @click="onSort(rs.name)"
                            class="sortable"
                            v-for="(rs, index) in fields"
                        >
                            {{ rs.label }}
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="(rs, index) in data.data">
                        <td valign="top" title="Edit" align="center">
                            <el-checkbox
                                v-model="rs.del"
                                :true-value="rs.id"
                                v-if="[999].includes(store.adminuser?.role)"
                            ></el-checkbox>
                            &nbsp;
                            <button
                                type="button"
                                class="btn btn-success btn-sm"
                                @click="
                                    myDialog.open('admin/kind/edit', { ...props, edit: rs.id }, {})
                                "
                            >
                                <i class="fa fa-edit" aria-hidden="true"></i>
                            </button>
                        </td>
                        <td v-for="(field, index2) in fields">
                            <template v-if="field.kind == 'my-xmlform'">
                                {{ utils.getXmlSearch(field.label, rs[field.name]) }}
                            </template>
                            <template v-else-if="field.kind == 'my-dateform'">
                                {{ utils.formatDate(rs[field.name]) }}
                            </template>
                            <template v-else>
                                {{
                                    typeof field.listname != 'undefined'
                                        ? rs[field.listname]
                                        : rs[field.name]
                                }}
                            </template>
                        </td>
                    </tr>
                </tbody>

                <tbody></tbody>
            </table>
        </div>

        <my-pagination
            :total.number="data.total"
            v-model:page="inputs.page"
            @current-change="onPageChange"
        ></my-pagination>

        <my-dialog ref="myDialog" @closed-dialog="closedDialog"> </my-dialog>
    </div>
</template>

<script setup lang="ts">
import {
    ref,
    reactive,
    onMounted,
    getCurrentInstance,
    defineAsyncComponent,
    shallowRef,
    watch
} from 'vue'

import { createHttp } from '@/utils/http' //http套件
const http = createHttp() //http套件

const router = useRouter()
const route = useRoute()

const store = useDataStore()

import MyDialog from '@/components/my-dialog.vue' //modal套件
const myDialog = ref('')

const searchNames = reactive({})
const searchDateNames = reactive({})
//const emits = defineEmits(["close"]); //接受外面送來的觸發的參數
const props = defineProps({
    kind: {
        default: ''
    }
}) //接受外面送來的參數

// 使用 reactive 定義對象
const data = reactive({
    //不用加.value
    data: []
})
const fields = ref([])
const edit = ref('')

const inputs = reactive({
    search: '',
    searchname: '',
    searchdatename: '',
    page: 1,
    sortname: '',
    sorttype: '',
    del: [],
    kind: route.query?.idkind
})
//分頁事件
function onPageChange(page) {
    inputs.page = page
    getData()
}
//排序事件

//排序事件
const onSort = async column => {
    inputs.page = 1
    inputs.sortname = column
    inputs.sorttype = inputs.sorttype == 'asc' ? 'desc' : 'asc'
    await getData()
}

//搜尋事件
const onSearch = async searchdatas => {
    inputs.page = 1
    inputs.sortname = ''
    Object.assign(inputs, searchdatas)
    await getData()
}

//接收到modal關閉視窗事件
const closedDialog = (isReload = false) => {
    if (isReload) {
        getData()
    }
}

//del全選事件
const handleCheckAllChange = async value => {
    data.data.forEach(item => {
        item.del = value ? item.id : 0
    })
}
//刪除事件
const onDel = async () => {
    try {
        inputs.del =
            data.data
                .filter(item => item.del !== 0)
                .map(item => item.del)
                .filter(value => value !== undefined && value !== null) || []

        let rep = await http.post('api/admin/kind/destroy', inputs)
        if (rep.resultcode == '0') {
            utils.toast(rep.resultmessage)
            getData()
        } else {
            throw new Error(rep.resultmessage)
        }
    } catch (error) {
        utils.formElError(error)

        console.error(error)
    } finally {
    }
}
//下載資料
const getData = async () => {
    try {
        http.setLoading(true) // 手動設置loading為true
        let rep = await http.post('api/admin/kind', inputs)
        //debugger;
        if (rep.resultcode == '0') {
            Object.assign(data, rep.data)
            //console.clear();
            //console.log(['data.value', data.value])
        } else {
            throw new Error(rep.resultmessage)
        }
    } catch (error) {
        utils.formElError(error)
        console.error(error)
    } finally {
    }
}

watch(
    () => props.kind,
    async newValue => {
        inputs.kind = props.kind
        importModule()
        getData()
    },
    { deep: true, immediate: true } //, immediate: true
)
onMounted(async () => {})

async function importModule() {
    try {
        const modules = import.meta.glob('@/datas/*.js')
        const path = `/datas/${route.query.kind}.js`

        if (modules[path]) {
            modules[path]()
                .then(module => {
                    fields.value = module.fields._rawValue.filter(rs => rs.islist == true)

                    fields.value.forEach(rs => {
                        if (rs && rs.issearch === true) {
                            let name = rs.name
                            if (rs.kind == 'my-dateform') {
                                searchDateNames[name] = rs.label
                            } else {
                                searchNames[name] = rs.label
                            }
                        }
                    })
                })
                .catch(error => {
                    throw new Error(error.message)
                })
        } else {
            throw new Error(`Module ${path} does not exist`)
        }
    } catch (error) {
        utils.formElError(error)
    }
}
</script>
<style scoped></style>
