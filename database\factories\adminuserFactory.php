<?php

namespace Database\Factories;


use Illuminate\Database\Eloquent\Factories\Factory;

/***
"功能名稱":"管理人員 產生假資料",
"資料表":"adminuser",
"建立時間":"2024-05-11 00:14:18 ",
 ***/
class adminuserFactory extends Factory {

        public $faker;
        public $myfaker;
        /**
         * Define the model's default state.
         *
         * @return array
         */
        public function definition() {
                $this->faker = \Faker\Factory::create('zh_TW');

                $this->myfaker = new \Database\Seeders\myFaker();

                return [
                        "account" => "allen" . rand(1, 100),   //帳號
                        "name" => "管理者" . rand(1, 100), //姓名
                        "password" =>  \Hash::make('a123456789'),   //密碼 , //密碼


                        "email" => "<EMAIL>", //EMAIL


                        "lastlogin_dt" => "2024-03-12 21:39:40", //最後登入時間
                        "lastlogin_ip" => "**************", //最後登入IP
                        "role" => "999", //角色
                        "online" => "1", //開啟
                        "created_at" => "", //建立時間
                        "updated_at" => "2024-03-12 21:39:40", //異動時間
                        "failcount" => "0", //登入錯誤次數
                ];
        }
}
