<?php

namespace App\Http\Controllers\api\admin;

use PF, PT;
use Exception, DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\board;

use App\Http\Controllers\Controller\api\admin;

/***
"功能名稱":"訊息公告",
"資料表":"board",
"建立時間":"2024-06-03 10:08:51 ",
 ***/
class boardController extends Controller {

    private $data;
    private $xmlDoc;


    public function __construct() {

        //$this->limit="xx";
        parent::__construct();
        //將request全部導入到$this->data變數中
        $this->data = PF::requestAll($this->data);
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');

        //$this->data['nav'] = PT::nav($this->data['xmldoc'],"board",$this->data['nav']);
        $this->data['displaynames'] = board::getFieldTitleArray();

        //$this->jsondata['nav'] = PT::nav($this->data['xmldoc'], request()->get('kind'), $this->data['nav']);
    }


    /**
     * @OA\Post(
     *     path="/api/admin/board",security={{"bearerAuth":{}}},operationId="",tags={"後台/訊息公告"},summary="列表",description="",
     *     @OA\RequestBody(required=true,
     *      @OA\JsonContent(
     *      allOf={

     *         @OA\Schema(@OA\Property(property="page",description="頁數",type="integer",example="1",)),
     *         @OA\Schema(@OA\Property(property="pagesize",description="筆數/頁",type="integer",example="10",)),
     *         @OA\Schema(@OA\Property(property="search",description="搜尋",type="string",example="",)),
     *          @OA\Schema(@OA\Property(property="sortname", type="string",description="排序欄位", example="",)),
     *          @OA\Schema(@OA\Property(property="sorttype", type="string",description="排序方式", example="desc",)),
     *         @OA\Schema(@OA\Property(property="searchstartdate",description="開始時間",type="string",example="2021-01-01",)),
     *         @OA\Schema(@OA\Property(property="searchenddate",description="結束時間",type="string",example="2099-12-31",)),
     *     })

     *   ,),

     *   @OA\Response(response=200,description="回覆",
     *     @OA\JsonContent(type="object",
     *      @OA\Property(property="resultcode",type="integer",description="訊息代碼",),
     *      @OA\Property(property="resultmessage",type="string",description="訊息內容"),
     *      @OA\Property(property="data", type="object",
     *          @OA\Property(property="current_page", type="integer",description="目前頁數", ),
     *          @OA\Property(property="total", type="integer",description="總頁數", ),

     *      @OA\Property(property="data",  type="array",
     *      @OA\Items(allOf={
     *         @OA\Schema(ref="#/components/schemas/board"),
     *         @OA\Schema(@OA\Property(property="", type="string",description="", example="") ),
     *     }))

     *      ),)
     * ),)
     */


    public function index(Request $request) {
        $rows = $this->getRows($request);
        //$rows = $rows->take(10);
        //PF::dbSqlPrint($rows);
        //$rows = $rows->get();
        $pagesize = (is_numeric($request->input('pagesize'))  ? $request->input('pagesize') : 10);
        $rows = $rows->paginate($pagesize);
        /*
         foreach ($rows as $key => $rs) {
          unset($rs->password);
          unset($rs->api_token);
          unset($rs->remember_token);
          unset($rs->lastlogin_ip);
         }
         */
        // 顯示sqlcmd
        $this->jsondata['data'] = $rows;
        return $this->apiResponse($this->jsondata);
    }

    public function getRows($request) {
        $rows = \DB::table('board')->selectRaw('board.*,kindtitle');
        $rows->leftjoin('kind', 'kind.id', '=', 'board.kind_id');


        $rows->myWhere('board.kind|S',  $request->input('kind'), "kind", 'Y');

        //依條件搜尋資料的SQL語法
        $rows->myWhere($request->input('searchname'), $request->input('search'), $this->data['displaynames'], 'N');
        //依條件時間搜尋資料的SQL語法
        $rows->myWhere('convert(' . $request->input('searchdatename') . ',DATE)|>=', $request->input('searchstartdate'), $this->fieldnicknames, 'N');
        $rows->myWhere('convert(' . $request->input('searchdatename') . ',DATE)|<=', $request->input('searchenddate'), $this->fieldnicknames, 'N');
        if ($request->input('sortname')) {
            $rows->orderBy($request->input('sortname'), $request->input('sorttype') == "desc"  ? $request->input('sorttype') : "asc");
        } else {
            $rows->orderByRaw('board.id desc');
        }
        return $rows;
    }


    /**
     * @OA\Post(
     *     path="/api/admin/board/show",security={{"bearerAuth":{}}},operationId="",tags={"後台/訊息公告"},summary="單筆顯示",description="",
     *     @OA\RequestBody(required=true,
     *      @OA\JsonContent(
     *      allOf={

     *         @OA\Schema(@OA\Property(property="id",description="編號",type="integer",example="1",)),

     *     })
     *   ,),

     *   @OA\Response(response=200,description="回覆",
     *     @OA\JsonContent(type="object",
     *      @OA\Property(property="resultcode",type="integer",description="訊息代碼",),
     *      @OA\Property(property="resultmessage",type="string",description="訊息內容"),

     *      @OA\Property(property="data", type="object",
     *      allOf={
     *         @OA\Schema(ref="#/components/schemas/board"),
     *         @OA\Schema(type="object",@OA\Property(property="", type="string",description="系列", example="") ),

     *     })

     *     ,)
     *),)
     */


    public function show($request) {


        $rows = \App\Models\board::selectRaw('board.*');
        $rows->where('id', '=', $request->input('id'));
        $rs = $rows->firstOrFail();

        /*
             unset($rs->password);
             unset($rs->api_token);
             unset($rs->remember_token);
             unset($rs->lastlogin_ip);
             */
        $this->jsondata['data'] = $rs;

        return $this->apiResponse($this->jsondata);
    }


    /**
     * @OA\Post(
     *     path="/api/admin/board/store",security={{"bearerAuth":{}}},operationId="",tags={"後台/訊息公告"},summary="新增/編輯",description="編號有值代表編輯,沒有代表新增",
     *     @OA\RequestBody(required=true,

     *      @OA\JsonContent(
     *      allOf={
     *         @OA\Schema(ref="#/components/schemas/board"),
     *         @OA\Schema(type="object",@OA\Property(property="", type="string",description="系列", example="") ),
     *     })

     *   ,),
     *   @OA\Response(response=200,description="回覆",
     *     @OA\JsonContent(type="object",
     *      @OA\Property(property="resultcode",type="integer",description="訊息代碼",),
     *      @OA\Property(property="resultmessage",type="string",description="訊息內容"),
     *        @OA\Property(property="data", type="object",
     *         allOf={
     *             @OA\Schema(@OA\Property(property="id", type="integer",description="編號", example="10101") ),
     *         }
     *        )
     *     ),)
     *),)
     */


    public function store(Request $request) {

        $edit = $request->input('id');
        //FIXME 那些欄位為必填判斷
        $validators = null;
        if ($validators != null) {
            $validator = \Validator::make($request->all(), $validators);
            $validator->setAttributeNames($this->data['displaynames']);
            if ($validator->fails()) {
                throw new \CustomException(implode(',', $validator->messages()->all()));
            }
        }
        $inputs = $request->all();

        //FIXME 檔案上傳取得值
        $upload = new \App\Libraries\UploadFile();
        $upload->request = $request;
        $upload->inputs = $inputs;
        $upload->folder = 'images/' . $request->input('kind');
        $upload->width = '1024';
        $upload->height = '1204';
        //$upload->limitext = config('app.FileLimit');
        $inputs = $upload->execute();


        if ('' == $edit) {

            $edit = board::create($inputs)->id;

            $this->jsondata['resultmessage'] = '新增成功';
        } else {
            //PF::printr($inputs); exit();
            $rows = board::selectRaw('board.*');
            $rows->myWhere('id|N', $edit, 'edit', 'Y');
            $rs = $rows->firstOrFail();
            $rs->update($inputs);

            $this->jsondata['resultmessage'] = '更新成功';
        }
        $this->jsondata['data']['id'] = $edit;
        return $this->apiResponse($this->jsondata);
    }
    /**
     * @OA\Post(
     *     path="/api/admin/board/destroy",security={{"bearerAuth":{}}},operationId="",tags={"後台/訊息公告"},summary="刪除",description="",
     *   @OA\RequestBody(required=true,@OA\MediaType(mediaType="application/json",@OA\Schema(
     *         @OA\Property(property="del",description="要刪除的編號",type="integer",example="1",description="多筆中間用逗號",),

     *       ),
     *   ),),
     *   @OA\Response(response=200,description="回覆",
     *     @OA\JsonContent(type="object",
     *      @OA\Property(property="resultcode",type="integer",description="訊息代碼",),
     *      @OA\Property(property="resultmessage",type="string",description="訊息內容"),
     *     ),)
     *),)
     */

    public function destroy(Request $request) {
        $rows = board::selectRaw('board.id');
        $rows->myWhere('id|ININT', $this->data['del'], 'del', 'Y');
        //$rows->delete();
        $rows->chunk(200, function ($rows) {
            foreach ($rows as $rs) {
                $rs->delete();
            }
        });


        $this->jsondata['resultmessage'] = '刪除成功';
        return $this->apiResponse($this->jsondata);
    }
}
