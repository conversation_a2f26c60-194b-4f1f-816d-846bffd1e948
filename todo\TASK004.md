# TASK004 - 教練預約與預約管理系統

## 任務描述

建立完整的教練預約功能，包含會員預約教練課程、預約流程管理、教練接單系統，以及預約記錄管理。此功能是平台的核心商業邏輯，連接會員與教練之間的
服務交易。

### 主要功能內容：

1. 會員預約教練課程功能
2. 教練課程時間設定與管理
3. 預約確認與取消機制
4. 預約記錄查詢與管理
5. 教練接單通知系統
6. 預約狀態追蹤功能

## 狀態

-   [x] 計劃中
-   [ ] 測試單元編寫中
-   [ ] 開發中
-   [ ] 完成

## 驗收標準

-   [ ] 會員可以選擇教練並預約可用時段
-   [ ] 教練可以設定開放預約的時間與課程
-   [ ] 預約確認機制運作正常
-   [ ] 預約取消功能運作正常
-   [ ] 會員可以查看預約記錄與狀態
-   [ ] 教練可以查看接單記錄與管理預約
-   [ ] 預約通知系統運作正常
-   [ ] 預約衝突檢查機制正常運作
-   [ ] 響應式設計在各裝置正常顯示

## 注意事項

-   預約資料存放在 booking 資料表
-   課程資料存放在 course 資料表
-   需要建立預約時間衝突檢查機制
-   預約狀態：待確認、已確認、已完成、已取消
-   教練需要手動確認預約（非自動確認）
-   取消預約需要考慮取消政策和時間限制
-   已登入會員才能進行預約
-   預約頁面放在 `membercenter` 目錄（需登入）
-   教練管理頁面放在教練後台區域
