<template>
    <div class="ski-platform-layout">
        <!-- 頁頭導航 -->
        <header class="header">
            <nav class="navbar">
                <div class="container">
                    <!-- 品牌 Logo -->
                    <div class="navbar-brand">
                        <NuxtLink to="/" class="brand-link">
                            <span class="brand-text">滑雪平台</span>
                        </NuxtLink>
                    </div>

                    <!-- 主要導航選單 -->
                    <div class="navbar-nav">
                        <NuxtLink to="/" class="nav-link" exact-active-class="active"> 首頁 </NuxtLink>
                        <NuxtLink to="/courses" class="nav-link" active-class="active"> 課程列表 </NuxtLink>
                        <NuxtLink to="/coaches" class="nav-link" active-class="active"> 教練介紹 </NuxtLink>
                        <NuxtLink to="/about" class="nav-link" active-class="active"> 關於我們 </NuxtLink>
                    </div>

                    <!-- 用戶操作區 -->
                    <div class="navbar-actions">
                        <el-button type="primary"> 登入 </el-button>
                        <el-button> 註冊 </el-button>
                    </div>
                </div>
            </nav>
        </header>

        <!-- 主要內容區域 -->
        <main class="main-content">
            <slot />
        </main>

        <!-- 頁尾 -->
        <footer class="footer">
            <div class="container">
                <div class="footer-content">
                    <div class="footer-section">
                        <h4>滑雪平台</h4>
                        <p>專業的滑雪教學平台，提供優質的滑雪課程與教練服務。</p>
                    </div>
                    <div class="footer-section">
                        <h4>快速連結</h4>
                        <ul class="footer-links">
                            <li><NuxtLink to="/courses">課程列表</NuxtLink></li>
                            <li><NuxtLink to="/coaches">教練介紹</NuxtLink></li>
                            <li><NuxtLink to="/about">關於我們</NuxtLink></li>
                            <li><NuxtLink to="/contact">聯絡我們</NuxtLink></li>
                        </ul>
                    </div>
                    <div class="footer-section">
                        <h4>聯絡資訊</h4>
                        <ul class="contact-info">
                            <li>電話: 02-1234-5678</li>
                            <li>信箱: <EMAIL></li>
                            <li>地址: 台北市信義區滑雪大道123號</li>
                        </ul>
                    </div>
                </div>
                <div class="footer-bottom">
                    <p>&copy; 2024 滑雪平台. All rights reserved.</p>
                </div>
            </div>
        </footer>
    </div>
</template>

<script setup lang="ts">
import { reactive, watch } from 'vue'

const http = createHttp() //http套件
const router = useRouter()
const store = useDataStore()
const route = useRoute()

const inputs = reactive({
    //不用加.value
    title: ''
})

watch(
    () => route.fullPath,
    () => {
        //drawerVisible.value = false
    }
)

// 設置頁面頭部
useHead({
    link: [
        {
            rel: 'stylesheet',
            href: '/css/bootstrap.min.css'
        }
    ]
})
</script>

<style>
.ski-platform-layout {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 頁頭樣式 */
.header {
    background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.navbar {
    padding: 1rem 0;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.navbar-brand {
    display: flex;
    align-items: center;
}

.brand-link {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: white;
    font-weight: bold;
    font-size: 1.5rem;
}

.brand-text {
    color: white;
}

.navbar-nav {
    display: flex;
    gap: 2rem;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
    color: white;
    background: rgba(255, 255, 255, 0.1);
}

.navbar-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* 主要內容區域 */
.main-content {
    flex: 1;
    min-height: calc(100vh - 200px);
}

/* 頁尾樣式 */
.footer {
    background: #2c3e50;
    color: white;
    padding: 3rem 0 1rem;
    margin-top: auto;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h4 {
    margin-bottom: 1rem;
    color: #3498db;
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #3498db;
}

.contact-info {
    list-style: none;
    padding: 0;
}

.contact-info li {
    margin-bottom: 0.5rem;
    color: rgba(255, 255, 255, 0.8);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.6);
}

/* 響應式設計 */
@media (max-width: 768px) {
    .navbar-nav {
        display: none;
    }

    .container {
        padding: 0 1rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
}

/* 動畫效果 */
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}
</style>
