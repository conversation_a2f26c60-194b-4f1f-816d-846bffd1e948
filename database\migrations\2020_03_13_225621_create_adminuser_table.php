<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateAdminuserTable extends Migration {
    /**
     * Run the migrations.
     */
    public function up() {
        if (!Schema::hasTable('adminuser')) {
            Schema::create('adminuser', function (Blueprint $table) {
                $table->increments('id')->from(10000);
                $table->string('account', 50)->unique()->comment('帳號');
                $table->string('name', 50)->unique()->comment('姓名');
                $table->string('password', 100)->comment('密碼');

                $table->string('email', 255)->nullable()->comment('電子信箱');
                $table->integer('role')->nullable()->comment('角色');
                $table->string('limits')->nullable()->comment('權限');
                $table->dateTime('lastlogin_dt')->nullable()->comment('最後登入時間');
                $table->ipAddress('lastlogin_ip')->nullable()->comment('最後登入IP');
                //$table->string('linenotify_token')->nullable()->comment('linenotify token');

                $table->tinyInteger('online')->nullable()->default('1')->comment('是否核可');
                $table->integer('failcount')->default(0)->comment('登入錯誤次數');
                $table->timestamps();
            });
            \DB::statement("ALTER TABLE adminuser COMMENT '管理人員'");
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down() {
        Schema::dropIfExists('adminuser');
    }
}
