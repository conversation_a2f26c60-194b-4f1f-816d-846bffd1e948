/**
 * @license Copyright (c) 2003-2013, CKSource - <PERSON><PERSON>. All rights reserved.
 * For licensing, see LICENSE.html or http://ckeditor.com/license
 */

CKEDITOR.editorConfig = function (config) {
	config.font_names =
		'細明體;微軟正黑體;Arial;Helvetica;sans-serif;Comic Sans MS;cursive;Courier New; Courier;monospace;Georgia;serif;Lucida Sans Unicode; Lucida Grande, sans-serif;Tahoma; Geneva; sans-serif;Times New Roman;Times;serif;Trebuchet MS; Helvetica, sans-serif;Verdana; sans-serif';
	config.language = 'zh-tw';
	config.toolbar = 'Full';
	/*
	config.toolbar = 'MXICToolbar';
	config.toolbar_MXICToolbar =
	[
	['Bold','Italic','Underline','Strike','-','Subscript','Superscript'],
	['Font','FontSize'],
	['TextColor','BGColor']

	];
	*/
	config.toolbar_basic = [
		['Bold', 'Italic', 'Underline', 'Strike', '-', 'Subscript', 'Superscript'],
		['Font', 'FontSize'],
		['TextColor', 'BGColor']
	];
	config.forceSimpleAmpersand = true;
	// Remove some buttons, provided by the standard plugins, which we don't
	// need to have in the Standard(s) toolbar.
	config.removeButtons = 'Underline,Subscript,Superscript,Flash,Form,Checkbox,Radio,TextField,Textarea,Select,Button,ImageButton,HiddenField';
	config.filebrowserBrowseUrl = '../ckfinder/ckfinder.html';

	config.filebrowserImageBrowseUrl = '../../admin/ckeditor/upload?type=Images';
	config.filebrowserUploadUrl = '../../admin/ckeditor/upload?type=Files';
	config.filebrowserImageUploadUrl = '../../admin/ckeditor/upload?type=Images';

	// Se the most common block elements.
	config.format_tags = 'p;h1;h2;h3;pre';
	config.fontSize_sizes =
		'8/8px;9/9px;10/10px;11/11px;12/12px;13/13px;14/14px;16/16px;18/18px;20/20px;22/22px;24/24px;26/26px;28/28px;36/36px;48/48px;72/72px';
	config.width = '100%';
	config.height = 500;
	config.allowedContent = true;
	config.extraAllowedContent = 'i;span;ul;li;table;td;style;*[id];*(*);*{*}';
	config.ignoreEmptyParagraph = false;
	config.enterMode = CKEDITOR.ENTER_BR;
	config.shiftEnterMode = CKEDITOR.ENTER_P;
	config.entities = false;
	config.allowedContent = true;
	config.entities_processNumerical = true;
	//config.protectedSource = [/\r|\n/g];
	//config.protectedSource.push(/\r/gi);
	//config.protectedSource.push(/\n/gi);
	//config.protectedSource.push(/@[\s\S]*?\)/g);

	config.protectedSource.push(/@extends[\s\S]*\)/gi);
	config.protectedSource.push(/@section[\s\S]*\)/gi);
	//config.protectedSource.push(/[\s\S]*?\-\-\>/g);
	// config.protectedSource.push( /###[\s\S]*/g );
	// //config.protectedSource.push( /@section[\s\S]*/g );
	// //config.protectedSource.push( /@extends[\s\S]*/g );
	config.protectedSource.push(/@foreach.*/g);
	config.protectedSource.push(/@end[\s\S]*/g);
	config.protectedSource.push(/@endforeach.*/g);
	config.protectedSource.push(/@endsection.*/g);
	// config.protectedSource.push( /###end.*/g );
	// config.protectedSource.push( /{{.*}}/g );
};
