<template>
    <Teleport to="body">
        <el-dialog
            :class="state.width == 0 ? 'full-screen' : ''"
            :width="state.width"
            v-model="isDialogVisible"
            destroy-on-close
            close-on-click-modal
            center
            tabindex="0"
            @keydown="handleKeydown"
            @close="() => closedDialog(state.isCloseReload)"
        >
            <template #header="{ close, titleId, titleClass }">
                <div class="dialog-fixed-header">
                    <span :id="titleId" :class="titleClass">{{ title }}</span>
                    <button type="button" class="el-dialog__headerbtn" aria-label="Close">
                        <i class="el-icon el-icon-close"></i>
                    </button>
                </div>
            </template>

            <div class="sync-dialog__div" :style="state.height != 0 ? 'height:' + state.height + 'px' : ''">
                <Suspense>
                    <template #default>
                        <div class="dialog-content">
                            <component :is="dialogPage" v-bind="params" @closed-dialog="closedDialog"></component>
                        </div>
                    </template>
                    <template #fallback>
                        <div class="dialog-loading">
                            <el-icon class="is-loading"><Loading /></el-icon>
                            <span>Loading...</span>
                        </div>
                    </template>
                </Suspense>
            </div>
        </el-dialog>
    </Teleport>
</template>

<script setup lang="ts">
import { utils } from '@/utils'
import { Loading } from '@element-plus/icons-vue'
import { useWindowSize } from '@vueuse/core'
import type { Component } from '@vue/runtime-core'
import { defineAsyncComponent, onUnmounted, reactive, ref, shallowRef, watch } from 'vue'

interface DialogInfo {
    width?: number
    height?: number
    title?: string
    isCloseReload?: boolean
}

interface DialogParams {
    [key: string]: any
}
//modal Dialog參數
const isDialogVisible = ref<boolean>(false)
const dialogPage = shallowRef<Component | null>(null)
const { width, height } = useWindowSize()
const state = reactive({
    width: 0,
    height: 0,
    isCloseReload: false
})

const emits = defineEmits(['closed-dialog'])
const props = defineProps({})
const components = import.meta.glob<() => Promise<Component>>('@/pages/admin/**/*.vue')
let params = reactive<DialogParams>({})

let title = ref('')
const open = async (
    url: string,
    aparams: DialogParams = {},
    info: DialogInfo = { width: 0, height: 0, title: '', isCloseReload: false }
) => {
    params = {
        ...aparams
    }

    title.value = info.title || ''
    state.width = info.width ?? 0
    state.height = info.height ?? 0
    state.isCloseReload = info.isCloseReload ?? false
    //console.clear();
    //console.log(['windowWidth.value', width.value])
    if (width.value < 768) {
        state.width = 0
    }
    try {
        if (url.includes('://')) {
            // 使用內建的 URL 物件解析 URL
            const urlString = new URL(url)
            params = {
                ...aparams,
                ...Object.fromEntries(urlString.searchParams.entries())
            }

            let pathName = urlString.pathname
            url = pathName.replace(/^\/+/, '')
        }

        const urls = url.split('/')
        const componentUrl = urls.length === 2 ? `${url}/index` : url

        const componentPath = `/pages/${componentUrl}.vue`
        //console.log(['components', components])
        // 检查组件是否存在
        if (components[componentPath]) {
            dialogPage.value = await defineAsyncComponent(components[componentPath])
            isDialogVisible.value = true
        } else {
            throw new Error(`無法找到檔案: ${componentPath}`)
        }
    } catch (error) {
        if (error instanceof Error) {
            utils.message(`Dialog Error: ${error.message}`, 'error')
        } else {
            utils.message('發生未知錯誤', 'error')
        }
    }
}
const closedDialog = async (isReload: boolean) => {
    dialogPage.value = null
    isDialogVisible.value = false
    //console.clear();

    emits('closed-dialog', isReload)
}
defineExpose({ open })
const errorCaptured = async (err: Error, vm: any, info: string) => {
    console.error(`my-dialog Error: ${vm.$options?.name};message: ${err.toString()};info: ${info}`)
    return false // 可以返回 true 或 false 來決定是否繼續往上傳遞錯誤
}

const handleKeydown = (event: KeyboardEvent) => {
    if (event.key === 'PageUp') {
        event.preventDefault()
        const dialogContent = document.querySelector('.sync-dialog__div')
        if (dialogContent) {
            dialogContent.scrollBy(0, -window.innerHeight)
        }
    } else if (event.key === 'PageDown') {
        event.preventDefault()
        const dialogContent = document.querySelector('.sync-dialog__div')
        if (dialogContent) {
            dialogContent.scrollBy(0, window.innerHeight)
        }
    }
}

// Add global event listener when dialog is opened
const setupKeyboardEvents = () => {
    if (isDialogVisible.value) {
        window.addEventListener('keydown', handleKeydown)
    }
}

// Remove event listener when dialog is closed
const cleanupKeyboardEvents = () => {
    window.removeEventListener('keydown', handleKeydown)
}

// Watch for dialog visibility changes
watch(isDialogVisible, newValue => {
    if (newValue) {
        setupKeyboardEvents()
    } else {
        cleanupKeyboardEvents()
    }
})

// Clean up on component unmount
onUnmounted(() => {
    cleanupKeyboardEvents()
})
</script>

<style lang="scss">
.full-screen {
    width: 98% !important;
    height: 98% !important;
    display: flex;
    flex-direction: column;
    margin: 0 !important;
    position: absolute;
    top: 50%;
    left: 50%;
    z-index: 999999;
    transform: translate(-50%, -50%);
}

// .el-dialog .el-dialog__body {
//     flex: 1;
//     overflow: auto;
// }
.sync-dialog__div {
    height: 100%;

    overflow: auto;
}
.el-dialog--center {
    text-align: left;
}
.el-dialog__body {
    text-align: initial;
    height: 95%;
}
.dialog-content {
    width: 98%;
    height: 100%;
}

.dialog-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    gap: 8px;

    .el-icon {
        font-size: 20px;
    }
}
</style>
