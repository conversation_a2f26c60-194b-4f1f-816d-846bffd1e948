<?php

namespace App\Mails;

use DB;
use PF;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class adminuserforgetpwdMail extends baseMail {
    public $tries = 3;
    use Queueable;
    use SerializesModels;
    public $email;
    public $account;
    public $password;

    // 讓外部可以將參數指定進來
    public function __construct($account, $email) {
        $this->email = $email;
        $this->account = $account;
    }

    public function build() {
        $rows = DB::table('adminuser');
        $rows->selectRaw('email,name,id');
        $rows->myWhere('email|S', $this->email, 'email', 'Y');
        $rows->myWhere('account|S', $this->account, 'account', 'Y');
        $rows->limit(1);
        if ($rows->count() > 0) {
            $rs = $rows->first();
            $pwd = substr(md5(uniqid(rand(), true)), 0, 5);
            $inputs = null;
            $inputs['password'] = \Hash::make($pwd);

            \App\Models\adminuser::myWhere('id', $rs->id, 'id', 'Y')->update($inputs);

            $rs = $rows->first();
            foreach ($rs as $key => $value) {
                $this->data[$key] = $value;
            }
            $this->data['pwd'] = $pwd;

            //\App::setLocale($rs->alg);

            $this->from(config('mail.from.address'), config('config.name'));

            $this->subject(config('config.name') . '-忘記密碼');
            /*
                        if (PF::isEmpty($rs->alg)) {
                            $this->view('email.zh.memberforgetpwd');
                        } else {
                            $this->view('email.'.$rs->alg.'.memberforgetpwd');
                        }
            */
            $this->view('email.memberforgetpwd');
            $this->with($this->data);
            $this->to($rs->email);
            //PF::printr($this->data);

            //    $this->cc(explode(';', config('config.email')));
            //$this->bcc(config('config.'.$rs->alg.'.email'));
        } else {
            throw new \CustomException('查無此記錄');
        }

        //PF::dbSqlPrint($rows);

        return $this;
    }
}
