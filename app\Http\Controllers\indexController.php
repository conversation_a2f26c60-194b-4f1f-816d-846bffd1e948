<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use PF;
use DB;

//use Illuminate\Support\Facades\DB;

class indexController extends Controller {
    private $data;

    /**
     *建構子.
     */
    public function __construct() {
        //$this->limit="xx";
        parent::__construct();
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request, $id = "") {



        $rs = [];
        $url = $request->path();
        $title = "";
        $keyword = "";
        $description = "";
        $urls = explode("/", $url);
        $img = url('/') . "images/logo.png";

        for ($i = count($urls) - 1; $i >= 0; --$i) {
            if ($urls[$i] != "") {
            $title = \Cache::get('setup')[$urls[$i]];
            if ($title != "") {
                break;
            }
        }
        }

        if ($url == "/") {
            $ld_json = $this->get_ld_json("WebSite", $rs);
        } elseif (\Str::contains($url, ['news/show'])) {
            $rs = \App\Models\board::selectRaw('title,body as description,field1 as img,created_at')->findOrFail($id);
            $title = $rs->title . " | 最新消息";
            $rs->title =  $title;
            $body = $rs->description;

            if ($rs->img != "") {
                $img = url('/') . "/images/news/" . explode(",", $rs->img)[0];;
            }

            $ld_json = $this->get_ld_json("NewsArticle", ['rs' => $rs, 'imgurl' => url('/') . '/images/news/']);
        } elseif (\Str::contains($url, ['news'])) {
            $rows = \App\Models\board::selectRaw('id');
            $rows->whereRaw('convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE() and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()');
            $rows->orderByRaw('id desc');
            $rows = $rows->limit(10);
            $rows = $rows->get();
            if ($rows->count() > 0) {
                $ld_json = $this->get_ld_json("ItemList", ['rows' => $rows, 'url' => url('/') . '/news/show/']);
            }
        } elseif (\Str::contains($url, ['product/show'])) {
            $rows = \App\Models\product::selectRaw('*,img as image,body as description ');
            $rows->whereRaw('convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE() and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()');
            $rs = $rows->findOrFail($id);
            $title = $rs['title'] . " | " . $title;
            $description = $rs->body;
            $ld_json = $this->get_ld_json("Product", ['rows' => $rows, 'url' => url('/') . '/product/show/']);
        } elseif (\Str::contains($url, ['about'])) {
            $rows = \App\Models\epost::selectRaw('*');
            $rows->myWhere('epostid|S', end($urls), "id", 'Y');
            $rs = $rows->first();
            if ($rs != null) {
                $rs->title = $title;
                $rs->description = $rs->epostbody;

                $ld_json = $this->get_ld_json("ProfilePage", ['rs' => $rs]);
            }
        }
        if ($ld_json != "") {
            $ld_json = str_replace('\\/', '/', $ld_json);
        }

        //File::copy(base_path('client/.output'), base_path('public'));
        $html = \File::get(public_path('index.html'));
        $head = \Str::between($html, '<head>', '</head>');
        $body = \Str::between($html, '<body>', '</body>');

        return view(
            'index',
            [
                'head' => $head,
                'title' => $title,
                'keyword' => $keyword,
                'body' => $body,
                'description' => PF::noHtml($rs['description']),
                'ld_json' => $ld_json
            ]
        );
    }
    public function get_ld_json($type, $arr) {
        $ld_json = \view('ld_json.' . $type, $arr)->render();
        return $ld_json;
    }
}
