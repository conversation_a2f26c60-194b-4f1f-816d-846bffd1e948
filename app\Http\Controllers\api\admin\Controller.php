<?php

namespace App\Http\Controllers\api\admin;

use App\Http\Controllers\api\Controller as BaseController;


/**
 * @OA\SecurityScheme(securityScheme="bearerAuth",type="http",scheme="bearer")
 */

class Controller extends BaseController {


    public $jsondata;
    public function __construct() {
        $this->jsondata['resultcode'] = 0;
        $this->jsondata['resultmessage'] = '';
    }
    public function apiResponse($data, $code = 200) {

        return response()->json($data, $code, ['Content-Type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);
    }
}
