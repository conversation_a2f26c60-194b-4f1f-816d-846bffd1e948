<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
// $newJob = new \App\Jobs\SendEmailJob();
// dispatch($newJob);
class SendEmailJob implements ShouldQueue {
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    public $timeout = 180;
    public $tries = 2;

    public function __construct() {
    }

    public function handle() {
        // $linenotifyService = new \App\Services\linenotifyService();
        // $linenotifyService->executePushLog();
    }
}
