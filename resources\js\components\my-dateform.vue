<!-- DateInput.vue -->
<template>
    <div
        v-if="isIPhone"
        class="el-input el-input--prefix el-input--suffix el-date-editor el-date-editor--date el-tooltip__trigger el-tooltip__trigger"
        :style="{ width: type === 'date' ? '150px' : '250px' }"
    >
        <!-- input --><!-- prepend slot --><!--v-if-->
        <div class="el-input__wrapper" tabindex="-1">
            <!-- prefix slot -->
            <span class="el-input__prefix"
                ><span class="el-input__prefix-inner"
                    ><i class="el-icon el-input__icon"
                        ><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
                            <path
                                fill="currentColor"
                                d="M128 384v512h768V192H768v32a32 32 0 1 1-64 0v-32H320v32a32 32 0 0 1-64 0v-32H128v128h768v64zm192-256h384V96a32 32 0 1 1 64 0v32h160a32 32 0 0 1 32 32v768a32 32 0 0 1-32 32H96a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32h160V96a32 32 0 0 1 64 0zm-32 384h64a32 32 0 0 1 0 64h-64a32 32 0 0 1 0-64m0 192h64a32 32 0 1 1 0 64h-64a32 32 0 1 1 0-64m192-192h64a32 32 0 0 1 0 64h-64a32 32 0 0 1 0-64m0 192h64a32 32 0 1 1 0 64h-64a32 32 0 1 1 0-64m192-192h64a32 32 0 1 1 0 64h-64a32 32 0 1 1 0-64m0 192h64a32 32 0 1 1 0 64h-64a32 32 0 1 1 0-64"
                            ></path></svg></i
                    ><!--v-if--></span
                ></span
            >
            <input
                :type="type === 'datetime' ? 'datetime-local' : type"
                v-model="localModelValue"
                style="font-size: 14px !important"
                class="el-input__inner"
            />
            <!-- suffix slot --><span class="el-input__suffix"
                ><span class="el-input__suffix-inner"> <!--v-if--><!--v-if--><!--v-if--><!--v-if--><!--v-if--></span>
            </span>
        </div>
        <!-- append slot --><!--v-if-->
    </div>

    <el-date-picker
        v-else
        v-model="localModelValue"
        v-bind="$attrs"
        :type="type"
        :format="type == 'date' ? 'YYYY-MM-DD' : 'YYYY-MM-DD HH:mm:ss'"
        :value-format="type == 'date' ? 'YYYY-MM-DD' : 'YYYY-MM-DD HH:mm:ss'"
        :style="{ width: type === 'date' ? '150px' : '250px' }"
    />
    <slot></slot>
</template>

<script setup lang="ts">
// 定义组件属性
const props = defineProps(['modelValue', 'type', 'placeholder'])
const isIPhone = /iPhone/i.test(navigator.userAgent)
const emits = defineEmits(['update:modelValue']) //子傳父
const localModelValue = computed({
    //前端要引用顯示才會自動觸發更新
    get() {
        if (!utils.isEmpty(props.modelValue)) {
            //呼叫 emit('update:modelValue', val)->2.父組件接收並更新->3.更新後通過 props 傳回子組件get
            if (utils.isNumber(props.modelValue) && props.type == 'datetime') {
                let t = parseInt(props.modelValue, 10)
                let date = new Date(t) // 将时间戳转换为 Date 对象
                // 格式化为 yyyy-MM-dd HH:mm:ss 格式
                let formattedDate =
                    date.getFullYear() +
                    '-' +
                    (date.getMonth() + 1).toString().padStart(2, '0') +
                    '-' +
                    date.getDate().toString().padStart(2, '0') +
                    ' ' +
                    date.getHours().toString().padStart(2, '0') +
                    ':' +
                    date.getMinutes().toString().padStart(2, '0') +
                    ':' +
                    date.getSeconds().toString().padStart(2, '0')

                return String(formattedDate) // 输出：2023-12-31 00:00:00
            }

            return String(props.modelValue)
        }
        return null
    },
    set(val) {
        //選擇後觸發,並通知父層
        emits('update:modelValue', val)
    }
})
const errorCaptured = async (err, vm, info) => {
    console.error(`my-dateform Error: ${vm.$options.name};message: ${err.toString()};info: ${info}`)
    return false // 可以返回 true 或 false 來決定是否繼續往上傳遞錯誤
}
// 定义 emit 事件
//const emits = defineEmits(['update:modelValue'])
// const localValue = computed({
//     get() {
//         return props.modelValue
//     },
//     set: val => {
//         emits('update:modelValue', val)
//     },
// })
// <my-dateform
//                                         v-model="rs.start_date"
//                                         placeholder="Pick a day"
//                                         type="date"
// >
//</my-dateform>
</script>
