import { useDataStore } from '@/stores' //資料儲存套件

// middleware/admin.ts
export default defineNuxtRouteMiddleware(
    (to, _from) => {
        const store = useDataStore()

        //console.clear();
        //console.log(['to', to])
        if (to.path.startsWith('/admin/') && to.path != '/admin/login' && to.path != '/admin') {
            if (utils.isEmpty(store.adminuser?.api_token)) {
                return navigateTo('/admin/login')
            }
            // 手动设置布局
            to.meta.layout = 'admin'
            //console.log(['to.meta.layout', to.meta.layout])
        }
        if (to.path == '/admin/login') {
            if (
                !utils.isEmpty(store.adminuser?.iskeep) &&
                !utils.isEmpty(store.adminuser?.api_token)
            ) {
                return navigateTo('/admin/adminuserloginlog')
            }
        }
    },
    {
        override: true
    }
)
