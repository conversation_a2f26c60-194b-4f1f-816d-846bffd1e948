<?php

namespace App\Http\Controllers\api;

use App\Http\Controllers\Controller;
use App\Models\course;
use App\Models\coach;
use App\Traits\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

/**
 * 課程 API 控制器
 * 處理課程相關功能
 */
class courseController extends Controller {
    use ApiResponse;

    /**
     * @OA\Post(
     *     path="/api/course/list",
     *     summary="課程列表",
     *     tags={"課程管理"},
     *     @OA\RequestBody(
     *         @OA\JsonContent(
     *             @OA\Property(property="page", type="integer", description="頁碼", example=1),
     *             @OA\Property(property="per_page", type="integer", description="每頁筆數", example=10),
     *             @OA\Property(property="difficulty_level", type="integer", description="課程難度", example=1),
     *             @OA\Property(property="price_min", type="number", description="最低價格", example=1000),
     *             @OA\Property(property="price_max", type="number", description="最高價格", example=3000),
     *             @OA\Property(property="location", type="string", description="上課地點", example="新手滑雪道"),
     *             @OA\Property(property="keyword", type="string", description="關鍵字搜尋", example="基礎滑雪")
     *         )
     *     ),
     *     @OA\Response(response=200, description="取得成功")
     * )
     */
    public function list(Request $request) {
        try {
            $query = course::with(['coach.member'])
                ->where('course_status', 1); // 只顯示已發布的課程

            // 難度篩選
            if ($request->has('difficulty_level')) {
                $query->where('difficulty_level', $request->difficulty_level);
            }

            // 價格範圍篩選
            if ($request->has('price_min')) {
                $query->where('price', '>=', $request->price_min);
            }
            if ($request->has('price_max')) {
                $query->where('price', '<=', $request->price_max);
            }

            // 地點篩選
            if ($request->has('location')) {
                $query->where('location', 'like', '%' . $request->location . '%');
            }

            // 關鍵字搜尋
            if ($request->has('keyword')) {
                $keyword = $request->keyword;
                $query->where(function ($q) use ($keyword) {
                    $q->where('title', 'like', '%' . $keyword . '%')
                        ->orWhere('description', 'like', '%' . $keyword . '%');
                });
            }

            // 分頁
            $perPage = $request->get('per_page', 10);
            $courses = $query->orderByDesc('rating')
                ->orderByDesc('created_at')
                ->paginate($perPage);

            return $this->successResponse([
                'courses' => $courses,
            ], '取得課程列表成功');
        } catch (\Exception $e) {
            return $this->errorResponse([], '取得課程列表失敗：' . $e->getMessage(), 500);
        }
    }

    /**
     * @OA\Post(
     *     path="/api/course/detail",
     *     summary="課程詳情",
     *     tags={"課程管理"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             @OA\Property(property="id", type="integer", description="課程編號", example=10001)
     *         )
     *     ),
     *     @OA\Response(response=200, description="取得成功"),
     *     @OA\Response(response=404, description="課程不存在")
     * )
     */
    public function detail(Request $request) {
        $validator = Validator::make($request->all(), [
            'id' => 'required|integer|exists:course,id',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse($validator->errors(), '資料驗證錯誤', 422);
        }

        try {
            $course = course::with([
                'coach.member',
                'reviews.member',
                'bookings' => function ($query) {
                    $query->where('booking_status', 4); // 已完成的預約
                }
            ])->find($request->id);

            if (!$course) {
                return $this->errorResponse([], '課程不存在', 404);
            }

            // 檢查課程狀態
            if ($course->course_status != 1) {
                return $this->errorResponse([], '課程未開放', 404);
            }

            return $this->successResponse([
                'course' => $course,
            ], '取得課程詳情成功');
        } catch (\Exception $e) {
            return $this->errorResponse([], '取得課程詳情失敗：' . $e->getMessage(), 500);
        }
    }

    /**
     * @OA\Post(
     *     path="/api/course/featured",
     *     summary="推薦課程",
     *     tags={"課程管理"},
     *     @OA\RequestBody(
     *         @OA\JsonContent(
     *             @OA\Property(property="limit", type="integer", description="筆數限制", example=6)
     *         )
     *     ),
     *     @OA\Response(response=200, description="取得成功")
     * )
     */
    public function featured(Request $request) {
        try {
            $limit = $request->get('limit', 6);

            $courses = course::with(['coach.member'])
                ->where('course_status', 1)
                ->where('rating', '>=', 4.0) // 評分 4.0 以上
                ->orderByDesc('rating')
                ->orderByDesc('total_bookings')
                ->limit($limit)
                ->get();

            return $this->successResponse([
                'courses' => $courses,
            ], '取得推薦課程成功');
        } catch (\Exception $e) {
            return $this->errorResponse([], '取得推薦課程失敗：' . $e->getMessage(), 500);
        }
    }

    /**
     * @OA\Post(
     *     path="/api/course/coach-courses",
     *     summary="教練的課程",
     *     tags={"課程管理"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             @OA\Property(property="coach_id", type="integer", description="教練編號", example=10001),
     *             @OA\Property(property="page", type="integer", description="頁碼", example=1),
     *             @OA\Property(property="per_page", type="integer", description="每頁筆數", example=10)
     *         )
     *     ),
     *     @OA\Response(response=200, description="取得成功")
     * )
     */
    public function coachCourses(Request $request) {
        $validator = Validator::make($request->all(), [
            'coach_id' => 'required|integer|exists:coach,id',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse($validator->errors(), '資料驗證錯誤', 422);
        }

        try {
            $perPage = $request->get('per_page', 10);

            $courses = course::with(['coach.member'])
                ->where('coach_id', $request->coach_id)
                ->where('course_status', 1)
                ->orderByDesc('created_at')
                ->paginate($perPage);

            return $this->successResponse([
                'courses' => $courses,
            ], '取得教練課程成功');
        } catch (\Exception $e) {
            return $this->errorResponse([], '取得教練課程失敗：' . $e->getMessage(), 500);
        }
    }
}
