<?php
namespace App\Http\Controllers\api\admin;
use PF,PT;
use Exception,DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\formquerylog;
use App\Http\Controllers\Controller\api\admin;
/***
"功能名稱":"操作記錄",
"資料表":"formquerylog",
"建立時間":"2025-02-16 00:00:32 ",
***/
class formquerylogController extends Controller
{

    private $data;
    private $xmlDoc;


    public function __construct()
    {

        //$this->limit="xx";
        parent::__construct();
        //將request全部導入到$this->data變數中
        $this->data=PF::requestAll($this->data);
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');

       //$this->data['nav'] = PT::nav($this->data['xmldoc'],"formquerylog",$this->data['nav']);
       $this->data['displaynames']= formquerylog::getFieldTitleArray();
       //PT::checkRoleLimitsThrow("limit", str_replace('Controller', '', class_basename($this)));

    }


    /**
     * @OA\Post(
     *     path="/api/admin/formquerylog",security={{"bearerAuth":{}}},operationId="",tags={"後台/操作記錄"},summary="列表",description="",
     *     @OA\RequestBody(required=true,
     *      @OA\JsonContent(
     *      allOf={

     *         @OA\Schema(@OA\Property(property="page",description="頁數",type="integer",example="1",)),
     *         @OA\Schema(@OA\Property(property="pagesize",description="筆數/頁",type="integer",example="10",)),
     *         @OA\Schema(@OA\Property(property="search",description="搜尋",type="string",example="",)),
     *          @OA\Schema(@OA\Property(property="sortname", type="string",description="排序欄位", example="",)),
     *          @OA\Schema(@OA\Property(property="sorttype", type="string",description="排序方式", example="desc",)),
        *         @OA\Schema(@OA\Property(property="searchstartdate",description="開始時間",type="string",example="2021-01-01",)),
     *         @OA\Schema(@OA\Property(property="searchenddate",description="結束時間",type="string",example="2099-12-31",)),
             *     })

     *   ,),

     *   @OA\Response(response=200,description="回覆",
     *     @OA\JsonContent(type="object",
     *      @OA\Property(property="resultcode",type="integer",description="訊息代碼",),
     *      @OA\Property(property="resultmessage",type="string",description="訊息內容"),
     *      @OA\Property(property="data", type="object",
     *          @OA\Property(property="current_page", type="integer",description="目前頁數", ),
     *          @OA\Property(property="total", type="integer",description="總頁數", ),

     *      @OA\Property(property="data",  type="array",
     *      @OA\Items(allOf={
     *         @OA\Schema(ref="#/components/schemas/formquerylog"),
          *         @OA\Schema(@OA\Property(property="", type="string",description="", example="") ),
     *     }))

     *      ),)
     * ),)
     */


    public function index(Request $request)
    {
         $rows=$this->getRows($request);
         //$rows = $rows->take(10);
         //PF::dbSqlPrint($rows);
         //$rows = $rows->get();
         $pagesize=(is_numeric($request->input('pagesize'))  ? $request->input('pagesize') : 10);
         $rows = $rows->paginate($pagesize);

         /*
         foreach ($rows as $key => $rs) {
          unset($rs->password);
          unset($rs->api_token);
          unset($rs->remember_token);
          unset($rs->lastlogin_ip);
         }
         $data = $rows->toArray();  // 轉換成陣列
         $data['kindtitle'] = $kindtitle;  // 加入 kindtitle
         $this->jsondata['data'] = $data;
         */


         $this->jsondata['data'] = $rows;
         return $this->apiResponse($this->jsondata);

    }

    public function getRows($request)
    {
        $rows = DB::table("formquerylog")->selectRaw("formquerylog.*");
        
                           //依條件搜尋資料的SQL語法
        $rows->myWhere($request->input('searchname'), $request->input('search'), $this->data['displaynames'], 'N');
        //依條件時間搜尋資料的SQL語法
        $rows->myWhere('convert(' . $request->input('searchdatename') . ',DATE)|>=', $request->input('searchstartdate'), $this->fieldnicknames, 'N');
        $rows->myWhere('convert(' . $request->input('searchdatename') . ',DATE)|<=', $request->input('searchenddate'), $this->fieldnicknames, 'N');
        if ($request->input('sortname')){
            $rows->orderBy($request->input('sortname'), $request->input('sorttype')=="desc"  ? $request->input('sorttype') : "asc"  );
        }else{
            $rows->orderByRaw('formquerylog.id desc');
        }
        return $rows;
    }


    /**
     * @OA\Post(
     *     path="/api/admin/formquerylog/show",security={{"bearerAuth":{}}},operationId="",tags={"後台/操作記錄"},summary="單筆顯示",description="",
     *     @OA\RequestBody(required=true,
     *      @OA\JsonContent(
     *      allOf={

     *         @OA\Schema(@OA\Property(property="id",description="編號",type="integer",example="1",)),

     *     })
     *   ,),

     *   @OA\Response(response=200,description="回覆",
     *     @OA\JsonContent(type="object",
     *      @OA\Property(property="resultcode",type="integer",description="訊息代碼",),
     *      @OA\Property(property="resultmessage",type="string",description="訊息內容"),

     *      @OA\Property(property="data", type="object",
    *      allOf={
    *         @OA\Schema(ref="#/components/schemas/formquerylog"),
    *         @OA\Schema(type="object",@OA\Property(property="", type="string",description="系列", example="") ),

    *     })

     *     ,)
     *),)
     */


    public function show($request)
    {



        $rows =\App\Models\formquerylog::selectRaw('formquerylog.*');
$rows->where('id', '=', $request->input('id'));
$rs = $rows->firstOrFail();
$rs = PF::jsonToRs($rs, $rs->jsonbody);
unset($rs->jsonbody);
             /*
             unset($rs->password);
             unset($rs->api_token);
             unset($rs->remember_token);
             unset($rs->lastlogin_ip);
             */
             $this->jsondata['data'] = $rs;

        return $this->apiResponse($this->jsondata);

    }


    /**
     * @OA\Post(
     *     path="/api/admin/formquerylog/store",security={{"bearerAuth":{}}},operationId="",tags={"後台/操作記錄"},summary="新增/編輯",description="編號有值代表編輯,沒有代表新增",
     *     @OA\RequestBody(required=true,

     *      @OA\JsonContent(
     *      allOf={
     *         @OA\Schema(ref="#/components/schemas/formquerylog"),
     *         @OA\Schema(type="object",@OA\Property(property="", type="string",description="系列", example="") ),
     *     })

     *   ,),
     *   @OA\Response(response=200,description="回覆",
     *     @OA\JsonContent(type="object",
     *      @OA\Property(property="resultcode",type="integer",description="訊息代碼",),
     *      @OA\Property(property="resultmessage",type="string",description="訊息內容"),
     *        @OA\Property(property="data", type="object",
     *         allOf={
     *             @OA\Schema(@OA\Property(property="id", type="integer",description="編號", example="10101") ),
     *         }
     *        )
     *     ),)
     *),)
     */


    public function store(Request $request)
    {

          $edit=$request->input('id');
        //FIXME 那些欄位為必填判斷
        $validators = null;
        if($validators!=null){
            $validator = \Validator::make($request->all(), $validators);
            $validator->setAttributeNames($this->data['displaynames'] == null ? [] : $this->data['displaynames']);
            if ($validator->fails()) {
                throw new \CustomException(implode(',', $validator->messages()->all()));
            }
        }
        $inputs = $request->all();


        

        if ('' ==$edit) {

           //PF::printr($inputs);exit();
           $edit=formquerylog::create($inputs)->id;

           $this->jsondata['resultmessage'] ='新增成功';
        } else {
            //PF::printr($inputs); exit();
           $rows = formquerylog::selectRaw('formquerylog.*');
           $rows->myWhere('id|N', $edit, 'edit', 'Y');
                    $rs=$rows->firstOrFail();
            $rs->update($inputs);
            $this->jsondata['resultmessage'] ='更新成功';
        }
                $this->jsondata['data']['id']=$edit;
        return $this->apiResponse($this->jsondata);




    }
    /**
    * @OA\Post(
    *     path="/api/admin/formquerylog/destroy",security={{"bearerAuth":{}}},operationId="",tags={"後台/操作記錄"},summary="刪除",description="",
    *   @OA\RequestBody(required=true,@OA\MediaType(mediaType="application/json",@OA\Schema(
    *         @OA\Property(property="del",description="要刪除的編號",type="integer",example="1",description="多筆中間用逗號",),

    *       ),
    *   ),),
    *   @OA\Response(response=200,description="回覆",
    *     @OA\JsonContent(type="object",
    *      @OA\Property(property="resultcode",type="integer",description="訊息代碼",),
    *      @OA\Property(property="resultmessage",type="string",description="訊息內容"),
    *     ),)
    *),)
    */

    public function destroy(Request $request)
    {
     $rows = formquerylog::selectRaw('formquerylog.*');
        $rows->myWhere('id|ININT', $this->data['del'], 'del', 'Y');
                //$rows->delete();
        $rows->chunk(200, function ($rows) {
            foreach ($rows as $rs) {
                $rs->delete();
            }
        });


        $this->jsondata['resultmessage'] = '刪除成功';
        return $this->apiResponse($this->jsondata);


    }

        

}