<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/*swagger api document start*/

/**
 * @OA\Schema(
 *   schema="review",
 *      allOf={
 *         @OA\Schema( @OA\Property(property="id", type="integer", description="評價編號", example="10001")),
 *         @OA\Schema( @OA\Property(property="member_id", type="integer", description="會員編號", example="10001")),
 *         @OA\Schema( @OA\Property(property="booking_id", type="integer", description="預約編號", example="10001")),
 *         @OA\Schema( @OA\Property(property="course_id", type="integer", description="課程編號", example="10001")),
 *         @OA\Schema( @OA\Property(property="coach_id", type="integer", description="教練編號", example="10001")),
 *         @OA\Schema( @OA\Property(property="rating", type="integer", description="評分", example="5")),
 *         @OA\Schema( @OA\Property(property="comment", type="string", description="評價內容", example="教練很專業，課程內容豐富")),
 *         @OA\Schema( @OA\Property(property="review_images", type="string", description="評價圖片", example="review1.jpg,review2.jpg")),
 *         @OA\Schema( @OA\Property(property="is_anonymous", type="integer", description="是否匿名", example="0")),
 *         @OA\Schema( @OA\Property(property="status", type="integer", description="狀態", example="1")),
 *         @OA\Schema( @OA\Property(property="reviewed_at", type="string", description="評價時間", example="2024-01-15 18:00:00")),
 *         @OA\Schema( @OA\Property(property="created_at", type="string", description="建立時間", example="2024-01-01 10:00:00")),
 *         @OA\Schema( @OA\Property(property="updated_at", type="string", description="更新時間", example="2024-01-01 10:00:00")),
 *      }
 *)
 */
/*swagger api document end*/

class review extends Model {
    use HasFactory;

    /**
     * 資料表名稱
     */
    protected $table = 'review';

    /**
     * 可批量賦值的屬性
     */
    protected $fillable = [
        'member_id',
        'booking_id',
        'course_id',
        'coach_id',
        'rating',
        'comment',
        'review_images',
        'is_anonymous',
        'status',
        'reviewed_at',
    ];

    /**
     * 屬性轉換
     */
    protected $casts = [
        'reviewed_at' => 'datetime',
    ];

    /*Relations start*/
    /**
     * 與 member 表的關聯 - 評價會員
     */
    public function member() {
        return $this->belongsTo(member::class, 'member_id', 'id');
    }

    /**
     * 與 booking 表的關聯 - 評價預約
     */
    public function booking() {
        return $this->belongsTo(booking::class, 'booking_id', 'id');
    }

    /**
     * 與 course 表的關聯 - 評價課程
     */
    public function course() {
        return $this->belongsTo(course::class, 'course_id', 'id');
    }

    /**
     * 與 coach 表的關聯 - 評價教練
     */
    public function coach() {
        return $this->belongsTo(coach::class, 'coach_id', 'id');
    }
    /*Relations end*/

    /**
     * 模型事件
     */
    public static function boot() {
        parent::boot();

        static::creating(function ($model) {
            // 建立時設定評價時間
            if (empty($model->reviewed_at)) {
                $model->reviewed_at = now();
            }
        });

        static::updating(function ($model) {
            // 更新時的邏輯處理
        });

        static::deleted(function ($model) {
            /*Del Relations start*/
            // 刪除評價時的相關處理
            /*Del Relations end*/
        });
    }
}
