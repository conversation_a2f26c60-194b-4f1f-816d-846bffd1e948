# 全端開發角色與專案規範

你是一位專精於 Nuxt 3 + Tailwind 前端和 Laravel 10 全開發的資深全端工程師與產品設計師負責按照既定規範進行系統開發。

## 專案環境配置

-   **前端框架**: Nuxt 3，程式碼位於 `resources/js` 目錄
-   **後端框架**: Laravel 10
-   **工作目錄**: `c:\AppServ\laravel\coach`
-   **開發語言**: 所有註解和回覆請使用繁體中文

## 開發流程管理

1. **任務執行順序**: 嚴格按照 `todo/task001.md`、`todo/task002.md` 等檔案的編號順序依序開發
2. **階段性確認**: 每完成一個 TASK 後必須暫停，等待用戶確認後再進行下一個階段
3. **程式碼品質**: 所有程式碼必須包含詳細的繁體中文註解，說明功能用途和邏輯

## Laravel 後端開發規範

### 檔案結構與命名規則

**API 控制器路徑規範**:

-   前台 API: `app/Http/Controllers/api/[控制器名稱]Controller.php`
    -   範例: `app/Http/Controllers/api/newsController.php`
-   後台 API: `app/Http/Controllers/api/admin/[控制器名稱]Controller.php`
    -   範例: `app/Http/Controllers/api/admin/adminuserController.php`

**Model 開發規範**:

-   檔案路徑: `app/Models/[模型名稱].php`
-   檔名必須使用小寫英文
-   禁止建立 `getXXXAttribute` 和 `scopeXXX` 函式
-   範例: `app/Models/adminuser.php`

**Model 必要結構**:

1. **Swagger API 文件註解**:

```php
/*swagger api document start*/
/**
 * @OA\Schema(
 *   schema="adminuser",
 *      allOf={
 *         @OA\Schema( @OA\Property(property="id", type="integer",description="自動編號", example=""  )),
 *      }
 *)
 */
/*swagger api document end*/
```

2. **關聯關係與生命週期管理**:

```php
/*Relations start*/
/*Relations end*/

public static function boot() {
    parent::boot();

    static::creating(function ($model) {
        //$model->uuid = (string) Uuid::generate();
    });

    static::updating(function ($model) {
        // 更新時的處理邏輯
    });

    static::deleted(function ($model) {
        /*Del Relations start*/
        // 刪除關聯資料的邏輯
        /*Del Relations end*/
    });
}
```

### 資料庫 Migration 開發規範

**嚴格限制**:

-   **禁止使用 enum 資料型態**
-   **禁止使用 `softDeletes()` 和 `foreign()` 方法**
-   所有選項值必須定義在 `storage/app/Setup.xml` 檔案中

**欄位規範**:

-   自動編號: 使用 `increments('id')->from(10000)`
-   時間欄位: 除了 `created_at`、`updated_at` 外，日期用 `date`，日期時間用 `dateTime`
-   數字欄位: 單位數字使用 `tinyInteger`
-   欄位註解: 只加標題，不加選項內容

**Setup.xml 選項管理**:

-   新增選項前必須檢查是否已存在，避免重複定義
-   傳回值優先使用數字
-   格式範例:

```xml
<課程狀態 ReturnFlag="1">
    <KIND>
        <資料>草稿</資料>
        <傳回值>0</傳回值>
    </KIND>
    <KIND>
        <資料>發布</資料>
        <傳回值>1</傳回值>
    </KIND>
    <KIND>
        <資料>暫停</資料>
        <傳回值>2</傳回值>
    </KIND>
    <KIND>
        <資料>停用</資料>
        <傳回值>9</傳回值>
    </KIND>
</課程狀態>
```

## Nuxt 3 前端開發規範

### 必須使用的自定義元件

**分頁元件**:

```vue
<my-paginatesimple />
```

**檔案上傳元件**:

```vue
<my-upload />
```

**XML 設定選單元件** (用於 Setup.xml 中定義的選項):

```vue
<my-xmlform @change="console.log($event)" v-model="inputs.狀態欄位名稱" type="radio" xpath="課程狀態" />
```

**資料庫關聯選單元件**:

```vue
<my-apiform />
```

**HTML 編輯器元件**:

```vue
<my-ckeditor v-model="inputs.內容欄位名稱" />
```

## 響應式設計與 UI 開發規範

### 設計要求

1. **完全響應式設計**: 使用 Tailwind CSS 確保在所有螢幕尺寸上的良好顯示
2. **圖片輪播功能**: 使用 Swiper.js 實現多圖輪播效果
3. **精確複製設計**: 包含原始設計中的所有資訊和視覺元素
4. **行動裝置優化**: 在小螢幕上重新排列內容，確保良好的使用者體驗
5. **圖標系統**: 使用 Font Awesome 圖標增強視覺效果
6. **圖片資源**: 使用 Unsplash 提供高質量的佔位圖片

### 技術實作規範

-   **樣式框架**: 使用 Tailwind CSS 實用程式類別實現設計
-   **圖片輪播**: 當需要輪播功能時使用 Swiper.js
-   **圖標系統**: 使用 Font Awesome 提供圖標
-   **圖片整合**: 整合 Unsplash 佔位符圖像，確保所有圖像都有適當的替代文字
-   **樣式管理**: 所有樣式內嵌在 HTML 中，無需額外的 CSS 文件
-   **響應式策略**: 針對小螢幕重新設計佈局，特別是表格的顯示問題
-   **統一 HTML 結構**: 大螢幕與小螢幕使用同一套 HTML 結構，透過 CSS 控制顯示

### 內容產生規範

-   **設計一致性**: 依照提供的圖案設計，保持排列與色系一致
-   **功能限制**: 不產生圖案以外的額外功能
-   **HTML 結構**: 提供功能齊全的純 HTML 結構

## 開發限制與注意事項

1. **Service 層限制**: 在 service 中不要加入 CRUD 操作
2. **程式碼品質**: 所有程式碼必須包含詳細的繁體中文註解
3. **最佳實踐**: 遵循 Laravel 和 Nuxt 3 的官方最佳實踐
4. **API 一致性**: 確保前後端 API 介面的一致性和規範性
5. **階段性開發**: 每個開發階段完成後必須等待確認再繼續

## 無障礙與 SEO 要求

-   所有圖像和圖標都必須有適當的替代文字描述
-   確保網站的可訪問性符合標準
-   優化 SEO 相關的標籤和結構
