<?php

namespace App\Http\Controllers\api;

use PF;
use PT;
use Illuminate\Http\Request;

class setupController extends Controller {
    private $data;


    /**
     *建構子.
     */
    public function __construct() {

        parent::__construct();

        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
    }
    /**
     * @OA\Get(
     *     path="/api/config",operationId="",tags={"前台/系統參數"},summary="列表",description="",

     *   @OA\Response(response=200,description="回覆",
     *     @OA\JsonContent(type="object",
     *      @OA\Property(property="resultcode",type="integer",description="訊息代碼",),
     *      @OA\Property(property="resultmessage",type="string",description="訊息內容"),

     *      @OA\Property(property="data", type="object",
     *      allOf={

     *         @OA\Schema(@OA\Property(property="id", type="string",description="編號", example="") ),
     *         @OA\Schema(@OA\Property(property="title", type="string",description="標題", example="") ),
     *     })

     *     ,)
     *),)
     */

    public function index(Request $request) {

        //        $items = [];

        // $jsonxml = json_encode($this->data['xmldoc'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);




        // // foreach ($this->data['xmldoc']  as $key => $v) {
        // //     if ($key == "權限") {
        // //         $item1 = [];
        // //         foreach ($v->選單  as $key1 => $v1) {
        // //             foreach ($v1->KIND  as $key2 => $v2) {
        // //                 $item1[] = ['id' => strval($v2->傳回值), 'title' => $v1['主選單名稱'] . " / " . strval($v2->資料)];
        // //                 foreach ($v2->KIND  as $key3 => $v3) {
        // //                     $item1[] = ['id' => strval($v3->傳回值), 'title' => $v1['主選單名稱'] . " / " . strval($v2->資料) . " / " . strval($v3->資料)];
        // //                     foreach ($v3->KIND  as $key3 => $v4) {
        // //                         $item1[] = ['id' => strval($v4->傳回值), 'title' => $v1['主選單名稱'] . " / " . strval($v3->資料) . " / " . strval($v4->資料)];
        // //                     }
        // //                 }
        // //             }
        // //         }
        // //         $items[$key] = $item1;
        // //     } else {
        // //         $item1 = [];
        // //         $c = [];
        // //         $c['資料'] = "label";
        // //         $c['傳回值'] = "value";
        // //         foreach ($v  as $key1 => $v1) {
        // //             $item = [];
        // //             foreach ($v1 as $k2 => $v2) {

        // //                 //$item[$k2] = ['value' => strval($v1->傳回值), 'label' => strval($v1->資料)];
        // //                 $key2 = strval($k2);

        // //                 if ($c[strval($k2)] != "") {
        // //                     $key2 = $c[strval($k2)];
        // //                 }
        // //                 $item[$key2] = strval($v2);
        // //             };
        // //             $item1[] = $item;
        // //             //$item1[] = ['value' => strval($v1->傳回值), 'label' => strval($v1->資料)];
        // //         }
        // //         $items[$key] = $item1;
        // //     }
        // //     // \PF::printr([$key, $v]);
        // // }

        unset($this->data['xmldoc']->權限);

        $this->jsondata['data']['setup'] = $this->data['xmldoc'];



        return $this->apiResponse($this->jsondata);
    }
}
