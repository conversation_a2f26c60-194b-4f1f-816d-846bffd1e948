
import { defineComponent } from "vue";

const RenderVnode = defineComponent({
    props: {
        vNode: {
            type: [Object, String],
            required: true,
        },
    },
    render() {
        return this.vNode;
    },
});

export default RenderVnode;
/* <template>
    <RenderVnode :vNode="node" />
</template>

<script setup lang="ts">
import { h } from "vue";
import RenderVnode from "@/utils/rendervnode";
const node = h("h1", { style: { color: "red" } }, "你好");
</script>
*/
