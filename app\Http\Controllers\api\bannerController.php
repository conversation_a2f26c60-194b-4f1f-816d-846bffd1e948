<?php

namespace App\Http\Controllers\api;

use PF;
use PT;
use Illuminate\Http\Request;


//use Illuminate\Support\Facades\DB;
/***
"功能名稱":"訊息公告",
"資料表":"board",
"備註":" ",
"建立時間":"2022-01-18 17:27:28",
 ***/
class bannerController extends Controller {
    private $data;


    /**
     *建構子.
     */
    public function __construct() {
        //$this->limit="xx";
        parent::__construct();

        //將request全部導入到$this->data變數中
        $this->data = PF::requestAll($this->data);
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
    }

    /**
     * @OA\Post(
     *     path="/api/banner",operationId="index",tags={"前台/BANNER"},summary="列表",description="",
     *     @OA\RequestBody(required=true,
     *      @OA\JsonContent(
     *      allOf={

     *         @OA\Schema(@OA\Property(property="page",description="頁數",type="integer",example="1",)),
     *         @OA\Schema(@OA\Property(property="pagesize",description="筆數/頁",type="integer",example="10",)),

     *     })

     *   ,),

     *   @OA\Response(response=200,description="回覆",
     *     @OA\JsonContent(type="object",
     *      @OA\Property(property="resultcode",type="integer",description="訊息代碼",),
     *      @OA\Property(property="resultmessage",type="string",description="訊息內容"),
     *      @OA\Property(property="data", type="object",
     *          @OA\Property(property="current_page", type="integer",description="目前頁數", ),
     *          @OA\Property(property="total", type="integer",description="總頁數", ),

     *      @OA\Property(property="data",  type="array",
     *      @OA\Items(allOf={
     *         @OA\Schema(ref="#/components/schemas/banner"),

     *     }))

     *      ),)
     * ),)
     */
    public function index(Request $request) {

        $rows = \DB::table('board')->selectRaw('id,title,field1 as img,memo as url,field9 as target');

        $rows->whereRaw('convert(ifnull(begindate,CURDATE()),DATE)<=CURDATE() and convert(ifnull(closedate,CURDATE()),DATE)>=CURDATE()');
        $rows->myWhere('kind|S', 'banner', 'kind', 'Y');
        $rows->orderByRaw('boardsort');

        $pagesize = (is_numeric($request->input('pagesize')) ? $request->input('pagesize') : 999);
        $rows = $rows->paginate($pagesize);
        foreach ($rows as $key => $rs) {


            unset($rs->adminuser_name);
            unset($rs->updated_at);

            $rs->img = url('/') . '/images/banner/' . $rs->img;
        }

        $this->jsondata['data'] = $rows;

        return $this->apiResponse($this->jsondata);
    }
}
