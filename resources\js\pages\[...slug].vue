<template>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-sm-12 col-lg-6">
                <div class="card text-center shadow-lg">
                    <div class="card-header bg-warning text-dark">
                        <h1>404 錯誤</h1>
                    </div>
                    <div class="card-body">
                        <h5 class="card-title">找不到頁面</h5>
                        <p class="card-text">您所尋找的頁面不存在或已被移除。</p>
                        <p class="card-text text-muted">頁面位置: {{ currentPath }}</p>
                        <button class="btn btn-primary mt-3" @click="router.back()">返回</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()

const currentPath = ref(route.fullPath)
</script>

<style scoped>
.container {
    margin-top: 50px;
    margin-bottom: 50px;
}
.card {
    border-radius: 15px;
}
.card-header {
    font-size: 1.5rem;
}
.card-body {
    font-size: 1.2rem;
}
</style>
