<template>
    <NuxtLayout :name="route.meta?.layout">
        <div class="container md-5">
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header bg-danger text-white">
                            <h1>{{ error.statusCode }} 錯誤</h1>
                        </div>
                        <div class="card-body text-start">
                            <h5 class="card-title">
                                {{
                                    error.message ||
                                    error.statusMessage ||
                                    '系統發現錯誤，請連絡管理員'
                                }}
                            </h5>
                            <div
                                v-if="error.statusCode >= 500"
                                v-html="error.stack"
                                class="alert alert-danger mt-3 error-stack"
                            ></div>
                            <div
                                class="d-flex justify-content-center mt-3"
                                v-if="router && router.history && router.history.length > 1"
                            >
                                <button class="btn btn-primary" @click="router.back()">返回</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </NuxtLayout>
</template>

<script setup lang="ts">
import { ref, reactive, getCurrentInstance, onMounted, computed } from 'vue'
const router = useRouter()
const route = useRoute()
useHead({
    title: '管理介面',
    link: [
        {
            rel: 'stylesheet',
            href: '/css/css.css'
        },
        {
            rel: 'stylesheet',
            href: '/css/bootstrap.min.css'
        }
    ]
})

const props = defineProps({
    error: Object
})

const { error } = toRefs(props)

onMounted(async () => {
    //console.log(['route', route.meta.layout])
})
</script>

<style scoped>
.container {
    margin-top: 50px;
    margin-bottom: 50px;
}
.error-stack {
    white-space: pre-wrap; /* 支援斷行 */
    word-break: break-word; /* 支援長字斷行 */
}
</style>
