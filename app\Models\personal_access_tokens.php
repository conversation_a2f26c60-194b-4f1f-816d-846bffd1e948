<?php
namespace App\Models;
use DB;
use PF;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/*swagger api document start*/
/**
 * @OA\Schema(
 *   schema="personal_access_tokens",
 *      allOf={
 *         @OA\Schema( @OA\Property(property="id", type="integer",description="自動編號", example=""  )),
*         @OA\Schema( @OA\Property(property="tokenable_type", type="string",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="tokenable_id", type="integer",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="name", type="string",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="token", type="string",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="abilities", type="string",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="last_used_at", type="string",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="expires_at", type="string",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="created_at", type="string",description="建立時間", example=""  )),
*         @OA\Schema( @OA\Property(property="updated_at", type="string",description="編輯時間", example=""  )),

 *      }
 *)
 */
/*swagger api document end*/
class personal_access_tokens extends baseModel
{
    //use SoftDeletes;//軟刪除
public $timestamps = true;
    public $table = 'personal_access_tokens';
    public $primaryKey = 'id';
    //public $incrementing = false;//取消自動編號
    protected $casts = [
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];
      //欄位必填
    public $rules = [
		'id' => 'required',

    ];
    //欄位屬於檔案類型
    public $fieldFiles = [
        
       // 'imgs' => 'public/images/personal_access_tokens',
    ];
    public $fieldInfo = [
'id'=>['title'=>'自動編號','type'=>'bigint(20) unsigned'],//
'tokenable_type'=>['title'=>'','type'=>'varchar(190)'],//
'tokenable_id'=>['title'=>'','type'=>'bigint(20) unsigned'],//
'name'=>['title'=>'','type'=>'varchar(190)'],//
'token'=>['title'=>'','type'=>'varchar(64)'],//
'abilities'=>['title'=>'','type'=>'text'],//
'last_used_at'=>['title'=>'','type'=>'timestamp'],//
'expires_at'=>['title'=>'','type'=>'timestamp'],//
'created_at'=>['title'=>'建立時間','type'=>'timestamp'],//
'updated_at'=>['title'=>'編輯時間','type'=>'timestamp'],//
];
    //public $timestamps = false;//不使用 timestamps 相關字段
    //日期欄位的儲存格式。'Y-m-d' or 'U' or ...
    //protected $dateFormat = 'Y-m-d';
    protected $fillable = ['tokenable_type','tokenable_id','name','token','abilities','last_used_at','expires_at','created_at','updated_at']; //可充許傳入的欄位
    protected $guarded =[];   //拒絶修改的欄位(fillable,guarded都設已fillable為準)
protected $dates = ['last_used_at','expires_at','created_at','updated_at'];
/*related table start*/
    //子對父(多對一)
    public function tokenable() {
        return $this->belongsTo(tokenable::class);
    }
/*related table end*/
    public static function boot()
    {
        parent::boot();
    static::creating(function ($model) {
            //$model->uuid = (string) Uuid::generate();
        });
        self::created(function ($model) {
                //$model->uuid = (string) Uuid::generate();
        });
        static::updating(function ($model) {
            // do some logging
        });
        self::updated(function ($model) {
            // $model->getChanges() 可以獲取到本次更新的欄位名稱和新值。
        // if ($model->isDirty('欄位名稱') && $model->欄位名稱 == "1") {// '欄位名稱' 的值已被修改
            //     $model->getOriginal('欄位名稱');   //原來的資料
            //     $model->toArray();
            // }
        });
        static::deleting(function ($model) {
            parent::delFieldFile($model); //刪fieldFiles定義的欄位檔案

    });
        static::deleted(function ($model) {
    // $rows = \App\Models\personal_access_tokensitem::selectRaw('id');
                // $rows->where('personal_access_tokens_id', $model->id);
                // $rows->chunk(200, function ($rows) {
                // foreach ($rows as $rs) {
                //     $rs->delete();
                // }
    });
}

}