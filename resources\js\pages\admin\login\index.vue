<template>
    <div class="login-container">
        <div class="login-box">
            <el-form
                ref="formEl"
                :model="inputs"
                class="login-form"
                autocomplete="on"
                label-position="left"
                v-loading="http.getLoading()"
                @submit.prevent="onSubmit"
            >
                <div class="title-container">
                    <h1 class="title">{{ store.config.name }}</h1>
                    <p class="subtitle">歡迎回來，請登入您的帳號</p>
                </div>

                <div class="form-content">
                    <el-form-item prop="account" :rules="[{ required: true, message: '請輸入帳號' }]">
                        <el-input v-model="inputs.account" placeholder="請輸入帳號" :prefix-icon="User" size="large" />
                    </el-form-item>

                    <el-form-item prop="password" :rules="[{ required: true, message: '請輸入密碼' }]">
                        <el-input
                            v-model="inputs.password"
                            type="password"
                            placeholder="請輸入密碼"
                            :prefix-icon="Lock"
                            show-password
                            size="large"
                        />
                    </el-form-item>

                    <div class="login-options">
                        <el-form-item prop="iskeep">
                            <el-checkbox v-model="inputs.iskeep">記住帳號</el-checkbox>
                        </el-form-item>
                    </div>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-block">登入</button>
                    </div>
                    <p></p>
                    <div class="d-grid">
                        <button type="button" class="btn btn-light btn-block" @click="utils.location('../')">返回首頁</button>
                    </div>
                </div>
            </el-form>
        </div>
    </div>
</template>

<script setup lang="ts">
useHead({
    title: '管理介面',
    link: []
})
import { ref, reactive, getCurrentInstance, onMounted, computed } from 'vue'
import { User, Lock } from '@element-plus/icons-vue'

const store = useDataStore()

const http = createHttp()

//store.setup.放置位置
// const props = defineProps({
//     edit: {
//         required: true,
//         type: String,
//     },
// });

// 使用 reactive 定義對象
const datas = ref([])
const formEl = ref(null)
const inputs = reactive({
    account: store.adminuser.iskeep ? store.adminuser.account : '',
    password: '',
    iskeep: store.adminuser.iskeep ? true : false,
    google_recaptcha_token: ''
})

const { getRecaptchaToken } = useRecaptcha()

const templetedata = reactive({})
const onSubmit = async () => {
    if (!formEl.value) return

    try {
        const valid = await formEl.value.validate()

        if (valid) {
            try {
                store.adminuser.api_token = ''

                inputs.google_recaptcha_token = await getRecaptchaToken('submit')
                let rep = await http.post('api/adminlogin', inputs)
                if (rep.resultcode == '0') {
                    Object.assign(store.adminuser, rep.data)
                    store.adminuser.iskeep = inputs.iskeep
                    store.adminuser.role = parseInt(rep.data.role)

                    if (inputs.iskeep) {
                        store.adminuser.account = inputs.account
                    } else {
                        store.adminuser.account = ''
                    }
                    // store.$subscribe((mutation, state) => {
                    //     console.log("Something changed!");
                    //     utils._alert(rep.resultmessage, "success");
                    //     router.push({ path: "/admin/adminuserloginlog" });
                    // });
                    document.querySelector('.grecaptcha-badge').style.visibility = 'hidden'

                    utils.toast(rep.resultmessage)
                    navigateTo({ path: '/admin/adminuserloginlog', query: {} })

                    // utils._alert(rep.resultmessage, "success");
                    // router.push({ path: "/admin/adminuserloginlog" });
                    //let x = store.get("adminuser.api_token");
                    // store.$subscribe((mutation, state) => {
                    //     console.log(["state", state]);

                    // });
                } else {
                    throw new Error(rep.resultmessage)
                }
            } catch (error) {
                utils.formElError(error)
                console.error(error)
            }
        }
    } catch (error) {
        console.error('Validation error:', error)
    }
}
onMounted(() => {
    if (process.env.NODE_ENV == 'development') {
        utils.getFakeData('adminlogin', inputs)
    }
})
</script>

<style lang="scss" scoped>
.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px;

    .login-box {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 16px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
        padding: 40px;
        width: 100%;
        max-width: 420px;
        margin: 0 auto;
    }

    .title-container {
        text-align: center;
        margin-bottom: 40px;
        padding: 0 20px;

        .title {
            font-size: 28px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            line-height: 1.2;
        }

        .subtitle {
            color: #666;
            font-size: 14px;
            margin: 0;
        }
    }

    .form-content {
        padding: 0 20px;

        :deep(.el-input) {
            .el-input__wrapper {
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
                border-radius: 8px;
                height: 44px;
                padding: 0 15px;

                &:hover {
                    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
                }

                .el-input__inner {
                    height: 100%;
                    line-height: 44px;
                }
            }
        }

        .el-form-item {
            margin-bottom: 20px;

            &:last-child {
                margin-bottom: 0;
            }
        }
    }

    .login-options {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24px;
        padding: 0 2px;

        :deep(.el-checkbox) {
            height: 32px;
            display: flex;
            align-items: center;
        }

        .forgot-password {
            color: #666;
            text-decoration: none;
            font-size: 14px;
            padding: 8px 0;

            &:hover {
                color: #409eff;
            }
        }
    }

    :deep(.el-form-item) {
        margin-bottom: 20px;

        .el-form-item__content {
            display: flex;
            align-items: center;
        }
    }
}

@media (max-width: 768px) {
    .login-container {
        padding: 16px;
        background: linear-gradient(145deg, #667eea 0%, #764ba2 100%);

        .login-box {
            padding: 24px;
            margin: 10px;
            width: calc(100% - 20px);
        }

        .title-container {
            padding: 0 10px;

            .title {
                font-size: 24px;
            }
        }

        .form-content {
            padding: 0 10px;
        }
    }
}
</style>
