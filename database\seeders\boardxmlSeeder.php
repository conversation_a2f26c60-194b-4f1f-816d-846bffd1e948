<?php

namespace Database\Seeders;

use App\Repositories\boardRepository;

class boardxmlSeeder extends baseSeeder
{
    private $boardRepo;

    public function __construct(boardRepository $boardRepo)
    {
        parent::__construct();

        $this->boardRepo = $boardRepo;
    }

    /**
     * Run the database seeds.
     */
    public function run()
    {
        $this->boardRepo->select()
        ->myWhere('kind|S', 'news', 'del', 'Y')
        ->delete();

        foreach ($this->data['xmldoc']->xpath('//參數設定檔/新聞種類/KIND') as $v) {
            $data = [
                'kind' => 'news',
                'kind_id' => $v->傳回值,
                'title' => '花盒子飲食生活公告',
                'location' => 'focus,featured',
                'body' => $this->faker->realText(),
                //'field1' => $faker->file('C:\AppServ\laravel\1\Pictures\banner.jpg', 'C:\AppServ\laravel\e\storage\app\public\images\news', false),
                //'field1' => $this->faker->image(public_path('images/news'), 390, 290, 'cats', false),
                // 'field2' => '(02)2314-6871',
                // 'field3' => '(02)2331-8047',
                //'body' => $faker->randomHtml(2, 3),
                'hits' => 0,
                'begindate' => date('Y-m-d', strtotime(date('Y-m-d', strtotime('-1 month')))),
            ];
            $this->boardRepo->create($data);
        }

        // $this->call(UsersTableSeeder::class);
    }
}
