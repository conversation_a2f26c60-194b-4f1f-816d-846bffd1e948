<?php

namespace Tests;

use Illuminate\Contracts\Console\Kernel;
use Illuminate\Foundation\Application;

trait CreatesApplication {
    public function createApplication(): Application {
        $app = require __DIR__ . '/../bootstrap/app.php';

        // 確保測試環境設定
        $app->detectEnvironment(function () {
            return 'testing';
        });

        $app->make(Kernel::class)->bootstrap();

        // 清除快取以確保測試環境純淨
        $this->clearCache();

        return $app;
    }

    /**
     * 清除 Laravel 快取
     * 確保測試環境使用最新配置
     */
    protected function clearCache(): void {
        $commands = ['clear-compiled', 'cache:clear', 'view:clear', 'config:clear', 'route:clear'];
        foreach ($commands as $command) {
            try {
                \Illuminate\Support\Facades\Artisan::call($command);
            } catch (\Exception $e) {
                // 忽略清除快取時的錯誤，但記錄日誌
                error_log("清除快取 {$command} 時發生錯誤: " . $e->getMessage());
            }
        }
    }
}
