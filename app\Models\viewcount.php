<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

//人數統計
class viewcount extends Model
{
    public $tabletitle = '人數統計';
    public $table = 'viewcount';
    public $primaryKey = 'kind';

    //欄位必填
    public $rules = [
    ];
    public $fieldnicknames = [
'hits' => '點率閱',
'created_at' => '建立時間',
'updated_at' => '編輯時間',
];
    //日期欄位的儲存格式。'Y-m-d' or 'U' or ...
    //protected $dateFormat = 'Y-m-d';

    protected $guarded = [];
    protected $dates = ['viewcountdate','created_at','updated_at'];

    public static function boot()
    {
        parent::boot();

       
    }
}
