// plugins/add-middleware.js
export default defineNuxtPlugin(() => {
    addRouteMiddleware('global-middlewar',
        (to, from) => {
            //console.clear();
            // console.log(["to,form", to, from]);
            // to.meta.props = {
            //     ...to.meta.props, // 保留现有的 props
            //     ...to.query       // 将 query 的键值对复制到 props
            // }
            // console.log(["to,form", to, from]);
        },

        { global: true }
    );
});