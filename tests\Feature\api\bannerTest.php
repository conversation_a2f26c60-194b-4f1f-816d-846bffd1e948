<?php

namespace Tests\Feature\api;

use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\board;

/**
 * Banner控制器測試類
 */
class bannerTest extends baseTest {
    /**
     * 設定測試環境
     */
    public function setUp(): void {
        parent::setUp();
        
        // 建立測試用的banner資料
        $this->banner = board::factory()->create([
            'title' => '測試Banner',
            'field1' => 'test-image.jpg', // img欄位
            'memo' => 'https://example.com', // url欄位
            'field9' => '_blank', // target欄位
            'kind' => 'banner',
            'begindate' => now(),
            'closedate' => now()->addDays(7),
            'boardsort' => 1
        ]);
    }
    
    /**
     * 測試取得Banner列表_index
     */
    public function test_取得Banner列表_index() {
        // 發送請求取得banner列表
        $response = $this->post('/api/banner', [
            'page' => 1,
            'pagesize' => 10
        ]);
        
        // 檢查回應狀態碼
        $response->assertStatus(200);
        
        // 檢查JSON回應格式
        $this->checkJson($response);
        
        // 檢查回應中包含預期的資料結構
        $response->assertJsonPath('resultcode', 0);
        $response->assertJsonPath('data.current_page', 1);
        
        // 檢查回應中包含剛才建立的banner資料
        $response->assertJsonPath('data.data.0.title', $this->banner->title);
        $response->assertJsonPath('data.data.0.target', $this->banner->field9);
        
        // 檢查資料庫中存在測試資料
        $this->assertDatabaseHas('board', [
            'title' => $this->banner->title,
            'kind' => 'banner'
        ]);
    }
}