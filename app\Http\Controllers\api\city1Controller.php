<?php

namespace App\Http\Controllers\api;

use Illuminate\Http\Request;
//use Illuminate\Support\Facades\DB;
use App\Repositories\city1Repository;

class city1Controller extends Controller {
    private $data;
    private $city1Repo;

    /**
     *建構子.
     */
    public function __construct(city1Repository $city1Repo) {
        //$this->limit="xx";
        parent::__construct();

        $this->city1Repo = $city1Repo;
    }
    /**
     * @OA\Get(
     *     path="/api/city1",operationId="",tags={"前台/縣市"},summary="列表",description="",
     *   @OA\Response(response=200,description="回覆",
     *     @OA\JsonContent(type="object",
     *      @OA\Property(property="resultcode",type="integer",description="訊息代碼",),
     *      @OA\Property(property="resultmessage",type="string",description="訊息內容"),

     *      @OA\Property(property="data", type="object",
     *      allOf={

     *         @OA\Schema(@OA\Property(property="city1title", type="string",description="縣市", example="") ),
     *     })

     *     ,)
     *),)
     */

    public function index(Request $request) {

        $rows = $this->city1Repo->selectRaw('city1title');

        $rows = $rows->orderByRaw('sortnum desc');
        $rows = $rows->get();


        $this->jsondata['data'] = $rows;

        return response()->json($this->jsondata, 200, ['Content-Type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);
    }
}
