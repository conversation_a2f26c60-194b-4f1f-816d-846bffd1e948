@if (count($data['rowsbanner1'] )>0)

<section id="coverPhoto" class="carousel slide" data-ride="carousel">
    <ol class="carousel-indicators">
        @php
        $i=0;
        @endphp
        @foreach ($data['rowsbanner1'] as $rs)
        <li data-target="#coverPhoto" data-slide-to="{{$i}}" class="{{ ($i==0) ? 'active' : ''}}"></li>
        @php
        $i++;
        @endphp
        @endforeach

    </ol>
    <div class="carousel-inner">
        @php
        $i=0;
        @endphp
        @foreach ($data['rowsbanner1'] as $rs)


        <div class="carousel-item {{ ($i==0) ? 'active' : ''}}"
            style="background-image:url('{{ url('/') }}/images/banner/{{$rs->field1}}')">
            @php
            $i++;
            $url=$rs->memo=="" ? "#" : $rs->memo;
            @endphp

            <a href="{{$url}}" target="{{$rs->field9}}" title="{{$rs->title}}"
                onclick="PF_Hits('board','id','{{$rs->id}}');">

                {{
              Html::myUIImage([
                  'folder' => "images/banner",
                  'filename' => $rs->field1,
                  'alt' => $rs->title,
                  //'width' => 230,
                  
                  'class' => 'd-block w-100',
              ])
      }}

            </a>
        </div>

        @endforeach

    </div>
    <a class="carousel-control-prev" href="#coverPhoto" role="button" data-slide="prev">
        <span class="carousel-control-prev-icon" aria-hidden="true"></span>
        <span class="sr-only">Previous</span>
    </a>
    <a class="carousel-control-next" href="#coverPhoto" role="button" data-slide="next">
        <span class="carousel-control-next-icon" aria-hidden="true"></span>
        <span class="sr-only">Next</span>
    </a>
</section><!-- banner end -->
@endif