<template>
    <my-breadcrumb id="config"></my-breadcrumb>

    <el-form ref="formEl" width="98%" :model="datas" v-loading="http.getLoading()" @submit.prevent="onSubmit">
        <div class="form-group row" v-for="(rs, index) in datas">
            <div class="col-md-2">{{ rs.title }}</div>
            <div class="col-md-10">
                <el-form-item prop="rs.name">
                    <el-input v-model="rs.value" type="text" />
                    <span v-if="rs.title != rs.memo">{{ rs.memo }}</span>
                </el-form-item>
            </div>
        </div>
        <div align="center">
            <button type="submit" class="btn btn-primary">確定</button>
             
            <button type="reset" class="btn btn-secondary" @click="formEl.resetFields()">取消</button>
             
        </div>
    </el-form>
</template>

<script setup lang="ts">
import { ref, reactive, getCurrentInstance, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { createHttp } from '@/utils/http' //http套件
const http = createHttp() //http套件

import { utils } from '@/utils' //工具套件
import { useDataStore } from '@/stores' //資料儲存套件
const store = useDataStore()

const router = useRouter()

// 使用 reactive 定義對象
const datas = ref([])
const formEl = ref(null)
//const templetedata = reactive({});
const getData = async () => {
    try {
        let rep = await http.post('api/admin/config')
        if (rep.resultcode == '0') {
            datas.value = rep.data
        } else {
            throw new Error(rep.resultmessage)
        }
    } catch (error) {
        utils.formElError(error)
        console.error(error)
    } finally {
    }
}
const onSubmit = async () => {
    if (!formEl.value) return

    try {
        const valid = await formEl.value.validate()
        if (valid) {
            try {
                let rep = await http.post('api/admin/config/store', datas.value)

                if (rep.resultcode == '0') {
                    utils.toast(rep.resultmessage)
                    //router.go(-1);
                    //router.push("/member/login");
                    //emits("closed-dialog", true);
                } else {
                    throw new Error(rep.resultmessage)
                }
            } catch (error) {
                utils.formElError(error)
                console.error(error)
            }
        }
    } catch (error) {
        console.error('Validation error:', error)
    }
}

onMounted(async () => {
    getData()
})
</script>
