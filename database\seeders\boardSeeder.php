<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

/***
"功能名稱":"seeder - 建立board假資料",
"資料表":"board",
"備註":" ",
"建立時間":"2022-01-18 17:05:17",
***/
class boardSeeder extends baseSeeder
{
    private $boardRepo;

    public function __construct(boardRepository $boardRepo)
    {
        parent::__construct();

        $this->boardRepo = $boardRepo;
    }

    /**
     * Run the database seeds.
     */
    public function run()
    {
        $this->boardRepo->select()
        //->myWhere('kind|S', $kind, 'del', 'Y')
        ->delete();

        $this->faker = \Faker\Factory::create('zh_TW');
        // $items = ['www.yahoo.com.tw', 'www.google.com'];
        // foreach ($items as $k => $v) {
        //     $data = [
        //     'kind' => $kind,
        //     'kindtitle' => $v,
        //   ];
        //   $this->boardRepo->create($data);
        // }
    }
}
