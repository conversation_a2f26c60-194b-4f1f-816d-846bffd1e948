<?php

namespace App\Http\Controllers\api;

use PF, PT;
use Exception, DB;
use App\Models\city2;
use Illuminate\Http\Request;
use App\Repositories\city2Repository;
use Illuminate\Support\Facades\Validator;

/***
"功能名稱":"鄉鎮",
"資料表":"city2",
"建立時間":"2024-02-19 10:13:49 ",
 ***/
class city2Controller extends Controller {

    private $data;
    private $xmlDoc;


    public function __construct() {

        //$this->limit="xx";
        parent::__construct();
        //將request全部導入到$this->data變數中
        $this->data = PF::requestAll($this->data);
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');

        //$this->data['nav'] = PT::nav($this->data['xmldoc'],"city2",$this->data['nav']);
        $this->data['displaynames'] = city2::getFieldTitleArray();
    }


    /**
     * @OA\Post(
     *     path="/api/city2",operationId="",tags={"前台/鄉鎮"},summary="列表",description="",
     *     @OA\RequestBody(required=true,
     *      @OA\JsonContent(
     *      allOf={



     *          @OA\Schema(@OA\Property(property="city1title", type="string",description="縣市", example="台北市",)),
     *     })

     *   ,),

     *   @OA\Response(response=200,description="回覆",
     *     @OA\JsonContent(type="object",
     *      @OA\Property(property="resultcode",type="integer",description="訊息代碼",),
     *      @OA\Property(property="resultmessage",type="string",description="訊息內容"),
     *      @OA\Property(property="data", type="object",


     *      @OA\Property(property="data",  type="array",
     *      @OA\Items(allOf={
     *         @OA\Schema(ref="#/components/schemas/city2"),

     *     }))

     *      ),)
     * ),)
     */


    public function index(Request $request) {
        $rows = $this->getRows($request);
        //$rows = $rows->take(10);
        //PF::dbSqlPrint($rows);
        //$rows = $rows->get();

        $rows = $rows->get();
        /*
         foreach ($rows as $key => $rs) {
          unset($rs->password);
          unset($rs->api_token);
          unset($rs->remember_token);
          unset($rs->lastlogin_ip);
         }
         */
        // 顯示sqlcmd
        $this->jsondata['data']['data'] = $rows;
        return response()->json($this->jsondata, 200, ['Content-Type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);
    }

    public function getRows($request) {
        //city1title,city2title,postal,city1id,city2id
        $rows = DB::table("city2")->selectRaw('city2.*');
        $rows->myWhere('city1title|S', $this->data['city1title'], $this->data['displaynames'], 'N'); //
        $rows->orderByRaw('city2.postal');

        return $rows;
    }
}
