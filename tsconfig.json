{
  // 根目录 tsconfig.json
  "extends": "./resources/js/.nuxt/tsconfig.json",
  "compilerOptions": {
    "strict": true,
    "skipLibCheck": true, // 跳过库文件检查
    "noEmit": true, // 不生成输出文件
    "types": [
      "@vue/runtime-core",
      "vite/client"
    ],
    "moduleResolution": "node",
    "target": "esnext",
    "module": "esnext",
    "jsx": "preserve",
    "typeRoots": [
      "./resources/js/types",
      "./node_modules/@types",
      "./resources/js/.nuxt/types"
    ],
    "paths": {
      "@/*": [
        "./resources/js/*"
      ],
      "@/types/*": [
        "./resources/js/types/*"
      ],
      "~/*": [
        "./resources/js/*"
      ],
      "#imports": [
        "./resources/js/.nuxt/imports"
      ]
    },
    "baseUrl": "."
  },
  "include": [
    "./resources/js/**/*",
    "./resources/js/types/**/*.d.ts",
    "./resources/js/.nuxt/types/**/*.d.ts"
  ],
  "exclude": [
    "node_modules",
    "dist",
    ".nuxt"
  ]
}