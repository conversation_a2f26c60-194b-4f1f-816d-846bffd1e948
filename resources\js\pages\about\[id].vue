<template>
    <main style="min-height: 550.45px">
        <my-seo
            :title="(() => data.eposttitle)()"
            :description="(() => data.epostbody)()"
            defaultTitle=""
        >
        </my-seo>
        <div class="vt-banner sm-banner-empty pn-cover-left">
            <div class="vu-abs-l-c">
                <h1 class="sm-banner-title size-26 size-md-36">
                    關於<span class="sub-title vt-value text-left">{{ data.eposttitle }}</span>
                </h1>
            </div>
        </div>

        <div class="container-fluid">
            <div class="container vt-html">
                <div class="row vt-col-loop vt-no-resize">
                    <div class="col-12">
                        <div class="row align-items-center" v-loading="http.getLoading()">
                            <p v-html="data.epostbody"></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
</template>
<script setup lang="ts">
// definePageMeta({
//     validate: async route => {
//         // 如果 id 不存在，设置默认值
//         if (!route.params.id) {
//             route.params.id = 'about'
//         }
//         return true
//     }
// })
import { ref, reactive, getCurrentInstance, onMounted, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useDataStore } from '@/stores'
const store = useDataStore()
import { createHttp } from '@/utils/http' //http套件
const http = createHttp()

import { utils } from '@/utils' //工具套件
const router = useRouter()

const route = useRoute()
const props = defineProps({
    id: {
        type: String,
        default: '',
        required: false
    }
})

const data = reactive({
    //不用加.value
    eposttitle: '',
    epostbody: ''
})

watch(
    () => route.fullPath,
    () => {
        getData()
    }
)
const getData = async () => {
    try {
        // const result = store.setup['權限'].filter(rs => rs.id == props.id)
        // if (result.length > 0) {
        //     inputs.title = result[0]['title'].split('/')[1]
        //     //            document.title = inputs.title
        // }
        let rep = await http.post('api/epost/show', { id: route.params.id || 'about' })

        if (rep.resultcode == '0') {
            Object.assign(data, rep.data)
        } else {
            data.epostbody = ''
            //throw new Error(rep.resultmessage)
        }
    } catch (error: any) {
        utils.formElError(error)

        console.error(error)
    } finally {
    }
}

getData()
onMounted(async () => {})
</script>

<style lang="scss"></style>
