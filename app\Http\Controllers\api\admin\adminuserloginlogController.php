<?php

namespace App\Http\Controllers\api\admin;

use PF, PT;
use Exception, DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\adminuserloginlog;
use App\Http\Controllers\Controller\api\admin;

/***
"功能名稱":"管理人員登入記錄",
"資料表":"adminuserloginlog",
"建立時間":"2024-06-12 14:45:22 ",
 ***/
class adminuserloginlogController extends Controller {

    private $data;
    private $xmlDoc;


    public function __construct() {

        //$this->limit="xx";
        parent::__construct();
        //將request全部導入到$this->data變數中
        $this->data = PF::requestAll($this->data);
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');

        //$this->data['nav'] = PT::nav($this->data['xmldoc'],"adminuserloginlog",$this->data['nav']);
        $this->data['displaynames'] = adminuserloginlog::getFieldTitleArray();
    }


    /**
     * @OA\Post(
     *     path="/api/admin/adminuserloginlog",security={{"bearerAuth":{}}},operationId="",tags={"後台/管理人員登入記錄"},summary="列表",description="",
     *     @OA\RequestBody(required=true,
     *      @OA\JsonContent(
     *      allOf={

     *         @OA\Schema(@OA\Property(property="page",description="頁數",type="integer",example="1",)),
     *         @OA\Schema(@OA\Property(property="pagesize",description="筆數/頁",type="integer",example="10",)),
     *         @OA\Schema(@OA\Property(property="search",description="搜尋",type="string",example="",)),
     *          @OA\Schema(@OA\Property(property="sortname", type="string",description="排序欄位", example="",)),
     *          @OA\Schema(@OA\Property(property="sorttype", type="string",description="排序方式", example="desc",)),
     *         @OA\Schema(@OA\Property(property="searchstartdate",description="開始時間",type="string",example="2021-01-01",)),
     *         @OA\Schema(@OA\Property(property="searchenddate",description="結束時間",type="string",example="2099-12-31",)),
     *     })

     *   ,),

     *   @OA\Response(response=200,description="回覆",
     *     @OA\JsonContent(type="object",
     *      @OA\Property(property="resultcode",type="integer",description="訊息代碼",),
     *      @OA\Property(property="resultmessage",type="string",description="訊息內容"),
     *      @OA\Property(property="data", type="object",
     *          @OA\Property(property="current_page", type="integer",description="目前頁數", ),
     *          @OA\Property(property="total", type="integer",description="總頁數", ),

     *      @OA\Property(property="data",  type="array",
     *      @OA\Items(allOf={
     *         @OA\Schema(ref="#/components/schemas/adminuserloginlog"),
     *         @OA\Schema(@OA\Property(property="", type="string",description="", example="") ),
     *     }))

     *      ),)
     * ),)
     */


    public function index(Request $request) {
        $rows = $this->getRows($request);
        //$rows = $rows->take(10);
        //PF::dbSqlPrint($rows);
        //$rows = $rows->get();
        $pagesize = (is_numeric($request->input('pagesize'))  ? $request->input('pagesize') : 10);
        $rows = $rows->paginate($pagesize);
        /*
         foreach ($rows as $key => $rs) {
          unset($rs->password);
          unset($rs->api_token);
          unset($rs->remember_token);
          unset($rs->lastlogin_ip);
         }
         */
        // 顯示sqlcmd
        $this->jsondata['data'] = $rows;
        return $this->apiResponse($this->jsondata);
    }

    public function getRows($request) {
        //id,account,clientip,loginstatus,logouttime,created_at,updated_at
        $rows = DB::table("adminuserloginlog")->selectRaw('adminuserloginlog.*');
        if (\Gate::check('isAdminRole', ['999']) == false) {
            $rows->where('account', \Auth::guard('admin')->user()->account);
        }
        //依條件搜尋資料的SQL語法
        $rows->myWhere($request->input('searchname'), $request->input('search'), $this->data['displaynames'], 'N');
        //依條件時間搜尋資料的SQL語法
        $rows->myWhere('convert(adminuserloginlog.created_at,DATE)|>=', $request->input('searchstartdate'), $this->data['displaynames'], 'N');
        $rows->myWhere('convert(adminuserloginlog.created_at,DATE)|<=', $request->input('searchenddate'), $this->data['displaynames'], 'N');
        if ($request->input('sortname')) {
            $rows->orderBy($request->input('sortname'), $request->input('sorttype') == "desc"  ? $request->input('sorttype') : "asc");
        } else {
            $rows->orderByRaw('adminuserloginlog.id desc');
        }
        return $rows;
    }
}
