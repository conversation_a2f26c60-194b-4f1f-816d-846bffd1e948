<script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "NewsArticle",
      "headline": "{{ $rs->title }} } - {{PF::getConfig('name')}}",
      @if ($rs->img != null)
        "image": [
          "{{$imgurl}}{{ end(explode(",",$rs->img))}}"
        ],
      @endif
      "datePublished": "{{ $rs->created_at!="" ? $rs->created_at->toIso8601String() : now()->toIso8601String() }}",
      "dateModified": "{{ $rs->created_at!="" ? $rs->created_at->toIso8601String() : now()->toIso8601String() }}",
      "author": [{
          "@type": "Person",
          "name": "{{ PF::getConfig('name') }}",
          "url": "{{ Request::getUri() }}"
        }]
    }
</script>
