<IfModule mod_rewrite.c>
    Options FollowSymLinks
    DirectoryIndex index.php index.html
    RewriteEngine on
    # 強制 www
    RewriteCond %{HTTP_HOST} ^sctt\.com\.tw$ [NC]
    RewriteRule ^(.*)$ http://www.%{HTTP_HOST}/$1 [R=301,L]

    # 強制 HTTPS
    RewriteCond %{HTTPS} off
    RewriteCond %{HTTP_HOST} ^www\.sctt\.com\.tw$ [NC]
    RewriteRule ^(.*)$ https://%{HTTP_HOST}/$1 [R=301,L]

    # 確保不重寫實際存在的檔案和目錄
    RewriteCond %{REQUEST_URI} !^public
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^([^/]+)/?$ public/index.php [L,QSA]

    # 確保不重寫實際存在的檔案和目錄
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^(admin|membercenter|member)/([^/]+)/?$ public/index.php [L,QSA]


    # 處理不帶 public 的路徑
    RewriteCond %{REQUEST_URI} !^/public/

    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^(.*)$ public/$1 [L,QSA]

</IfModule>
<files *.html>
    SetOutputFilter DEFLATE
</files>