/**
 * kind 資料表類型定義
 * * 種類
 */
declare global {
    interface kind {
        id: number
        kind: string // 種類
        kindtitle: string // 標題
        kindfield1?: string // 其他文字欄位1
        kindfield2?: string // 其他文字欄位2
        kindfield3?: string // 其他文字欄位3
        kindbody?: string // 本文
        kindint1: number // 其他數字欄位1
        kindint2: number // 其他數字欄位2
        kindint3: number // 其他數字欄位3
        alg: string // 語系
        kindsortnum?: number // 排序號碼
        created_at?: string
        updated_at?: string
    }
}

export {}
