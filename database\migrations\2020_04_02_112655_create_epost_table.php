<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

/*上稿系統*/

class CreateepostTable extends Migration {
    /**
     * Run the migrations.
     */
    public function up() {
        // if (Schema::hasTable('epost')) {
        //     Schema::dropIfExists('epost');
        // }
        Schema::create('epost', function (Blueprint $table) {
            $table->increments('id');
            $table->string('epostid', 50)->index()->comment('編號');
            $table->string('eposttitle', 100)->nullable()->comment('標題');
            $table->text('epostbody')->nullable()->comment('本文');
            $table->string('alg', 10)->nullable()->comment('語系');
            $table->integer('adminuser_id')->nullable()->comment('編輯人員');
            $table->string('useraccount', 50)->nullable()->comment('編輯人員');

            $table->timestamps();

            //$table->timestamps();
        });
        \DB::statement("ALTER TABLE epost COMMENT '上稿系統'");
    }

    /**
     * Reverse the migrations.
     */
    public function down() {
        Schema::dropIfExists('epost');
    }
}
