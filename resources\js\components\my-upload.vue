<template>
    <Suspense>
        <template #default>
            <div>
                <div class="container-fluid p-1">
                    <div class="upload-container">
                        <!-- Upload Area -->
                        <div class="upload-area">
                            <el-upload
                                ref="uploadRef"
                                :file-list="state.localUploadFiles"
                                :headers="state.headers"
                                :action="actionUrl"
                                :multiple="limit > 1"
                                :limit="limit"
                                :accept="accept"
                                list-type="picture-card"
                                :on-error="handleError"
                                :on-success="handleSuccess"
                                :on-exceed="handleExceed"
                                :on-remove="handleRemove"
                                :on-preview="handlePreview"
                                :show-file-list="false"
                                :before-upload="handleBeforeUpload"
                            >
                                <template #trigger>
                                    <el-button type="primary" :loading="state.uploading">
                                        {{ state.uploading ? 'Uploading...' : 'Click to upload' }}
                                    </el-button>
                                </template>
                                <template #tip>
                                    <div class="el-upload__tip">
                                        {{ accept }} size : {{ width }}*{{ height }} / upload Limit :{{ limit }}
                                    </div>
                                </template>
                            </el-upload>
                        </div>
                        <!-- Preview List -->

                        <div class="preview-area">
                            <ul class="el-upload-list el-upload-list--picture-card">
                                <vue-draggable-next
                                    v-model="state.localUploadFiles"
                                    tag="div"
                                    handle=".el-upload-list__item"
                                    group="warngroup"
                                    ghost-class="ghost"
                                    class="warn-card-box"
                                    animation="300"
                                    @end="onSorted"
                                >
                                    <transition-group>
                                        <li
                                            :key="index"
                                            class="el-upload-list__item is-success animated"
                                            v-for="(rs, index) in state.localUploadFiles"
                                        >
                                            <template v-if="isImage(rs.url)">
                                                <div class="file-container" style="text-align: left; word-break: break-all">
                                                    <el-image :src="rs.url" class="el-upload-list__item-thumbnail" :fit="fit">
                                                        <template #placeholder>
                                                            <div class="image-slot">Loading<span class="dot">...</span></div>
                                                        </template>
                                                        <template #error>
                                                            <div style="text-align: center">
                                                                &nbsp; no found<br />{{ getFileName(rs.url) }}
                                                            </div>
                                                        </template>
                                                    </el-image>
                                                </div>
                                            </template>

                                            <!-- 如果不是图片则显示文件名 -->
                                            <template v-else>
                                                <div class="file-container">
                                                    <div class="file-icon">
                                                        <!-- 文件图标 SVG -->
                                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="file-type-icon">
                                                            <path
                                                                fill="currentColor"
                                                                d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"
                                                            />
                                                        </svg>
                                                    </div>
                                                    <div class="file-name">
                                                        {{ getFileName(rs.url) }}
                                                    </div>
                                                </div>
                                            </template>

                                            <label class="el-upload-list__item-status-label">
                                                <i class="el-icon el-icon--upload-success el-icon--check">
                                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
                                                        <path
                                                            fill="currentColor"
                                                            d="M406.656 706.944 195.84 496.256a32 32 0 1 0-45.248 45.248l256 256 512-512a32 32 0 0 0-45.248-45.248L406.592 706.944z"
                                                        ></path></svg></i
                                            ></label>
                                            <i class="el-icon-close"></i>
                                            <span class="el-upload-list__item-actions">
                                                <!-- 预览 -->
                                                <span
                                                    class="el-upload-list__item-preview"
                                                    @click="handlePictureCardPreviewFileDetail(rs.url)"
                                                >
                                                    <i class="el-icon el-icon--zoom-in">
                                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
                                                            <path
                                                                fill="currentColor"
                                                                d="m795.904 750.72 124.992 124.928a32 32 0 0 1-45.248 45.248L750.656 795.904a416 416 0 1 1 45.248-45.248zM480 832a352 352 0 1 0 0-704 352 352 0 0 0 0 704zm-32-384v-96a32 32 0 0 1 64 0v96h96a32 32 0 0 1 0 64h-96v96a32 32 0 0 1-64 0v-96h-96a32 32 0 0 1 0-64h96z"
                                                            ></path>
                                                        </svg>
                                                    </i>
                                                </span>
                                                <!-- 删除 -->
                                                <span class="el-upload-list__item-delete" @click="onDel(rs)">
                                                    <i class="el-icon el-icon--zoom-in">
                                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
                                                            <path
                                                                fill="currentColor"
                                                                d="M160 256H96a32 32 0 0 1 0-64h256V95.936a32 32 0 0 1 32-32h256a32 32 0 0 1 32 32V192h256a32 32 0 1 1 0 64h-64v672a32 32 0 0 1-32 32H192a32 32 0 0 1-32-32V256zm448-64v-64H416v64h192zM224 896h576V256H224v640zm192-128a32 32 0 0 1-32-32V416a32 32 0 0 1 64 0v320a32 32 0 0 1-32 32zm192 0a32 32 0 0 1-32-32V416a32 32 0 0 1 64 0v320a32 32 0 0 1-32 32z"
                                                            ></path>
                                                        </svg>
                                                    </i>
                                                </span>
                                            </span>
                                        </li>
                                    </transition-group>
                                </vue-draggable-next>
                            </ul>
                        </div>
                    </div>
                </div>
                <el-dialog v-model="state.dialogVisible" :fullscreen="true">
                    <img w-full :src="state.dialogImageUrl" style="width: 100%" alt="Preview Image" />
                </el-dialog>
                <slot></slot>
            </div>
        </template>
        <template #fallback>
            <div class="dialog-loading">
                <el-icon class="is-loading"><Loading /></el-icon>
                <span>Loading...</span>
            </div>
        </template>
    </Suspense>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref, watch } from 'vue'
//const { proxy } = getCurrentInstance();
import { VueDraggableNext } from 'vue-draggable-next'

import { useDataStore } from '@/stores'
import { utils } from '@/utils'
const store = useDataStore()
const props = defineProps({
    modelValue: [String, Object],
    width: String,
    height: String,
    accept: {
        type: String,
        required: false,
        default: 'image/*'
    },
    limit: {
        type: Number,
        required: false,
        default: 1
    },
    fit: {
        type: String,
        default: 'cover' // or whatever default value you want
    },
    folder: {
        type: String,
        required: true
    }
})
const uploadRef = ref<any>(null)

const IMAGE_EXTENSIONS = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'] as const
//let uploadFolder = ref(WEB_FOLDER + props.folder)

const emits = defineEmits(['update:modelValue'])
const state = reactive({
    dialogVisible: false,
    dialogImageUrl: '',
    uploadFolder: utils.getConfig('API_BASE') + (props.folder.startsWith('/') ? '' : '/') + `${props.folder}`.replace(/\/\//g, '/'),
    localUploadFiles: [],
    headers: {},
    uploading: false
})

// let localname = ref('')
watch(
    () => props.modelValue,
    async newValue => {
        //console.clear();
        //console.log(['newValue', newValue])
        let datas = [] //要加.value
        if (Array.isArray(newValue)) return newValue

        if (state.localUploadFiles.length > 0) {
            return
        }
        if (newValue != null && newValue != '') {
            let newValues = newValue.split(',')

            const files = newValue
                .split(',')
                .filter(file => file !== 'undefined')
                .map(name => ({
                    name,
                    url: `${state.uploadFolder}/${name}`
                }))

            state.localUploadFiles = files
        }
    },
    { deep: true, immediate: true }
)

const handleBeforeUpload = file => {
    state.uploading = true
    state.headers = {
        Authorization: `Bearer ${store.adminuser.api_token}` // Adjust based on your token storage
    }
    return true
}

const handleSuccess = (response: any, file: any, uploadFiles: any) => {
    state.uploading = false
    if (response.resultcode == '0') {
        file['name'] = response.data.name.trim()
        file['url'] = state.uploadFolder + '/' + file['name']
        state.localUploadFiles.push(file)

        updateValue()
    } else {
        throw new Error(response.resultmessage)
    }
}

const handleError = (err, file, fileList) => {
    state.uploading = false
    utils.message(err.message)
}

const onDel = async val => {
    utils.onDelRow(state.localUploadFiles, val)

    updateValue()
    resetUpload()
}

const updateValue = async () => {
    let tmpFiles = []
    state.localUploadFiles.forEach(function (rs, index) {
        tmpFiles.push(rs['name'])
    })
    tmpFiles = tmpFiles.join(',')

    emits('update:modelValue', tmpFiles)
}

const handleExceed: UploadProps['onExceed'] = (files, uploadFiles) => {
    // console.log(['state.localUploadFiles', state.localUploadFiles])
    // console.log(['files', files, uploadFiles])
    utils.toast('超出限制数量', 'error')
}

const resetUpload = () => {
    if (uploadRef.value) {
        uploadRef.value.clearFiles()
    }
}

const handleRemove: UploadProps['onRemove'] = (file, uploadFiles) => {
    state.localUploadFiles = state.localUploadFiles.filter(f => f.uid !== file.uid)
    updateValue()
    resetUpload()
}

const handlePreview = file => {
    if (file.url) {
        const newWindow = window.open(file.url, '_blank')
        newWindow.focus()
    } else {
        console.warn('Preview URL not available for this file.')
    }
}

const handlePictureCardPreviewFileDetail = (file: string) => {
    if (isImage(file)) {
        state.dialogImageUrl = file
        state.dialogVisible = true
    } else {
        window.open(file, '_blank')?.focus()
    }
}

let actionUrl = ref('')
onMounted(async () => {
    let f = props.folder.replaceAll('images/', '')

    //state.uploadFolder = `${utils.getConfig('API_BASE')}${props.folder}`.replace(/\/\//g, '/')
    console.log(['state.uploadFolder', state.uploadFolder])
    actionUrl.value = utils.getConfig('API_BASE') + 'api/admin/upload?width=' + props.width + '&height=' + props.height + '&f=' + f
})

const isImage = (url: string) => {
    const extension = url.split('.').pop()?.toLowerCase() || ''
    return IMAGE_EXTENSIONS.includes(extension as (typeof IMAGE_EXTENSIONS)[number])
}

const getFileName = (url: string) => {
    // 从 URL 中获取文件名
    return url.split('/').pop()
}

const errorCaptured = async (err, vm, info) => {
    console.error(`my-upload Error: ${vm.$options.name};message: ${err.toString()};info: ${info}`)
    return false // 可以返回 true 或 false 來決定是否繼續往上傳遞錯誤
}

const onSorted = () => {
    updateValue()
}
</script>

<style scoped>
.upload-container {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    min-height: 148px;
    flex-wrap: wrap; /* 允許內容換行 */
}

.upload-area {
    flex-shrink: 0;
    width: 148px;
}

.preview-area {
    flex: 1;
    overflow-x: auto;
    padding-bottom: 8px;
    display: flex; /* 確保圖片預覽列表橫向排列 */
    flex-wrap: wrap; /* 當有多個文件時，允許換行 */
    gap: 8px;
}

.el-upload-list--picture-card {
    display: flex;
    flex-wrap: wrap; /* 允許換行 */
    gap: 8px;
    margin: 0;
}

.el-upload-list__item {
    width: 148px;
    height: 148px;
    flex-shrink: 0;
    background-color: #fff;
    border: 1px solid #c0ccda;
    border-radius: 6px;
    box-sizing: border-box;

    margin-bottom: 8px; /* 为每个项目增加间距 */
}

.preview-area::-webkit-scrollbar {
    height: 6px;
}

.preview-area::-webkit-scrollbar-thumb {
    background-color: #909399;
    border-radius: 3px;
}

.preview-area::-webkit-scrollbar-track {
    background-color: #f4f4f5;
}
</style>
