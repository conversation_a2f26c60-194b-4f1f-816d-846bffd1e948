<template>
    <ClientOnly fallback-tag="span" fallback="Loading">
        <template v-if="type == 'checkbox'">
            <el-checkbox-group v-model="localModelValue">
                <el-checkbox
                    :key="index"
                    :value="rs.value"
                    :label="rs.label"
                    v-for="(rs, index) in localOptions"
                />
            </el-checkbox-group>
        </template>

        <template v-else-if="type == 'radio'">
            <el-radio-group v-model="localModelValue">
                <el-radio
                    :key="index"
                    :label="rs.label"
                    :value="rs.value"
                    v-for="(rs, index) in localOptions"
                >
                </el-radio>
            </el-radio-group>
        </template>
        <template v-else-if="type == 'select2'">
            <el-select-v2
                v-model="localModelValue"
                style="width: 100%"
                filterable
                v-bind="props"
                :options="localOptions"
            />
        </template>

        <template v-else>
            <el-select v-model="localModelValue">
                <el-option value="">Select</el-option>
                <el-option
                    :key="index"
                    :value="rs.value"
                    :label="rs.label"
                    v-for="(rs, index) in localOptions"
                >
                </el-option>
            </el-select>
        </template>
        <slot></slot>
    </ClientOnly>
</template>

<script setup lang="ts">
import { useDataStore } from '@/stores/index'
import type { PropType } from 'vue'
import { computed, defineEmits, defineProps } from 'vue'
import { utils } from '@/utils'
interface Option {
    value: string | number
    label: string
}

const store = useDataStore()

const emits = defineEmits<{
    (e: 'update:modelValue', value: string | string[] | number): void
    (e: 'change', value: string | string[] | number): void
}>()

const props = defineProps({
    type: {
        type: String as PropType<'select' | 'checkbox' | 'radio' | 'select2'>,
        required: true,
        validator: (value: string) => ['select', 'checkbox', 'radio', 'select2'].includes(value)
    },
    options: {
        type: Array as PropType<Option[]>
    },
    getData: {
        type: [String, Array] as PropType<string | any[]>,
        default: () => []
    },
    isFirstDisplayFlag: {
        type: Boolean,
        default: true
    },
    xpath: {
        type: String,
        required: true
    },
    className: {
        type: String,
        default: ''
    },
    modelValue: {
        type: [String, Array, Number] as PropType<string | string[] | number>,
        default: ''
    },
    required: {
        type: Boolean,
        default: false
    }
})

const localModelValue = computed({
    get() {
        if (props.type === 'checkbox') {
            if (!props.modelValue) return []
            return Array.isArray(props.modelValue)
                ? props.modelValue
                : String(props.modelValue).split(',').filter(Boolean)
        }
        return String(props.modelValue ?? '')
    },
    set(val: string | string[] | number) {
        emits('change', val)
        emits('update:modelValue', val)
    }
})

const localOptions = computed<Option[]>(() => {
    if (!props.xpath) {
        console.error('my-xmlform: xpath is required')
        return []
    }

    if (props.options) {
        return props.options
    }

    const data = store.setup[props.xpath]['KIND'] || []
    let tempData: { value: string; label: string }[] = []
    if (Array.isArray(data)) {
        tempData = data.map(rs => ({
            value: utils.isEmpty(rs['傳回值']) == false ? rs['傳回值'] : rs['資料'],
            label: String(rs['資料'])
        }))
    } else {
        tempData.push({
            value: utils.isEmpty(data['傳回值']) == false ? data['傳回值'] : data['資料'],
            label: String(data['資料'])
        })
    }
    return tempData
})

defineExpose({
    localOptions,
    localModelValue
})
</script>

<style scoped>
.el-select,
.el-select-v2 {
    width: 100%;
}

.el-checkbox-group,
.el-radio-group {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}
</style>
