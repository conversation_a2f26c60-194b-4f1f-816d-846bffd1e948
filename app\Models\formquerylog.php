<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

//formquerylog
class formquerylog extends Model
{
    protected $tabletitle = 'formquerylog';
    protected $table = 'formquerylog';
    protected $primaryKey = 'id';

    //欄位必填
    public $rules = [
    ];
    public $fieldnicknames = [
'pagename' => '功能名稱',
'pathinfo' => '程式位置',
'formbody' => 'FORM欄位值',
'querybody' => 'get內容',
'created_at' => 'Created_at',
'updated_at' => 'Updated_at',
];
    //日期欄位的儲存格式。'Y-m-d' or 'U' or ...
    //protected $dateFormat = 'Y-m-d';

    protected $guarded = [];
    protected $dates = ['created_at','updated_at'];

    public static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            //$model->uuid = (string) Uuid::generate();
        });
        self::created(function ($model) {
            //$model->uuid = (string) Uuid::generate();
        });
        static::updating(function ($model) {
            // do some logging
        });
        self::updated(function ($model) {
        });
        static::deleting(function ($model) {
        });
        static::deleted(function ($model) {
        });
    }
}
