<?php

namespace App\Http\Controllers\api\admin;

use PF;
use PT;
use Illuminate\Http\Request;

class configController extends Controller {
    private $data;
    public $filename;


    /**
     *建構子.
     */
    public function __construct() {

        parent::__construct();

        //$this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
        $this->filename = base_path('config/config.php');


        if ($this->data['alg'] == "") {
            $this->data['alg'] = "zh";
        }

        if ('' != config('config.' . $this->data['alg'] . '.name')) {
            $this->filename = base_path('config/config.' . $this->data['alg'] . '.php');
        }
    }
    /**
     * @OA\Get(
     *     path="/api/config",operationId="",tags={"前台/系統參數"},summary="列表",description="",

     *   @OA\Response(response=200,description="回覆",
     *     @OA\JsonContent(type="object",
     *      @OA\Property(property="resultcode",type="integer",description="訊息代碼",),
     *      @OA\Property(property="resultmessage",type="string",description="訊息內容"),

     *      @OA\Property(property="data", type="object",
     *      allOf={

     *         @OA\Schema(@OA\Property(property="id", type="string",description="編號", example="") ),
     *         @OA\Schema(@OA\Property(property="title", type="string",description="標題", example="") ),
     *     })

     *     ,)
     *),)
     */

    public function index(Request $request) {


        $ebody = \File::get($this->filename);


        foreach (explode(PHP_EOL, $ebody) as $key => $item) {
            $arrs = [];
            $items1 = explode(' //', $item);
            if (count($items1) > 1) {
                $items2 = explode('\'', $items1[0]);
                if (count($items2) > 0) {
                    $arrs['key'] = $items2[1];
                    $arrs['title'] = explode("(", $items1[1])[0];
                    //$items=explode("chr(13).chr(10)",$s);
                    $memo = \Str::between($items1[1], '(', ')');
                    $arrs['memo'] = $memo;
                    $arrs['value'] = $items2[3];
                }
                $this->jsondata['data'][] = $arrs;
            }
        }


        return $this->apiResponse($this->jsondata);
    }

    /**
     * 資料新增編輯寫入.
     *
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request) {


        $ebody = \File::get($this->filename);

        $body = '<?php' . PHP_EOL;
        $body .= PHP_EOL;
        $body .= 'return [' . PHP_EOL;
        $json = $request->json()->all();
        foreach (explode(PHP_EOL, $ebody) as $key => $item) {
            if (\Str::contains($item, ['=>'])) {
                $items1 = explode(' //', $item);
                if (count($items1) > 1) {
                    $items2 = explode('\'', $items1[0]);
                    if (count($items2) > 0) {
                        $rs = collect($json)->where('key', $items2[1])->first(); //->value();for json
                        $value = $rs['value'];
                        $value = str_replace("'", '', $value);
                        $value = str_replace('"', '', $value);
                        $body .= "   '" . $items2[1] . "' => '" . $value . "',  //" . $items1[1] . PHP_EOL;
                    }
                } else {
                    $body .= $item . PHP_EOL;
                }
            }
        }
        $body .= '];' . PHP_EOL;



        \File::put($this->filename, $body);


        \Artisan::call('config:clear');
        \Artisan::call('config:cache');
        $this->jsondata['resultmessage'] = '更新成功';
        return response()->json(
            $this->jsondata,
            200,
            ['Content-Type' => 'application/json; charset=utf-8'],
            JSON_UNESCAPED_UNICODE
        );
    }
}
