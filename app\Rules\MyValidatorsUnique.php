<?php

namespace App\Rules;

use DB;
use App\Libraries\PF;
use Illuminate\Contracts\Validation\Rule;

/***
"功能名稱":"Validators-判斷是否為唯一",
"資料表":"",
"備註":" ",
"建立時間":"2022-01-18 16:51:34",
 ***/
class MyValidatorsUnique implements Rule {
    public $table_name;

    public $keyWheres;

    public $exWheres;

    /**
     * Create a new rule instance.
     */
    public function __construct($tableName, $keyWheres, $exWheres = null) {
        $this->tableName = $tableName;
        $this->keyWheres = $keyWheres;
        $this->exWheres = $exWheres;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param string $attribute
     * @param mixed  $value
     *
     * @return bool
     */

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message() {
        //return 'The validation error message.';
        return '[:attribute] 請勿重覆';
    }

    public function passes($attribute, $value) {
        $isOld = false;
        foreach ($this->keyWheres as $k => $v) {
            if ('' != $v) {
                $isOld = true;
            }
        }
        //已有資料
        if ($isOld) {
            //PF::printr('old');
            $rows = DB::table($this->tableName)->select($attribute);
            if (null != $this->exWheres) {
                $rows->where($this->exWheres);
            }
            $rows->where($this->keyWheres);

            $rows = $rows->get();
            if ($rows->count() > 0) {
                $rs = $rows->first();
                $rs = get_object_vars($rs);
                $oldValue = $rs[$attribute];

                if ($oldValue == $value) {
                    return true;
                } else {
                    $rows = DB::table($this->tableName)->select($attribute);
                    if (null != $this->exWheres) {
                        $rows->where($this->exWheres);
                    }
                    $rows->where($attribute, $value);
                    $rows = $rows->get();
                    if ($rows->count() > 0) {
                        return false;
                    }

                    return true;
                }
            }
        } else {
            //新資料
            $rows = DB::table($this->tableName)->select($attribute);
            if (null != $this->exWheres) {
                $rows->where($this->exWheres);
            }
            $rows->where($attribute, $value);
            $rows = $rows->get();
            if ($rows->count() > 0) {
                return false;
            }
        }

        return true;
    }
}
