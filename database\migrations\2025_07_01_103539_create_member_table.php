<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
//php artisan migrate:refresh --path=/database/migrations/_create_member_table.php
return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        if (!Schema::hasTable('member')) {
            Schema::create('member', function (Blueprint $table) {
                //$table->engine = 'MyISAM';
                $table->increments('id')->from(10000)->comment('會員編號');
                $table->string('email', 100)->unique()->comment('電子信箱');
                $table->string('password')->comment('密碼');
                $table->string('name', 50)->comment('姓名');
                $table->string('phone', 20)->nullable()->comment('手機號碼');
                $table->date('birth')->nullable()->comment('生日');
                $table->string('gender', 10)->nullable()->comment('性別');
                $table->tinyInteger('member_type')->default(1)->comment('會員類型'); // 1:一般會員 2:VIP會員 3:教練
                $table->tinyInteger('status')->default(1)->comment('會員狀態'); // 1:正常 2:停權
                $table->text('bio')->nullable()->comment('個人簡介');
                $table->string('avatar')->nullable()->comment('頭像');
                $table->decimal('wallet_balance', 10, 2)->default(0)->comment('錢包餘額');
                $table->dateTime('email_verified_at')->nullable()->comment('信箱驗證時間');
                $table->string('remember_token')->nullable()->comment('記住我令牌');
                $table->timestamps();

                // 建立索引
                $table->index(['email']);
                $table->index(['member_type']);
                $table->index(['status']);
            });
            \DB::statement("ALTER TABLE member COMMENT 'XX'");
        }
        /*
        $table->unsignedBigInteger('activitysession_id')->comment('場次');
        $table->foreign('activitysession_id')->references('id')->on('activitysession')->onDelete('cascade');

        $table->string('kind',50)->index()->comment('種類');
        $table->mediumText('body')->nullable()->comment('說明');
        $table->dateTime('begindate')->nullable()->comment('開始時間');
        $table->integer('hits')->default(0)->comment('點率次數');
        $table->float('sortnum', 5, 3)->nullable()->comment('排序號碼');
        $table->integer('adminsuer_id')->nullable()->comment('編輯人員');
        $table->string('adminuser_name', 50)->nullable()->comment('編輯人員');
        $table->string('edit_account',50)->comment('修改人');
        $table->string('account',50)->unique();;
        $table->timestamps('reviewed_at')->default('now');
        $table->unique(array('kind', 'kindid'));
        $table->index(array('kind', 'kindid'));
        $table->softDeletes();

        */
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::dropIfExists('member');
    }
};
