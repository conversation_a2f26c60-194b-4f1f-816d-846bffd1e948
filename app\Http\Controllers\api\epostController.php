<?php

namespace App\Http\Controllers\api;

use PF, PT;
use Exception, DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\product;
use App\Http\Controllers\Controller\api;

/***
"功能名稱":"產品",
"資料表":"product",
"建立時間":"2024-10-26 21:49:17 ",
 ***/
class epostController extends Controller {

    private $data;
    private $xmlDoc;


    public function __construct() {

        //$this->limit="xx";
        parent::__construct();
        //將request全部導入到$this->data變數中
        $this->data = PF::requestAll($this->data);
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');

        //$this->data['nav'] = PT::nav($this->data['xmldoc'],"product",$this->data['nav']);
        $this->data['displaynames'] = product::getFieldTitleArray();
    }



    /**
     * @OA\Post(
     *     path="/api/epost/show",operationId="",tags={"前台/上稿系統"},summary="單筆顯示",description="",
     *     @OA\RequestBody(required=true,
     *      @OA\JsonContent(
     *      allOf={

     *         @OA\Schema(@OA\Property(property="id",description="編號",type="integer",example="1",)),

     *     })
     *   ,),

     *   @OA\Response(response=200,description="回覆",
     *     @OA\JsonContent(type="object",
     *      @OA\Property(property="resultcode",type="integer",description="訊息代碼",),
     *      @OA\Property(property="resultmessage",type="string",description="訊息內容"),

     *      @OA\Property(property="data", type="object",
     *      allOf={
     *         @OA\Schema(ref="#/components/schemas/product"),
     *         @OA\Schema(type="object",@OA\Property(property="", type="string",description="系列", example="") ),

     *     })

     *     ,)
     *),)
     */


    public function show($request) {



        $rows = \App\Models\epost::selectRaw('epost.*');
        $rows->where('epostid', '=', $request->input('id'));

        $rs = $rows->first();

        $rs['eposttitle'] = PF::xmlSearch($this->data['xmldoc'], '//參數設定檔/權限/選單/KIND/傳回值', '資料', $request->input('id'));
        if ($rs != null) {
            //   <權限 ReturnFlag="1">
            //         <選單 主選單名稱="內頁維護" 主編號="3">
            //             <KIND 角色="999">
            //                 <資料>關於我們</資料>
            //                 <傳回值>about</傳回值>
            //                 <網址>epost/edit?edit=about</網址>
            //                 <形態>html</形態>
            //             </KIND>

            /*
             unset($rs->password);
             unset($rs->api_token);
             unset($rs->remember_token);
             unset($rs->lastlogin_ip);
             */
            $this->jsondata['data'] = $rs;
        }

        return $this->apiResponse($this->jsondata);
    }
}
