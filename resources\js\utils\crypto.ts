import CryptoJS from 'crypto-js'
export const crypto = {
    encryptData(plainText: string) {
        // 定義密鑰與 IV，請確保與後端設定一致

        const key = CryptoJS.enc.Utf8.parse('Abc!2345XyZ@6789QwErTyUiOpAsDfGh') // 32字元
        const iv = CryptoJS.enc.Utf8.parse('0Fp0u1uSG5Bj4LuT') // 16字元

        // 使用 AES 加密，模式使用 CBC，填充使用 Pkcs7
        const encrypted = CryptoJS.AES.encrypt(plainText, key, {
            iv: iv,
            mode: CryptoJS.mode.CBC,
            padding: CryptoJS.pad.Pkcs7
        })
        // 回傳 Base64 字串
        return encrypted.toString()
    },
    encryptDataJson(plainText: string) {
        plainText = JSON.stringify(plainText)

        return this.encryptData(plainText)
    }
}
