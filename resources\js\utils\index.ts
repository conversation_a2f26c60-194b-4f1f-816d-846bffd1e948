import { useRequestURL, useRuntimeConfig } from '#imports' // 導入 Nuxt 3 的組合式函數
import { useDataStore } from '@/stores' //資料儲存套件
import { createHttp } from '@/utils/http' //http套件
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'

export const utils = {
    // mode: NODE_ENV,
    folder: '/',
    mode: process.env.NODE_ENV,
    urlApi: process.env.API_BASE,
    get url() {
        // 使用 getter 確保在運行時獲取正確的 URL
        return useRequestURL().origin + '/'
    },

    /**
     * 獲取運行時配置中的指定值
     * @param {string} key - 配置的鍵名
     * @returns {any} 返回對應鍵名的值
     */
    getConfig(key: string) {
        const config = useRuntimeConfig() // 读取运行时配置
        const value = config.public[key] // 读取 API_BASE
        return value
    },

    /**
     * 清空物件中的所有鍵值對
     * @param {Object} data - 要清空的物件
     */
    mapClear(data: Record<string, any>): void {
        Object.keys(data).forEach(key => {
            delete data[key]
        })
    },

    /**
     * 檢查資料是否為空
     * @param {any} data - 要檢查的資料，可以是任意類型
     * @returns {boolean} 如果資料為空則返回true，否則返回false
     */
    isEmpty(data: any): boolean {
        // 如果是 null 或 undefined
        if (data == null) {
            return true
        }

        // 如果是布林值
        if (typeof data === 'boolean') {
            return false
        }

        // 如果是數字
        if (typeof data === 'number') {
            return Number.isNaN(data)
        }

        // 如果是字串
        if (typeof data === 'string') {
            return data.trim().length === 0
        }

        // 如果是陣列
        if (Array.isArray(data)) {
            return data.length === 0
        }

        // 如果是物件
        if (typeof data === 'object') {
            return Object.keys(data).length === 0
        }

        // 如果是函數
        if (typeof data === 'function') {
            return false
        }

        // 其他情況
        return true
    },

    /**
     * 檢查資料是否為有效的數字
     * @param {any} data - 要檢查的資料，可以是任意類型
     * @returns {boolean} 如果資料是有效數字則返回true，否則返回false
     */
    isNumber(data: any): boolean {
        try {
            // 如果是 null 或 undefined，返回 false
            if (data == null) {
                return false
            }

            // 如果是布林值，返回 false
            if (typeof data === 'boolean') {
                return false
            }

            // 如果是數字類型但是 NaN，返回 false
            if (typeof data === 'number' && isNaN(data)) {
                return false
            }

            // 如果是數字類型且不是 NaN，返回 true
            if (typeof data === 'number') {
                return true
            }

            // 如果是字串，檢查是否可以轉換為有效數字
            if (typeof data === 'string') {
                // 去除首尾空格
                const trimmed = data.trim()

                // 空字串不是數字
                if (trimmed === '') {
                    return false
                }

                // 檢查是否為有效數字（包括整數、小數、科學記號）
                // parseFloat 會嘗試轉換字串為數字
                // isFinite 確保不是 Infinity 或 -Infinity
                return !isNaN(parseFloat(trimmed)) && isFinite(trimmed)
            }

            // 其他所有類型都返回 false
            return false
        } catch (error) {
            console.error(error.message + '(' + data + ')')
        }
    },

    /**
     * 顯示確認框
     * @param {string} title - 確認框的標題
     * @returns {Promise<string>} 返回一個Promise，解析為成功或失敗的消息
     */
    confirm(title: string): Promise<string> {
        return new Promise((resolve, reject) => {
            ElMessageBox.confirm(title, {
                confirmButtonText: 'OK',
                cancelButtonText: 'Cancel',
                type: 'warning'
            })
                .then(() => {
                    resolve('成功')
                })
                .catch(() => {
                    reject('失敗')
                })
        })
    },

    /**
     * 顯示通知
     * @param {string} message - 通知消息內容
     * @param {string} [type='success'] - 通知類型，可選值如 'success', 'error'，默認為 'success'
     * @param {number} [duration=1000] - 通知顯示的持續時間（毫秒），默認為 1000
     */
    toast(message: string, type: string = 'success', duration: number = 1000) {
        try {
            const title = type.charAt(0).toUpperCase() + type.slice(1).toLowerCase()
            ElNotification({
                title,
                message,
                type,
                offset: 0,
                duration: duration
            })
        } catch (error) {
            console.error('Toast notification error:', error)
        }
    },

    /**
     * 顯示警告框
     * @param {string} message - 警告消息內容
     * @param {string} [title=''] - 警告框標題，默認為空字串
     * @param {string} [type='info'] - 警告框類型，可選值如 'info', 'error'，默認為 'info'
     */
    alert(message: string, title = '', type: 'info' | 'warning' | 'error' | 'success' = 'info'): void {
        ElMessageBox.alert(message, title, {
            dangerouslyUseHTMLString: true,
            type: type
        })
    },

    /**
     * 顯示消息提示框
     * @param {string} message - 消息內容
     * @param {string} [type='error'] - 消息類型，可選值如 'error', 'success'，默認為 'error'
     */
    message(message: string, type: string = 'error') {
        if (!message) {
            console.error('Message cannot be empty')
            return // 防止发送空消息
        }

        try {
            ElMessage({
                showClose: true,
                dangerouslyUseHTMLString: true,
                message,
                type
            })
        } catch (error) {
            console.error('Error displaying message:', error)
        }
    },

    /**
     * 去除字串左邊的空白
     * @param {string} str - 要處理的字串
     * @returns {string} 返回去除左邊空白後的字串
     */
    ltrim(str: string): string {
        // 检查输入是否为字符串
        if (typeof str !== 'string') {
            //console.error('输入必须是字符串');
            return str // 如果不是字符串，直接返回原值
        }

        // 使用正则表达式去除左侧空白
        return str.replace(/^\s+/, '')
    },

    /**
     * 去除字串右邊的空白
     * @param {string} str - 要處理的字串
     * @returns {string} 返回去除右邊空白後的字串
     */
    rtrim(str: string): string {
        if (typeof str !== 'string') {
            //console.error('输入必须是字符串:' + str);
            return str
        }

        // 使用正则表达式去除右侧空白
        return str.replace(/\s+$/, '')
    },

    /**
     * 去除字符串两边的空白
     * @param {string} str - 要處理的字串
     * @returns {string} 返回去除两边空白后的字符串
     */
    trim(str: string): string {
        return this.ltrim(this.rtrim(str))
    },

    /**
     * 全選或全不選，返回對應的 ID 陣列
     * @param {boolean} isAll - 是否全選，true 為全選，false 為全不選
     * @param {Object} data - 包含資料的物件，需有 data 屬性為陣列
     * @returns {Array} 返回選中的 ID 陣列，若不全選則返回空陣列
     */
    checkboxAll(isAll: boolean, data: { data: Array<{ id: number }> }): number[] {
        let values = []
        if (isAll) {
            values = data.data.map(item => item.id)
        }
        return values
    },

    /**
     * 对数据进行排序
     * @param {Array} data - 要排序的數據陣列
     * @param {string} name - 用於排序的屬性名稱
     * @param {string} dirt - 排序方向，'asc' 表示升序，'desc' 表示降序
     * @returns {Array} 排序後的數據陣列
     */
    onSortDirt<T extends Record<string, any>>(data: T[], name: keyof T, dirt: 'asc' | 'desc'): T[] {
        console.log('onSortDirt' + name + dirt)
        if (dirt === 'asc') {
            return [...data].sort((a, b) => (a[name] > b[name] ? 1 : a[name] === b[name] ? (a.size > b.size ? 1 : -1) : -1))
        } else {
            return [...data].sort((a, b) => (a[name] < b[name] ? 1 : a[name] === b[name] ? (a.size > b.size ? 1 : -1) : -1))
        }
    },
    getDateTime(fmt = 'yyyy-MM-dd') {
        const now = new Date()
        const parts = {
            yyyy: now.getFullYear(),
            MM: String(now.getMonth() + 1).padStart(2, '0'),
            dd: String(now.getDate()).padStart(2, '0'),
            hh: String(now.getHours()).padStart(2, '0'),
            mm: String(now.getMinutes()).padStart(2, '0'),
            ss: String(now.getSeconds()).padStart(2, '0')
        }

        return fmt.replace(/yyyy|MM|dd|hh|mm|ss/g, match => parts[match])
    },

    /**
     * 格式化日期
     * @param {Date} date - 要格式化的日期物件或可轉換為日期的輸入
     * @param {string} [fmt='yyyy-MM-dd'] - 格式化字符串，默認為 'yyyy-MM-dd'
     * @returns {string} 格式化後的日期字符串
     */
    formatDate(date, fmt = 'yyyy-MM-dd') {
        /* 日期格式化 yyyy - MM - dd hh: mm: ss*/
        try {
            if (this.isEmpty(date)) {
                return ''
            }
            date = new Date(date)
            var o = {
                'M+': date.getMonth() + 1, //月份
                'd+': date.getDate(), //日
                'h+': date.getHours(), //小时
                'm+': date.getMinutes(), //分
                's+': date.getSeconds(), //秒
                'q+': Math.floor((date.getMonth() + 3) / 3), //季度
                S: date.getMilliseconds() //毫秒
            }
            if (/(y+)/.test(fmt)) {
                fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
            }
            for (var k in o) {
                if (new RegExp('(' + k + ')').test(fmt)) {
                    fmt = fmt.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length))
                }
            }
            return fmt
        } catch (error: any) {
            console.error(`utils-formatDate error : ${date} ( ${error.message})`)
        }
    },

    /**
     * 將字串中的換行符替換為 <br> 標籤
     * @param {string} dateStr - 要處理的字串
     * @returns {string} 處理後的字串，若原字串為空則返回空字串
     */
    vbcrlf(dateStr) {
        if (this.isEmpty(dateStr) == false) {
            return dateStr.replace(/\n/g, '<br>')
        }
        return ''
    },

    /**
     * 將字串按換行符分割並轉換為 <li> 標籤列表
     * @param {string} dateStr - 要處理的字串
     * @returns {string} 處理後的 HTML 列表字符串，若原字串為空則返回空字串
     */
    li(dateStr) {
        if (this.isEmpty(dateStr) == false) {
            const str = dateStr
                .split('\n')
                .map(item => `<li>${item}</li>`)
                .join('')
            return str
        }
        return ''
    },

    /**
     * 格式化數字
     * @param {any} value - 要格式化的值，可以是數字或數字字符串
     * @param {number} [arg1] - 可選的最小和最大小數位數，默認為 0
     * @returns {string} 格式化後的數字字串，若輸入無效則返回原始值
     */
    formatNumber(value: any, arg1?: number) {
        // 检查 value 是否为 undefined 或 null
        if (this.isNumber(value) == false) {
            //console.error('Invalid input: value cannot be null or undefined:' + value);
            return value
        }

        // 默认配置
        const options = {
            minimumFractionDigits: arg1 != null ? arg1 : 0,
            maximumFractionDigits: arg1 != null ? arg1 : 0
        }

        try {
            return new Intl.NumberFormat('en-US', options).format(value)
        } catch (error) {
            console.error('Error formatting number:', error)
            return value // 返回原始值以保持一致性
        }
    },

    /**
     * 將項目添加到數據陣列的最前面
     * @param {Array} data - 要操作的數據陣列
     * @param {any} item - 要添加的項目，可以是任意類型
     */
    onAddTop(data, item) {
        data.unshift(JSON.parse(JSON.stringify(item)))
    },

    /**
     * 將項目添加到數據陣列的末尾
     * @param {Array} data - 要操作的數據陣列
     * @param {any} item - 要添加的項目，可以是任意類型
     */
    onAdd(data, item) {
        data.push(JSON.parse(JSON.stringify(item)))
    },

    /**
     * 刪除數據陣列中指定索引的項目
     * @param {Array} data - 要操作的數據陣列
     * @param {number} index - 要刪除的項目索引
     */
    onDel(data, index) {
        data.splice(index, 1)
    },

    /**
     * 刪除數據陣列中指定的項目
     * @param {Array} data - 要操作的數據陣列
     * @param {any} row - 要刪除的具體項目
     */
    onDelRow(data, row) {
        data.splice(
            data.findIndex(item => item === row),
            1
        )
    },

    /**
     * 移動數據陣列中的項目
     * @param {Array<any>} data - 要操作的數據陣列
     * @param {string} action - 移動的動作，可選值為 'up', 'down', 'top', 'bottom'
     * @param {number} key - 要移動的項目索引
     */
    onMoveRow(data: Array<any>, action: string, key: number) {
        // 确保数据有效
        if (!Array.isArray(data) || key < 0 || key >= data.length) {
            console.error('Invalid input: data must be a non-empty array and key must be valid')
            return
        }

        let targetIndex: number

        switch (action) {
            case 'up':
                targetIndex = key - 1
                if (targetIndex >= 0) {
                    // 交换位置
                    ;[data[targetIndex], data[key]] = [data[key], data[targetIndex]]
                }
                break

            case 'down':
                targetIndex = key + 1
                if (targetIndex < data.length) {
                    // 交换位置
                    ;[data[targetIndex], data[key]] = [data[key], data[targetIndex]]
                }
                break

            case 'top':
                if (key > 0) {
                    // 将当前项移到顶部
                    const temp = data[key]
                    data.splice(key, 1)
                    data.unshift(temp)
                }
                break

            case 'bottom':
                if (key < data.length - 1) {
                    // 将当前项移到底部
                    const temp = data[key]
                    data.splice(key, 1)
                    data.push(temp)
                }
                break

            default:
                console.warn('Unknown action:', action)
                break
        }
    },

    /**
     * 導向指定的 URL
     * @param {string} url - 要導向的目標 URL
     */
    location(url: string) {
        window.location.href = url
    },

    /**
     * 獲取當前 URL 的查詢參數
     * @param {string} key - 要獲取的查詢參數鍵名
     * @returns {string|null} 查詢參數的值，如果不存在則返回 null
     */
    getUrlQuery(key: string) {
        const queryString = window.location.search
        const urlParams = new URLSearchParams(queryString)
        let value = urlParams.get(key)
        return value
    },

    /**
     * 根據 XPath 獲取設置中的項目標籤
     * @param {string} xpath - 用於查詢的路徑
     * @param {string} value - 要匹配的值
     * @returns {any} 如果找到則返回對應標籤，否則返回原值
     */
    getXmlSearch(xpath: string, value: string) {
        try {
            if (utils.isEmpty(value)) {
                return
            }
            const store = useDataStore()

            const kindData = store.setup[xpath]?.['KIND']

            if (!Array.isArray(kindData)) {
                if (typeof kindData == 'object') {
                    return kindData['傳回值'] == value ? kindData['資料'] : value
                }
                console.error(`Invalid input: setup[${xpath}] is not an array or does not exist`)
                return value // 返回原始值以保持行为一致
            }
            const item = kindData.find(element => String(element['傳回值']) == String(value))
            return item ? item['資料'] : value // 如果找到，返回label，否则返回原始值
        } catch (error: any) {
            console.error(`getXmlSearch:${error.message}`)
        }
    },

    /**
     * 根據 XPath 獲取設置中的完整項目
     * @param {string} xpath - 用於查詢的路徑
     * @param {string} value - 要匹配的值
     * @returns {any} 如果找到則返回完整項目，否則返回原值
     */
    getXmlSearchRow(xpath: string, value: string) {
        try {
            if (utils.isEmpty(value)) {
                return
            }
            const store = useDataStore()

            const kindData = store.setup[xpath]?.['KIND']

            if (!Array.isArray(kindData)) {
                if (typeof kindData == 'object') {
                    return kindData['傳回值'] == value ? kindData['資料'] : value
                }
                console.error(`Invalid input: setup[${xpath}] is not an array or does not exist`)
                return value // 返回原始值以保持行为一致
            }
            const item = kindData.find(element => String(element['傳回值']) == String(value))
            return item ? item : value // 如果找到，返回label，否则返回原始值
        } catch (error: any) {
            console.error(`getXmlSearchRow:${error.message}`)
        }
    },

    /**
     * 計算資料陣列中指定字段的總和
     * @param {Array<any>} datas - 要計算的資料陣列
     * @param {string} field - 要計算總和的字段名稱
     * @returns {number} 返回該字段的總和
     */
    total(datas: Array<any>, field: string): number {
        if (!Array.isArray(datas) || datas.length === 0) {
            console.error('Invalid input: datas must be a non-empty array')
            return 0
        }

        const fieldValue = datas[0][field]

        if (typeof fieldValue === 'undefined') {
            console.error(`Field "${field}" does not exist in the data array`)
            return 0
        }

        return datas.reduce((total, item) => total + (item[field] || 0), 0)
    },

    /**
     * 獲取假資料並填充到指定物件
     * @param {string} file - 假資料檔案的名稱
     * @param {Object} [inputs={}] - 要填充假資料的目標物件，默認為空物件
     */
    getFakeData(file: string, inputs = {}) {
        const modules = import.meta.glob('~/fakes/*.js')
        const path = `/fakes/${file}.js`
        if (modules[path]) {
            modules[path]()
                .then(module => {
                    Object.keys(inputs).forEach(key => {
                        if (this.isEmpty(inputs[key])) {
                            inputs[key] = module.data._rawValue[key]
                        }
                    })
                    //console.clear();
                    console.log(['fake data', inputs])
                })
                .catch(error => {
                    console.error(`Failed to load module at ${path}`, error)
                })
        } else {
            console.error(`Module ${path} does not exist`)
        }
    },

    /**
     * 處理表單錯誤並顯示消息
     * @param {Object} error - 包含錯誤信息的物件
     */
    formElError(error: any) {
        const errorMessages = Object.values(error).flatMap(errors => errors.map(err => err.message))

        const errorMessage = errorMessages.map(msg => `<p>${msg}</p>`).join('')

        if (errorMessage != '') {
            this.message(errorMessage, 'error')
        } else {
            this.message(error.message, 'error')
        }
    },

    /**
     * 生成唯一的 UUID
     * @returns {string} 返回生成的 UUID 字符串
     */
    generateUUID() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
            const r = (Math.random() * 16) | 0,
                v = c === 'x' ? r : (r & 0x3) | 0x8
            return v.toString(16)
        })
    },

    /**
     * 獲取唯一標識符 (UUID)
     * @returns {string} 返回現有或新生成的 UUID
     */
    getUuid() {
        const store = useDataStore()
        if (!utils.isEmpty(store.data?.uuid)) {
            return store.data?.uuid
        }
        const uuid = this.generateUUID()
        store.data.uuid = uuid
        return uuid
    },

    /**
     * 處理文件選擇並更新輸入數據
     * @param {Event} event - 文件選擇事件對象
     * @param {Object} inputs - 要更新的輸入數據物件
     */
    selectFile: function (event, inputs) {
        const name = event.currentTarget.name
        inputs[name] = event.target.files[0].name
        //console.log(vm.inputs[name+'_filename']);
        var reader = new FileReader()
        reader.readAsDataURL(event.target.files[0])
        reader.onload = () => {
            inputs[name] = reader.result
            //console.log(vm.inputs[name]);
        }
    },

    /**
     * 獲取電子郵件內容
     * @param {string|number} id - 要查詢的電子郵件 ID
     * @returns {Promise<any>} 返回電子郵件內容或 undefined
     */
    getEpost: async id => {
        try {
            const http = createHttp()
            let rep = await http.post('api/epost/show', { id: id })

            if (rep.resultcode == '0') {
                return rep.data.epostbody
            }
        } catch (error: any) {
            //utils.formElError(error)
            console.error(error)
        }
    },

    /**
     * 移除 HTML 標籤，返回純文本
     * @param {string} htmlString - 包含 HTML 標籤的字串
     * @returns {string} 返回移除 HTML 標籤後的純文本，若輸入為空則返回空字串
     */
    noHtml(htmlString: string) {
        if (!this.isEmpty(htmlString)) {
            return htmlString.replace(/<[^>]*>/g, '')
        } else {
            return ''
        }
    },

    /**
     * 根據鍵名獲取對應的組件
     * @param {string} key - 組件名稱或鍵名
     * @returns {any} 返回組件定義或原始鍵名
     */
    getComponent(key: string) {
        if (key.substring(0, 3) != 'el-') {
            // const componentName = toPascalCase(key)
            // console.log(componentName) // 检查转换后的名称
            // return resolveComponent(componentName)
            return defineAsyncComponent(() => import(`~/components/${key}.vue`))
        } else {
            return key
        }
    },

    /**
     * 將指定值複製到剪貼板
     * @param {string} value - 要複製的文本值
     */
    async copy(value: string) {
        if (!navigator.clipboard) {
            this.alert('您的瀏覽器不支援複製功能，請手動複製。', '', 'error')
            return
        }

        try {
            // Accessing inputElement's value property (assuming it's correctly referenced)
            await navigator.clipboard.writeText(value)
            this.toast('複製成功', 'success', 1000)
        } catch (err) {
            this.alert('複製失敗，請重試。', '', 'error')
        }
    },
    // Unicode 解碼函數
    unicodeToChar(text: string) {
        return text.replace(/\\u[\dA-F]{4}/gi, match => String.fromCharCode(parseInt(match.replace(/\\u/g, ''), 16)))
    }
}
