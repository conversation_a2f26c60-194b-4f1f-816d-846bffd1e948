<template>
    <ClientOnly fallback-tag="span" fallback="Loading">
        <el-cascader v-model="localModelValue" :options="localOptions" style="width: 100%" />
        <el-icon
            v-if="showReconnectIcon"
            class="reconnect-icon"
            @click="fetchOptions"
            style="display: inline; width: 100%; text-align: left"
        >
            <span class="text-danger"><Refresh /> 連線失敗，點擊重新連線</span>
        </el-icon>
        <slot></slot>
    </ClientOnly>
</template>

<script setup lang="ts">
import { computed, defineEmits, defineProps, onMounted, ref } from 'vue'

import { createHttp } from '@/utils/http'
const http = createHttp()

const name1 = defineModel('name1', { required: true, type: [String, Number], default: '' })
const name2 = defineModel('name2', { required: true, type: [String, Number], default: '' })
const emits = defineEmits(['update:modelValue', 'change'])
const props = defineProps({
    api: {
        type: String,
        required: true
    },
    modelValue: {
        default: ''
    }
})
const showReconnectIcon = ref(false)
//let localModelValue=ref("");//要加.value
let localOptions = ref([]) //要加.value
// const handleChange = value => {
//     name1.value = value[0]
//     name2.value = value[1]
//     console.log(value)
// }
const localModelValue = computed({
    get() {
        return [name1.value, name2.value]
    },
    set(val: string) {
        name1.value = val[0]
        name2.value = val[1]
        // //console.clear();
        // console.log(['val', val])
        // emits('update:modelValue', val)
    }
})

const fetchOptions = async () => {
    try {
        showReconnectIcon.value = false
        let rep = await http.post(props.api, {}, false)
        if (rep.resultcode == '0') {
            localOptions.value = rep.data
        } else {
            throw new Error(rep.resultmessage)
        }

        //localDefaultText.value = 'Select'
    } catch (error) {
        showReconnectIcon.value = true
        console.error('Error fetching options:', error)
    }
}

onMounted(async () => {
    fetchOptions()
})

const errorCaptured = async (err, vm, info) => {
    console.error(
        `my-apicascader Error: ${vm.$options.name};message: ${err.toString()};info: ${info}`
    )
    return false // 可以返回 true 或 false 來決定是否繼續往上傳遞錯誤
}
/*
 <my-apicascader
        api="/api/dependentdropdown/citys"
        v-model:name1="inputs.city1"
        v-model:name2="inputs.city2"
    >
    </my-apicascader>
*/
</script>

<style scoped></style>
