﻿//onclick="PF_Hits('board','id','{{$rs->id}}');"
function PF_Hits(dbtable, field, key) {
	jQuery.ajax({
		type: 'post',
		data: 'dbtable=' + dbtable + '&field=' + field + '&key=' + key,
		url: FC_WebFolder + 'api/db/sethits',
		dataType: 'html'
	});
}

function PF_escape(S) {
	return encodeURIComponent(S);
}
//PF_getdata('xx.php','yy','kind=1');
//<div id="yy" style="height:550px;overflow:hidden"></div>
function PF_getdata(requestURL, objname, pars) {
	if (typeof objname == 'object') {
		$(objname).html('Loading content, please wait..');
	} else {
		$('#' + objname).html('Loading content, please wait..');
	}
	if (typeof pars == 'object') {
		pars = $(pars).serialize();
	}
	jQuery.ajax({
		type: 'post',
		data: pars,
		url: requestURL,
		dataType: 'html',
		error: function (resp) {
			//	alert('Error: url : ' + requestURL+'?'+pars+'\n'+resp.responseText);
		},
		beforeSend: function () {
			//jQuery("#"+objname).html('Loading content, please wait..');
		},
		success: function (resp) {
			if (typeof objname == 'object') {
				$(objname).html(resp);
			} else {
				$('#' + objname).html(resp);
			}
		}
	});
}
function PF_AjaxblockUI(requestURL, pars, title) {
	if (requestURL == '') {
		return;
	}
	jQuery.ajax({
		type: 'post',
		url: requestURL,
		dataType: 'json',
		data: pars,
		error: function (resp) {
			alert('Error: url : ' + requestURL + '?' + pars + '\n' + resp);
		},
		beforeSend: function () {
			//jQuery("#loading").toggle();
		},
		error: function (XMLHttpRequest, textStatus) { },
		success: function (resp) {
			try {
				if (resp.resultcode == 0) {
					Swal.fire({
						position: 'top-end',
						icon: 'success',
						title: resp.resultmessage,
						//text: title,
						showConfirmButton: false,
						timer: 1500
					});
					//$.growlUI(title,resp.resultmessage);
				} else {
					_alert(resp.resultmessage);
				}
			} catch (error) {
				console.log(error);
				_alert(error);
			}
		}
	});
}
//PF_sendtoline(document.title,location.href);
function PF_sendtoline(title, url) {
	window.open(
		'https://social-plugins.line.me/lineit/share?url=' +
		encodeURIComponent(url) +
		'&text=' +
		encodeURIComponent(title) +
		'&from=line_scheme'
	);
	//location.href = link;
	return false;
}
//PF_sendtofacebook(location.href);
function PF_sendtofacebook(url) {
	var link = 'http://www.facebook.com/share.php?u=' + encodeURIComponent(url);
	window.open(link);
	//location.href = link;
	return false;
}
