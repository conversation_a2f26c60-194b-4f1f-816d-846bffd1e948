<?php

namespace App\Http\Controllers\api;

use App\Http\Controllers\Controller;
use App\Models\member;
use App\Traits\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

/**
 * 會員 API 控制器
 * 處理會員註冊、登入、個人資料管理等功能
 */
class memberController extends Controller {
    use ApiResponse;

    /**
     * @OA\Post(
     *     path="/api/member/register",
     *     summary="會員註冊",
     *     tags={"會員管理"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             @OA\Property(property="email", type="string", description="電子信箱", example="<EMAIL>"),
     *             @OA\Property(property="password", type="string", description="密碼", example="password123"),
     *             @OA\Property(property="password_confirmation", type="string", description="確認密碼", example="password123"),
     *             @OA\Property(property="name", type="string", description="姓名", example="王小明"),
     *             @OA\Property(property="phone", type="string", description="手機號碼", example="0912345678"),
     *             @OA\Property(property="birth", type="string", description="生日", example="1990-01-01"),
     *             @OA\Property(property="gender", type="string", description="性別", example="先生")
     *         )
     *     ),
     *     @OA\Response(response=200, description="註冊成功"),
     *     @OA\Response(response=422, description="資料驗證錯誤")
     * )
     */
    public function register(Request $request) {
        // 驗證輸入資料
        $validator = Validator::make($request->all(), [
            'email' => 'required|email|unique:member,email',
            'password' => 'required|min:6|confirmed',
            'name' => 'required|string|max:50',
            'phone' => 'nullable|string|max:20',
            'birth' => 'nullable|date',
            'gender' => 'nullable|string|max:10',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse($validator->errors(), '資料驗證錯誤', 422);
        }

        try {
            // 建立新會員
            $member = member::create([
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'name' => $request->name,
                'phone' => $request->phone,
                'birth' => $request->birth,
                'gender' => $request->gender,
                'member_type' => 1, // 預設為一般會員
                'status' => 1, // 預設為正常狀態
            ]);

            // 建立 API Token
            $token = $member->createToken('member-token')->plainTextToken;

            return $this->successResponse([
                'member' => $member,
                'token' => $token,
            ], '註冊成功');
        } catch (\Exception $e) {
            return $this->errorResponse([], '註冊失敗：' . $e->getMessage(), 500);
        }
    }

    /**
     * @OA\Post(
     *     path="/api/member/login",
     *     summary="會員登入",
     *     tags={"會員管理"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             @OA\Property(property="email", type="string", description="電子信箱", example="<EMAIL>"),
     *             @OA\Property(property="password", type="string", description="密碼", example="password123")
     *         )
     *     ),
     *     @OA\Response(response=200, description="登入成功"),
     *     @OA\Response(response=401, description="登入失敗")
     * )
     */
    public function login(Request $request) {
        // 驗證輸入資料
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse($validator->errors(), '資料驗證錯誤', 422);
        }

        try {
            // 查找會員
            $member = member::where('email', $request->email)->first();

            if (!$member || !Hash::check($request->password, $member->password)) {
                return $this->errorResponse([], '電子信箱或密碼錯誤', 401);
            }

            // 檢查會員狀態
            if ($member->status != 1) {
                return $this->errorResponse([], '帳號已被停權', 401);
            }

            // 建立 API Token
            $token = $member->createToken('member-token')->plainTextToken;

            return $this->successResponse([
                'member' => $member,
                'token' => $token,
            ], '登入成功');
        } catch (\Exception $e) {
            return $this->errorResponse([], '登入失敗：' . $e->getMessage(), 500);
        }
    }

    /**
     * @OA\Post(
     *     path="/api/member/profile",
     *     summary="取得個人資料",
     *     tags={"會員管理"},
     *     security={{"sanctum": {}}},
     *     @OA\Response(response=200, description="取得成功"),
     *     @OA\Response(response=401, description="未授權")
     * )
     */
    public function profile(Request $request) {
        try {
            $member = $request->user();

            return $this->successResponse([
                'member' => $member->load(['coach', 'bookings', 'reviews']),
            ], '取得個人資料成功');
        } catch (\Exception $e) {
            return $this->errorResponse([], '取得個人資料失敗：' . $e->getMessage(), 500);
        }
    }

    /**
     * @OA\Post(
     *     path="/api/member/update-profile",
     *     summary="更新個人資料",
     *     tags={"會員管理"},
     *     security={{"sanctum": {}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             @OA\Property(property="name", type="string", description="姓名", example="王小明"),
     *             @OA\Property(property="phone", type="string", description="手機號碼", example="0912345678"),
     *             @OA\Property(property="birth", type="string", description="生日", example="1990-01-01"),
     *             @OA\Property(property="gender", type="string", description="性別", example="先生"),
     *             @OA\Property(property="bio", type="string", description="個人簡介", example="熱愛滑雪的初學者")
     *         )
     *     ),
     *     @OA\Response(response=200, description="更新成功"),
     *     @OA\Response(response=422, description="資料驗證錯誤")
     * )
     */
    public function updateProfile(Request $request) {
        // 驗證輸入資料
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:50',
            'phone' => 'nullable|string|max:20',
            'birth' => 'nullable|date',
            'gender' => 'nullable|string|max:10',
            'bio' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse($validator->errors(), '資料驗證錯誤', 422);
        }

        try {
            $member = $request->user();

            $member->update($request->only([
                'name',
                'phone',
                'birth',
                'gender',
                'bio'
            ]));

            return $this->successResponse([
                'member' => $member->fresh(),
            ], '個人資料更新成功');
        } catch (\Exception $e) {
            return $this->errorResponse([], '更新失敗：' . $e->getMessage(), 500);
        }
    }

    /**
     * @OA\Post(
     *     path="/api/member/logout",
     *     summary="會員登出",
     *     tags={"會員管理"},
     *     security={{"sanctum": {}}},
     *     @OA\Response(response=200, description="登出成功")
     * )
     */
    public function logout(Request $request) {
        try {
            // 刪除當前的 token
            $request->user()->currentAccessToken()->delete();

            return $this->successResponse([], '登出成功');
        } catch (\Exception $e) {
            return $this->errorResponse([], '登出失敗：' . $e->getMessage(), 500);
        }
    }
}
