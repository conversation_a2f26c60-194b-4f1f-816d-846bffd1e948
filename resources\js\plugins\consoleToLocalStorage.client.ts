import { defineNuxtPlugin } from '#app'
import { useLocalStorage } from '@vueuse/core'

export default defineNuxtPlugin(nuxtApp => {
    if (process.env.NODE_ENV != 'development') {
        // 使用 useLocalStorage 存儲日誌，確保初始值為空陣列，並加上監聽器
        const logs = useLocalStorage('logs', [], {
            deep: true
            // 可以在這裡加上一個監聽器，雖然在這個例子中可能不需要，但如果需要在 logs 變動時做額外處理，這會很有用
            // watch: (newLogs, oldLogs) => {
            //   console.log('Logs changed:', newLogs, oldLogs);
            // }
        })

        // 定義要覆蓋的 console 方法
        const consoleMethods = ['log', 'error'] // 移除不必要的註解

        // 獲取當前日期（只包含年月日）
        const getTodayDate = () => new Date().toISOString().split('T')[0]

        // 初次載入時過濾掉今天之前的日誌
        const initializeLogs = () => {
            if (!Array.isArray(logs.value)) {
                logs.value = []
                return
            }
            const today = getTodayDate()
            logs.value = logs.value.filter(log => log.timestamp?.startsWith(today))
        }

        // 格式化時間戳為 YYYY-MM-DD HH:MM:SS 格式
        const formatTimestamp = (timestamp: number) => {
            const date = new Date(timestamp)
            const year = date.getFullYear()
            const month = (date.getMonth() + 1).toString().padStart(2, '0')
            const day = date.getDate().toString().padStart(2, '0')
            const hours = date.getHours().toString().padStart(2, '0')
            const minutes = date.getMinutes().toString().padStart(2, '0')
            const seconds = date.getSeconds().toString().padStart(2, '0')
            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
        }

        // 格式化消息，包含 URL 和參數
        const formatMessage = (args: any[]) => {
            // 使用 any[] 類型
            return args.map(arg => (typeof arg === 'object' && arg !== null ? JSON.stringify(arg) : String(arg))).join(' ')
        }

        // 在插件初始化時執行一次過濾
        initializeLogs()

        // 遍歷並覆蓋所有 console 方法
        consoleMethods.forEach(method => {
            const originalMethod = console[method]
            console[method] = (...args: any[]) => {
                // 使用 any[]
                // 格式化消息，處理對象
                const message = formatMessage(args)
                if (message && message !== '{}') {
                    // 更簡潔的判斷
                    // 將方法名稱和參數記錄到 logs 中
                    logs.value.push({
                        method,
                        url: window.location.href,
                        message,
                        uuid: utils.getUuid(), // 這裡的 utils 看起來是外部注入的，Typescript 裡如果沒有 import 會報錯，我先註解掉
                        timestamp: formatTimestamp(Date.now()) // 使用 Date.now()
                    })
                }
                originalMethod.apply(console, args)
            }
        })

        // 提供 logs 給應用程序使用
        nuxtApp.provide('consoleLogs', logs)
    }
})
