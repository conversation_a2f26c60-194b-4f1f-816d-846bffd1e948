<script type="application/ld+json">
      {
        "@context": "https://schema.org/",
        "@type": "Product",
        "name": "{{ $rs->title }} - {{PF::getConfig('name')}}",
        @if ($rs->image != null)
        "image": [
          "{{ url('/') }}/product/{{ end(explode(",",$image))}}"
        ],
        @endif
        "description": "{{ PF::noHtml($rs->description) }}",
        "sku": "",
        "mpn": "",
        "brand": {
          "@type": "Brand",
          "name": "{{PF::getConfig('name')}}"
        },
        "review": {
          "@type": "Review",
          "reviewRating": {
            "@type": "Rating",
            "ratingValue": 4,
            "bestRating": 5
          },
          "author": {
            "@type": "Person",
            "name": "{{PF::getConfig('name')}}"
          }
        },
        @if ($rs->price!="")
        "offers": {
            "@type": "Offer",
            "price": {{ $rs->price }},
            "priceCurrency": "TWD"
        },
        @endif
        "aggregateRating": {
          "@type": "AggregateRating",
          "ratingValue": 4.4,
          "reviewCount": {{$rs->hits!=''? $rs->hits :0 }}
        }

      }
</script>
