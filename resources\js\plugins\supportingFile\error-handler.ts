// plugins/error-handler.ts
export default defineNuxtPlugin(nuxtApp => {
    // const formatError = (error: Error) => {
    //     // 取得錯誤堆疊
    //     const stack = error.stack || ''
    //     // 解析錯誤堆疊以獲取檔案位置和行號
    //     const stackLines = stack.split('\n')
    //     const fileInfo =
    //         stackLines
    //             .find(line => line.includes('.vue') || line.includes('.ts') || line.includes('.js'))
    //             ?.trim() || ''
    //     // 提取檔案路徑和行號
    //     const fileMatch =
    //         fileInfo.match(/\((.*?):(\d+):(\d+)\)/) || fileInfo.match(/at\s+(.*?):(\d+):(\d+)/)
    //     const errorInfo = {
    //         message: error.message,
    //         name: error.name,
    //         fileName: fileMatch ? fileMatch[1] : '未知檔案',
    //         lineNumber: fileMatch ? fileMatch[2] : '未知行號',
    //         columnNumber: fileMatch ? fileMatch[3] : '未知列號',
    //         stack: stack,
    //         // 如果是 TypeError，可能會有額外資訊
    //         cause: error.cause
    //     }
    //     return errorInfo
    // }
    // // Vue 錯誤處理器
    // nuxtApp.vueApp.config.errorHandler = (error, instance, info) => {
    //     const errorInfo = formatError(error as Error)
    //     console.error('Vue 錯誤詳情:', {
    //         ...errorInfo,
    //         componentInfo: {
    //             name: instance?.$options?.name || '未知組件',
    //             props: instance?.$props,
    //             route: window?.location?.href
    //         },
    //         vueInfo: info
    //     })
    //     // 這裡可以加入錯誤回報服務，例如 Sentry
    //     // reportToErrorService(errorInfo);
    // }
    // // Nuxt 錯誤鉤子
    // nuxtApp.hook('vue:error', (error, instance, info) => {
    //     const errorInfo = formatError(error as Error)
    //     console.error('Nuxt 錯誤詳情:', {
    //         ...errorInfo,
    //         componentInfo: {
    //             name: instance?.$options?.name || '未知組件',
    //             props: instance?.$props,
    //             route: window?.location?.href
    //         },
    //         vueInfo: info
    //     })
    //     // 這裡可以加入錯誤回報服務，例如 Sentry
    //     // reportToErrorService(errorInfo);
    // })
    // // 全域未捕獲的 Promise 錯誤
    // if (process.client) {
    //     window.addEventListener('unhandledrejection', event => {
    //         const errorInfo = formatError(event.reason)
    //         console.error('未處理的 Promise 錯誤:', errorInfo)
    //     })
    //     // 全域未捕獲的一般錯誤
    //     window.addEventListener('error', event => {
    //         const errorInfo = formatError(event.error)
    //         console.error('全域未捕獲錯誤:', errorInfo)
    //     })
    // }
})
