<template>
    <my-breadcrumb id="adminuser" refs="breadcrumb"></my-breadcrumb>
    <div v-loading="http.getLoading()">
        <my-search
            :searchNames="{
                '': '全部',
                'adminuser.account': '帳號',
                'adminuser.name': '姓名',
                'adminuser.email': 'EMAIL',
                'adminuser.role|INT': '角色',
                'adminuser.online|INT': '是否核可'
            }"
            :searchDateNames="{
                'adminuser.lastlogin_dt': '最後登入時間',
                'adminuser.created_at': '建立日期'
            }"
            @onSearch="onSearch"
        ></my-search>
        <div class="d-flex justify-content-between">
            <div v-if="[999].includes(store.adminuser?.role)">
                <my-buttondel @click="onDel"></my-buttondel>
            </div>
            <div></div>
            <div>
                <my-buttonadd @click="myDialog.open('admin/adminuser/edit', { ...props }, 0)"></my-buttonadd>
            </div>
        </div>
        <div class="table-responsive-md">
            <table class="table table-striped table-hover table-bordered table-fixed">
                <thead>
                    <tr>
                        <th align="center" width="90">
                            <el-checkbox @change="handleCheckAllChange" v-show="[999].includes(store.adminuser?.role)"></el-checkbox>
                        </th>
                        <th width="" @click="onSort('account')" class="sortable">帳號</th>
                        <th width="" @click="onSort('name')" class="sortable">姓名</th>

                        <th width="" @click="onSort('email')" class="sortable">EMAIL</th>
                        <th width="" @click="onSort('role')" class="sortable">角色</th>

                        <th width="" @click="onSort('lastlogin_dt')" class="sortable">最後登入時間</th>

                        <th width="" @click="onSort('online')" class="sortable">是否核可</th>
                        <th width="" @click="onSort('failcount')" class="sortable">登入錯誤次數</th>
                        <th width="" @click="onSort('created_at')" class="sortable">建立日期</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="(rs, index) in data.data">
                        <td valign="top" title="Edit" align="center">
                            <el-checkbox v-model="rs.del" :true-value="rs.id" v-if="[999].includes(store.adminuser?.role)"></el-checkbox>
                            &nbsp;
                            <a
                                :href="`/admin/adminuser/edit?edit=${rs.id}`"
                                class="btn btn-success btn-sm"
                                @click.prevent="e => myDialog.open(e.currentTarget.href, { ...props }, { title: '管理人員' })"
                                ><i class="fa fa-edit" aria-hidden="true"></i
                            ></a>
                        </td>

                        <td>
                            {{ rs.account }}
                            <!--帳號-->
                        </td>
                        <td>
                            {{ rs.name }}
                            <!--姓名-->
                        </td>

                        <td>
                            <a :href="'mailto:' + rs.email">{{ rs.email }}</a>
                            <!--EMAIL-->
                        </td>
                        <td>
                            {{ utils.getXmlSearch('角色', rs.role) }}
                            <!--角色-->
                        </td>

                        <td>
                            {{ utils.formatDate(rs.lastlogin_dt) }}
                            <!--最後登入時間-->
                        </td>

                        <td>
                            {{ utils.getXmlSearch('是否核可', rs.online) }}
                            <!--是否核可-->
                        </td>
                        <td>
                            {{ rs.failcount }}
                            <!--登入錯誤次數-->
                        </td>
                        <td>
                            {{ utils.formatDate(rs.created_at) }}
                            <!--建立日期-->
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <my-pagination :total.number="data.total" v-model:page="inputs.page" @current-change="onPageChange"></my-pagination>
        <my-dialog ref="myDialog" @closed-dialog="closedDialog"> </my-dialog>
    </div>
</template>

<script setup lang="ts">
import MyDialog from '@/components/my-dialog.vue' //modal套件
import { onMounted, reactive, ref } from 'vue'
const http = createHttp()
const router = useRouter()
const store = useDataStore()
const myDialog = ref<InstanceType<typeof MyDialog>>()
defineEmits(['closedDialog'])

//const emits = defineEmits(["close"]); //接受外面送來的觸發的參數
const props = defineProps({
    // bxx_id: {
    //     default: "",
    //     type: Number,
    //     required: true,
    // },
}) //接受外面送來的參數

// 使用 reactive 定義對象
const data = reactive({
    data: [] as adminuser[]
})
const edit = ref('')

const inputs = reactive({
    search: '',
    searchname: '',
    searchdatename: '',
    page: 1,
    sortname: '',
    sorttype: '',
    del: []
})
//分頁事件
const onPageChange = async page => {
    inputs.page = page
    await getData()
}
//排序事件
const onSort = async column => {
    inputs.page = 1
    inputs.sortname = column
    inputs.sorttype = inputs.sorttype == 'asc' ? 'desc' : 'asc'
    await getData()
}

//搜尋事件
const onSearch = async searchdatas => {
    inputs.page = 1
    inputs.sortname = ''
    Object.assign(inputs, searchdatas)
    await getData()
}

//接收到modal關閉視窗事件
const closedDialog = async (isReload = false) => {
    if (isReload) {
        await getData()
    }
}

//del全選事件
const handleCheckAllChange = async value => {
    data.data.forEach(item => {
        item.del = value ? item.id : 0
    })
}

//刪除事件
const onDel = async () => {
    try {
        // 提取被选中的删除项的 id
        inputs.del = data.data
            .filter(item => item.del !== 0)
            .map(item => item.del)
            .filter(value => value !== null)

        if (inputs.del.length === 0) {
            throw new Error('Please select an item to delete.')
        }

        let rep = await http.post('api/admin/adminuser/destroy', inputs)
        if (rep.resultcode == '0') {
            utils.toast(rep.resultmessage)
            await getData()
        } else {
            throw new Error(rep.resultmessage)
        }
    } catch (error: any) {
        utils.formElError(error)
        console.error(error)
    }
}

//下載資料
const getData = async () => {
    try {
        let rep = (await http.post('api/admin/adminuser', inputs)) as ApiResponse
        //debugger;
        if (rep.resultcode == '0') {
            Object.assign(data, rep.data)
        } else {
            throw new Error(rep.resultmessage)
        }
    } catch (error: any) {
        utils.message(error.message)
        console.error(error)
    }
}
onMounted(async () => {
    await getData()
})
</script>
<style scoped></style>
