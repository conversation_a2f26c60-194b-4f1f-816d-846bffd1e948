[2025-06-30 20:18:49] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:38.34]
  
[2025-06-30 20:18:49] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:2.09]
  
[2025-06-30 20:18:49] dev.INFO: 
select * from `coach_certifications` limit 1
  [執行超過10秒:1.85]
  
[2025-06-30 20:18:49] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:1.72]
  
[2025-06-30 20:18:49] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:2.06]
  
[2025-06-30 20:18:49] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:1.74]
  
[2025-06-30 20:18:49] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:1.75]
  
[2025-06-30 20:18:49] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:1.64]
  
[2025-06-30 20:18:49] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:1.41]
  
[2025-06-30 20:18:49] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:1.67]
  
[2025-06-30 20:18:49] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:1.33]
  
[2025-06-30 20:18:49] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:1.8]
  
[2025-06-30 20:18:49] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:1.35]
  
[2025-06-30 20:18:49] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:1.58]
  
[2025-06-30 20:18:49] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:1.68]
  
[2025-06-30 20:18:49] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:1.88]
  
[2025-06-30 20:18:49] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:2.15]
  
[2025-06-30 20:18:49] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:2.03]
  
[2025-06-30 20:48:33] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:25.47]
  
[2025-06-30 20:48:33] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:0.88]
  
[2025-06-30 20:48:33] dev.INFO: 
select * from `coach_certifications` limit 1
  [執行超過10秒:0.73]
  
[2025-06-30 20:48:33] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:0.83]
  
[2025-06-30 20:48:33] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:0.7]
  
[2025-06-30 20:48:33] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:1.06]
  
[2025-06-30 20:48:33] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.8]
  
[2025-06-30 20:48:33] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:1.16]
  
[2025-06-30 20:48:33] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:0.71]
  
[2025-06-30 20:48:33] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:0.78]
  
[2025-06-30 20:48:33] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:0.5]
  
[2025-06-30 20:48:33] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:0.64]
  
[2025-06-30 20:48:33] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.54]
  
[2025-06-30 20:48:33] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:0.62]
  
[2025-06-30 20:48:33] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:0.89]
  
[2025-06-30 20:48:33] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:0.61]
  
[2025-06-30 20:48:33] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.75]
  
[2025-06-30 20:48:33] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:0.53]
  
[2025-06-30 20:48:50] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:8.16]
  
[2025-06-30 20:48:50] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:0.8]
  
[2025-06-30 20:48:50] dev.INFO: 
select * from `coach_certifications` limit 1
  [執行超過10秒:0.85]
  
[2025-06-30 20:48:50] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:1]
  
[2025-06-30 20:48:50] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:0.77]
  
[2025-06-30 20:48:50] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:0.71]
  
[2025-06-30 20:48:50] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.92]
  
[2025-06-30 20:48:50] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.57]
  
[2025-06-30 20:48:50] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:0.7]
  
[2025-06-30 20:48:51] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:0.6]
  
[2025-06-30 20:48:51] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:0.6]
  
[2025-06-30 20:48:51] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:0.88]
  
[2025-06-30 20:48:51] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.72]
  
[2025-06-30 20:48:51] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:0.85]
  
[2025-06-30 20:48:51] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:0.8]
  
[2025-06-30 20:48:51] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:0.71]
  
[2025-06-30 20:48:51] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.68]
  
[2025-06-30 20:48:51] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:0.82]
  
[2025-06-30 20:49:09] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:17.65]
  
[2025-06-30 20:49:09] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:0.99]
  
[2025-06-30 20:49:09] dev.INFO: 
select * from `coach_certifications` limit 1
  [執行超過10秒:0.94]
  
[2025-06-30 20:49:09] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:1.83]
  
[2025-06-30 20:49:09] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:1.5]
  
[2025-06-30 20:49:09] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:1.87]
  
[2025-06-30 20:49:09] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:1.76]
  
[2025-06-30 20:49:09] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:1.47]
  
[2025-06-30 20:49:09] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:1.21]
  
[2025-06-30 20:49:09] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:1.4]
  
[2025-06-30 20:49:09] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:1.2]
  
[2025-06-30 20:49:09] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:0.89]
  
[2025-06-30 20:49:10] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:1.72]
  
[2025-06-30 20:49:10] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:1.47]
  
[2025-06-30 20:49:10] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:1.69]
  
[2025-06-30 20:49:10] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:1.75]
  
[2025-06-30 20:49:10] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:2.13]
  
[2025-06-30 20:49:10] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:2.33]
  
[2025-06-30 20:49:26] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:16.74]
  
[2025-06-30 20:49:26] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:1.05]
  
[2025-06-30 20:49:26] dev.INFO: 
select * from `coach_certifications` limit 1
  [執行超過10秒:0.72]
  
[2025-06-30 20:49:26] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:0.85]
  
[2025-06-30 20:49:26] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:1.26]
  
[2025-06-30 20:49:26] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:0.6]
  
[2025-06-30 20:49:26] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.55]
  
[2025-06-30 20:49:26] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.73]
  
[2025-06-30 20:49:26] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:1.23]
  
[2025-06-30 20:49:26] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:0.62]
  
[2025-06-30 20:49:26] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:1.02]
  
[2025-06-30 20:49:26] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:1.15]
  
[2025-06-30 20:49:26] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:1.13]
  
[2025-06-30 20:49:26] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:0.85]
  
[2025-06-30 20:49:26] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:1.04]
  
[2025-06-30 20:49:26] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:1.18]
  
[2025-06-30 20:49:26] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:1.09]
  
[2025-06-30 20:49:26] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:0.88]
  
[2025-06-30 20:49:47] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:22.01]
  
[2025-06-30 20:49:47] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:0.79]
  
[2025-06-30 20:49:47] dev.INFO: 
select * from `coach_certifications` limit 1
  [執行超過10秒:0.59]
  
[2025-06-30 20:49:47] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:0.83]
  
[2025-06-30 20:49:47] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:0.69]
  
[2025-06-30 20:49:47] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:0.79]
  
[2025-06-30 20:49:47] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.83]
  
[2025-06-30 20:49:47] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.76]
  
[2025-06-30 20:49:47] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:0.74]
  
[2025-06-30 20:49:47] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:1.03]
  
[2025-06-30 20:49:47] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:0.69]
  
[2025-06-30 20:49:47] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:0.91]
  
[2025-06-30 20:49:47] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.63]
  
[2025-06-30 20:49:47] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:0.67]
  
[2025-06-30 20:49:47] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:0.67]
  
[2025-06-30 20:49:47] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:1.2]
  
[2025-06-30 20:49:47] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.94]
  
[2025-06-30 20:49:47] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:0.59]
  
[2025-06-30 20:50:12] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:4]
  
[2025-06-30 20:50:12] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:0.92]
  
[2025-06-30 20:50:12] dev.INFO: 
select * from `coach_certifications` limit 1
  [執行超過10秒:0.65]
  
[2025-06-30 20:50:12] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:0.65]
  
[2025-06-30 20:50:12] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:0.52]
  
[2025-06-30 20:50:12] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:0.67]
  
[2025-06-30 20:50:12] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.85]
  
[2025-06-30 20:50:12] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.51]
  
[2025-06-30 20:50:12] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:0.51]
  
[2025-06-30 20:50:12] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:0.58]
  
[2025-06-30 20:50:12] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:0.89]
  
[2025-06-30 20:50:12] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:0.55]
  
[2025-06-30 20:50:12] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.61]
  
[2025-06-30 20:50:12] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:0.72]
  
[2025-06-30 20:50:12] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:0.83]
  
[2025-06-30 20:50:12] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:0.8]
  
[2025-06-30 20:50:12] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.79]
  
[2025-06-30 20:50:12] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:0.83]
  
[2025-06-30 20:50:52] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:15.79]
  
[2025-06-30 20:50:52] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:1.09]
  
[2025-06-30 20:50:52] dev.INFO: 
select * from `coach_certifications` limit 1
  [執行超過10秒:0.91]
  
[2025-06-30 20:50:52] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:1.01]
  
[2025-06-30 20:50:52] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:0.99]
  
[2025-06-30 20:50:52] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:0.78]
  
[2025-06-30 20:50:52] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:1.02]
  
[2025-06-30 20:50:52] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:1.08]
  
[2025-06-30 20:50:53] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:0.89]
  
[2025-06-30 20:50:53] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:1.09]
  
[2025-06-30 20:50:53] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:0.77]
  
[2025-06-30 20:50:53] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:1.01]
  
[2025-06-30 20:50:53] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.84]
  
[2025-06-30 20:50:53] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:0.99]
  
[2025-06-30 20:50:53] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:0.97]
  
[2025-06-30 20:50:53] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:0.91]
  
[2025-06-30 20:50:53] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:1.3]
  
[2025-06-30 20:50:53] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:1.05]
  
[2025-06-30 20:51:13] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:21.81]
  
[2025-06-30 20:51:13] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:0.86]
  
[2025-06-30 20:51:13] dev.INFO: 
select * from `coach_certifications` limit 1
  [執行超過10秒:0.5]
  
[2025-06-30 20:51:13] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:0.47]
  
[2025-06-30 20:51:13] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:0.85]
  
[2025-06-30 20:51:13] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:0.69]
  
[2025-06-30 20:51:13] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.6]
  
[2025-06-30 20:51:13] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.6]
  
[2025-06-30 20:51:13] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:0.69]
  
[2025-06-30 20:51:13] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:0.7]
  
[2025-06-30 20:51:13] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:0.99]
  
[2025-06-30 20:51:13] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:0.68]
  
[2025-06-30 20:51:13] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.8]
  
[2025-06-30 20:51:13] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:0.49]
  
[2025-06-30 20:51:13] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:0.64]
  
[2025-06-30 20:51:13] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:0.52]
  
[2025-06-30 20:51:13] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.8]
  
[2025-06-30 20:51:13] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:0.65]
  
[2025-06-30 20:51:33] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:15.72]
  
[2025-06-30 20:51:33] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:0.88]
  
[2025-06-30 20:51:33] dev.INFO: 
select * from `coach_certifications` limit 1
  [執行超過10秒:0.62]
  
[2025-06-30 20:51:33] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:0.65]
  
[2025-06-30 20:51:33] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:1.04]
  
[2025-06-30 20:51:33] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:0.76]
  
[2025-06-30 20:51:33] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.8]
  
[2025-06-30 20:51:33] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.99]
  
[2025-06-30 20:51:33] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:0.96]
  
[2025-06-30 20:51:33] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:0.7]
  
[2025-06-30 20:51:33] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:1.24]
  
[2025-06-30 20:51:33] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:0.87]
  
[2025-06-30 20:51:33] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.67]
  
[2025-06-30 20:51:33] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:0.67]
  
[2025-06-30 20:51:33] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:0.65]
  
[2025-06-30 20:51:33] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:0.9]
  
[2025-06-30 20:51:33] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.8]
  
[2025-06-30 20:51:33] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:0.75]
  
[2025-06-30 20:51:43] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:25.61]
  
[2025-06-30 20:51:43] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:2]
  
[2025-06-30 20:51:43] dev.INFO: 
select `migration` from `migrations`
 order by `batch` asc, `migration` asc
  [執行超過10秒:0.91]
  
[2025-06-30 20:51:43] dev.INFO: 
select `migration` from `migrations`
 order by `batch` asc, `migration` asc
  [執行超過10秒:0.99]
  
[2025-06-30 20:51:43] dev.INFO: 
select max(`batch`) as aggregate from `migrations`
  [執行超過10秒:0.8]
  
[2025-06-30 20:51:43] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:43] dev.INFO: 
create table `members` (`id` int unsigned not null auto_increment primary key comment '會員編號', `email` varchar(100) not null comment '電子信箱', `password` varchar(190) not null comment '密碼', `email_verified_at` timestamp null comment '信箱驗證時間', `name` varchar(50) not null comment '姓名', `nickname` varchar(50) null comment '暱稱', `phone` varchar(20) null comment '手機號碼', `birth_date` date null comment '生日', `gender` varchar(10) null comment '性別', `member_type` int not null default '1' comment '會員類型', `status` int not null default '1' comment '會員狀態', `snow_coin_balance` decimal(10, 2) not null default '0' comment '雪幣餘額', `referral_code` varchar(20) null comment '推薦碼', `referred_by` int unsigned null comment '推薦人編號', `avatar` varchar(190) null comment '頭像', `bio` mediumtext null comment '個人簡介', `last_login_at` timestamp null comment '最後登入時間', `created_at` timestamp not null default CURRENT_TIMESTAMP comment '建立時間', `updated_at` timestamp null on update CURRENT_TIMESTAMP comment '更新時間', `deleted_at` timestamp null comment '軟刪除') default character set utf8mb4 collate 'utf8mb4_unicode_ci'
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:43] dev.INFO: 
alter table `members` add index `members_status_member_type_index`(`status`, `member_type`)
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:43] dev.INFO: 
alter table `members` add constraint `members_referred_by_foreign` foreign key (`referred_by`) references `members` (`id`) on delete set null
  [執行超過10秒:0]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `members` add unique `members_email_unique`(`email`)
  [執行超過10秒:0]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `members` add index `members_member_type_index`(`member_type`)
  [執行超過10秒:0]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `members` add index `members_status_index`(`status`)
  [執行超過10秒:0]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `members` add unique `members_referral_code_unique`(`referral_code`)
  [執行超過10秒:0]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `members` auto_increment = 10000
  [執行超過10秒:0]
  
[2025-06-30 20:51:44] dev.INFO: 
ALTER TABLE members COMMENT '會員資料表'
  [執行超過10秒:0]
  
[2025-06-30 20:51:44] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
create table `coaches` (`id` int unsigned not null auto_increment primary key comment '教練編號', `member_id` int unsigned not null comment '會員編號', `name` varchar(50) not null comment '教練姓名', `description` mediumtext null comment '教練介紹', `avatar` varchar(190) null comment '教練照片', `experience_years` int not null default '0' comment '教學經驗年數', `specialties` json null comment '專長項目', `languages` json null comment '語言能力', `status` int not null default '0' comment '教練狀態', `hourly_rate` decimal(8, 2) not null default '0' comment '時薪', `rating` decimal(3, 2) not null default '0' comment '評分', `total_reviews` int not null default '0' comment '評價數量', `total_courses` int not null default '0' comment '授課次數', `availability` json null comment '可用時段', `is_available` tinyint(1) not null default '1' comment '是否接受預約', `certified_at` date null comment '認證通過日期', `certified_by` int unsigned null comment '認證人員', `created_at` timestamp not null default CURRENT_TIMESTAMP comment '建立時間', `updated_at` timestamp null on update CURRENT_TIMESTAMP comment '更新時間', `deleted_at` timestamp null comment '軟刪除') default character set utf8mb4 collate 'utf8mb4_unicode_ci'
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `coaches` add index `coaches_status_is_available_index`(`status`, `is_available`)
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `coaches` add constraint `coaches_member_id_foreign` foreign key (`member_id`) references `members` (`id`) on delete cascade
  [執行超過10秒:0]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `coaches` add constraint `coaches_certified_by_foreign` foreign key (`certified_by`) references `members` (`id`) on delete set null
  [執行超過10秒:0]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `coaches` add index `coaches_status_index`(`status`)
  [執行超過10秒:0]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `coaches` auto_increment = 10000
  [執行超過10秒:0]
  
[2025-06-30 20:51:44] dev.INFO: 
ALTER TABLE coaches COMMENT '教練資料表'
  [執行超過10秒:0]
  
[2025-06-30 20:51:44] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
create table `coach_certifications` (`id` int unsigned not null auto_increment primary key comment '認證編號', `coach_id` int unsigned not null comment '教練編號', `certification_type` int not null comment '認證類型', `certification_name` varchar(100) not null comment '認證名稱', `issuing_organization` varchar(100) not null comment '發證機構', `certification_number` varchar(50) null comment '認證號碼', `issued_date` date not null comment '發證日期', `expiry_date` date null comment '到期日期', `is_verified` tinyint(1) not null default '0' comment '是否已驗證', `verified_by` int unsigned null comment '驗證人員', `verified_at` timestamp null comment '驗證時間', `certificate_file` varchar(190) null comment '證書檔案', `notes` mediumtext null comment '備註', `created_at` timestamp not null default CURRENT_TIMESTAMP comment '建立時間', `updated_at` timestamp null on update CURRENT_TIMESTAMP comment '更新時間', `deleted_at` timestamp null comment '軟刪除') default character set utf8mb4 collate 'utf8mb4_unicode_ci'
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `coach_certifications` add index `coach_certifications_coach_id_certification_type_index`(`coach_id`, `certification_type`)
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `coach_certifications` add index `coach_certifications_is_verified_expiry_date_index`(`is_verified`, `expiry_date`)
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `coach_certifications` add constraint `coach_certifications_coach_id_foreign` foreign key (`coach_id`) references `coaches` (`id`) on delete cascade
  [執行超過10秒:0]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `coach_certifications` add constraint `coach_certifications_verified_by_foreign` foreign key (`verified_by`) references `members` (`id`) on delete set null
  [執行超過10秒:0]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `coach_certifications` add index `coach_certifications_certification_type_index`(`certification_type`)
  [執行超過10秒:0]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `coach_certifications` auto_increment = 10000
  [執行超過10秒:0]
  
[2025-06-30 20:51:44] dev.INFO: 
ALTER TABLE coach_certifications COMMENT '教練認證資料表'
  [執行超過10秒:0]
  
[2025-06-30 20:51:44] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
create table `courses` (`id` int unsigned not null auto_increment primary key comment '課程編號', `coach_id` int unsigned not null comment '教練編號', `title` varchar(100) not null comment '課程名稱', `description` mediumtext null comment '課程描述', `cover_image` varchar(190) null comment '課程封面', `difficulty_level` int not null comment '課程難度', `max_students` int not null default '1' comment '最大學員數', `duration_hours` int not null comment '課程時長(小時)', `price` decimal(8, 2) not null comment '課程價格', `snow_coin_price` decimal(8, 2) null comment '雪幣價格', `status` int not null default '0' comment '課程狀態', `is_featured` tinyint(1) not null default '0' comment '是否精選', `sort_order` int not null default '0' comment '排序', `total_bookings` int not null default '0' comment '總預約數', `average_rating` decimal(3, 2) not null default '0' comment '平均評分', `total_reviews` int not null default '0' comment '評價數量', `curriculum` json null comment '課程大綱', `requirements` json null comment '課程要求', `equipment_provided` json null comment '提供設備', `location` varchar(100) null comment '上課地點', `meeting_points` json null comment '集合地點', `created_at` timestamp not null default CURRENT_TIMESTAMP comment '建立時間', `updated_at` timestamp null on update CURRENT_TIMESTAMP comment '更新時間', `deleted_at` timestamp null comment '軟刪除') default character set utf8mb4 collate 'utf8mb4_unicode_ci'
  [執行超過10秒:0.02]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `courses` add index `courses_status_difficulty_level_index`(`status`, `difficulty_level`)
  [執行超過10秒:0.02]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `courses` add index `courses_is_featured_sort_order_index`(`is_featured`, `sort_order`)
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `courses` add constraint `courses_coach_id_foreign` foreign key (`coach_id`) references `coaches` (`id`) on delete cascade
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `courses` add index `courses_difficulty_level_index`(`difficulty_level`)
  [執行超過10秒:0.02]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `courses` add index `courses_status_index`(`status`)
  [執行超過10秒:0.02]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `courses` auto_increment = 10000
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
ALTER TABLE courses COMMENT '課程資料表'
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:0.02]
  
[2025-06-30 20:51:44] dev.INFO: 
create table `bookings` (`id` int unsigned not null auto_increment primary key comment '預約編號', `member_id` int unsigned not null comment '會員編號', `coach_id` int unsigned not null comment '教練編號', `course_id` int unsigned not null comment '課程編號', `booking_code` varchar(20) not null comment '預約代碼', `scheduled_at` datetime not null comment '預約時間', `duration_hours` int not null comment '課程時長', `student_count` int not null default '1' comment '學員人數', `status` int not null default '1' comment '預約狀態', `original_price` decimal(8, 2) not null comment '原始價格', `final_price` decimal(8, 2) not null comment '最終價格', `snow_coin_used` decimal(8, 2) not null default '0' comment '使用雪幣', `discount_amount` decimal(8, 2) not null default '0' comment '折扣金額', `payment_method` varchar(20) null comment '付款方式', `payment_status` varchar(20) not null default 'pending' comment '付款狀態', `paid_at` timestamp null comment '付款時間', `transaction_id` varchar(100) null comment '交易編號', `notes` mediumtext null comment '備註', `special_requirements` mediumtext null comment '特殊需求', `emergency_contact` varchar(20) null comment '緊急聯絡人電話', `cancelled_at` timestamp null comment '取消時間', `cancellation_reason` mediumtext null comment '取消原因', `cancelled_by` int unsigned null comment '取消人員', `created_at` timestamp not null default CURRENT_TIMESTAMP comment '建立時間', `updated_at` timestamp null on update CURRENT_TIMESTAMP comment '更新時間', `deleted_at` timestamp null comment '軟刪除') default character set utf8mb4 collate 'utf8mb4_unicode_ci'
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `bookings` add index `bookings_member_id_status_index`(`member_id`, `status`)
  [執行超過10秒:0.02]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `bookings` add index `bookings_coach_id_scheduled_at_index`(`coach_id`, `scheduled_at`)
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `bookings` add index `bookings_scheduled_at_status_index`(`scheduled_at`, `status`)
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `bookings` add constraint `bookings_member_id_foreign` foreign key (`member_id`) references `members` (`id`) on delete cascade
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `bookings` add constraint `bookings_coach_id_foreign` foreign key (`coach_id`) references `coaches` (`id`) on delete cascade
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `bookings` add constraint `bookings_course_id_foreign` foreign key (`course_id`) references `courses` (`id`) on delete cascade
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `bookings` add constraint `bookings_cancelled_by_foreign` foreign key (`cancelled_by`) references `members` (`id`) on delete set null
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `bookings` add unique `bookings_booking_code_unique`(`booking_code`)
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `bookings` add index `bookings_status_index`(`status`)
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `bookings` auto_increment = 10000
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
ALTER TABLE bookings COMMENT '預約資料表'
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
create table `reviews` (`id` int unsigned not null auto_increment primary key comment '評價編號', `member_id` int unsigned not null comment '評價者編號', `coach_id` int unsigned not null comment '被評價教練編號', `course_id` int unsigned not null comment '課程編號', `booking_id` int unsigned not null comment '預約編號', `rating` decimal(3, 2) not null comment '評分(1-5)', `teaching_rating` decimal(3, 2) null comment '教學評分', `professionalism_rating` decimal(3, 2) null comment '專業度評分', `communication_rating` decimal(3, 2) null comment '溝通評分', `title` varchar(100) null comment '評價標題', `content` mediumtext null comment '評價內容', `photos` json null comment '評價照片', `coach_reply` mediumtext null comment '教練回覆', `coach_replied_at` timestamp null comment '教練回覆時間', `is_verified` tinyint(1) not null default '0' comment '是否已驗證', `is_featured` tinyint(1) not null default '0' comment '是否精選', `is_anonymous` tinyint(1) not null default '0' comment '是否匿名', `helpful_count` int not null default '0' comment '有用數', `views` int not null default '0' comment '瀏覽次數', `created_at` timestamp not null default CURRENT_TIMESTAMP comment '建立時間', `updated_at` timestamp null on update CURRENT_TIMESTAMP comment '更新時間', `deleted_at` timestamp null comment '軟刪除') default character set utf8mb4 collate 'utf8mb4_unicode_ci'
  [執行超過10秒:0.02]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `reviews` add index `reviews_coach_id_rating_index`(`coach_id`, `rating`)
  [執行超過10秒:0.45]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `reviews` add index `reviews_course_id_rating_index`(`course_id`, `rating`)
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `reviews` add index `reviews_is_verified_is_featured_index`(`is_verified`, `is_featured`)
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `reviews` add unique `reviews_member_id_booking_id_unique`(`member_id`, `booking_id`)
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `reviews` add constraint `reviews_member_id_foreign` foreign key (`member_id`) references `members` (`id`) on delete cascade
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `reviews` add constraint `reviews_coach_id_foreign` foreign key (`coach_id`) references `coaches` (`id`) on delete cascade
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `reviews` add constraint `reviews_course_id_foreign` foreign key (`course_id`) references `courses` (`id`) on delete cascade
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `reviews` add constraint `reviews_booking_id_foreign` foreign key (`booking_id`) references `bookings` (`id`) on delete cascade
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `reviews` add index `reviews_rating_index`(`rating`)
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `reviews` auto_increment = 10000
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
ALTER TABLE reviews COMMENT '課程評價表'
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
create table `snow_coin_transactions` (`id` int unsigned not null auto_increment primary key comment '交易編號', `member_id` int unsigned not null comment '會員編號', `transaction_code` varchar(30) not null comment '交易代碼', `transaction_type` int not null comment '交易類型', `amount` decimal(10, 2) not null comment '交易金額', `balance_before` decimal(10, 2) not null comment '交易前餘額', `balance_after` decimal(10, 2) not null comment '交易後餘額', `booking_id` int unsigned null comment '關聯預約編號', `referral_id` int unsigned null comment '關聯推薦編號', `title` varchar(100) not null comment '交易標題', `description` mediumtext null comment '交易描述', `payment_method` varchar(20) null comment '付款方式', `payment_reference` varchar(100) null comment '付款參考號', `payment_verified_at` timestamp null comment '付款驗證時間', `operated_by` int unsigned null comment '操作人員', `admin_notes` mediumtext null comment '管理員備註', `is_verified` tinyint(1) not null default '1' comment '是否已驗證', `created_at` timestamp not null default CURRENT_TIMESTAMP comment '建立時間', `updated_at` timestamp null on update CURRENT_TIMESTAMP comment '更新時間', `deleted_at` timestamp null comment '軟刪除') default character set utf8mb4 collate 'utf8mb4_unicode_ci'
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `snow_coin_transactions` add index `snow_coin_transactions_member_id_transaction_type_index`(`member_id`, `transaction_type`)
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `snow_coin_transactions` add index `snow_coin_transactions_created_at_transaction_type_index`(`created_at`, `transaction_type`)
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `snow_coin_transactions` add constraint `snow_coin_transactions_member_id_foreign` foreign key (`member_id`) references `members` (`id`) on delete cascade
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `snow_coin_transactions` add constraint `snow_coin_transactions_booking_id_foreign` foreign key (`booking_id`) references `bookings` (`id`) on delete set null
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `snow_coin_transactions` add constraint `snow_coin_transactions_operated_by_foreign` foreign key (`operated_by`) references `members` (`id`) on delete set null
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `snow_coin_transactions` add unique `snow_coin_transactions_transaction_code_unique`(`transaction_code`)
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `snow_coin_transactions` add index `snow_coin_transactions_transaction_type_index`(`transaction_type`)
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `snow_coin_transactions` auto_increment = 10000
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
ALTER TABLE snow_coin_transactions COMMENT '雪幣交易記錄表'
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:0.02]
  
[2025-06-30 20:51:44] dev.INFO: 
create table `ski_trips` (`id` int unsigned not null auto_increment primary key comment '滑雪團編號', `organizer_id` int unsigned not null comment '主辦人編號', `title` varchar(100) not null comment '滑雪團名稱', `description` mediumtext not null comment '滑雪團描述', `cover_image` varchar(190) null comment '封面圖片', `gallery` json null comment '圖片集', `start_date` date not null comment '開始日期', `end_date` date not null comment '結束日期', `destination` varchar(100) not null comment '目的地', `itinerary` mediumtext null comment '行程安排', `max_participants` int not null comment '最大參與人數', `min_participants` int not null default '1' comment '最小成團人數', `current_participants` int not null default '0' comment '目前報名人數', `registration_deadline` date not null comment '報名截止日期', `price_per_person` decimal(10, 2) not null comment '每人費用', `deposit_amount` decimal(10, 2) not null comment '訂金金額', `price_includes` mediumtext null comment '費用包含', `price_excludes` mediumtext null comment '費用不包含', `status` int not null default '0' comment '滑雪團狀態', `is_featured` tinyint(1) not null default '0' comment '是否精選', `difficulty_level` int not null comment '適合難度', `requirements` mediumtext null comment '參加要求', `equipment_list` mediumtext null comment '攜帶裝備', `contact_person` varchar(50) null comment '聯絡人', `contact_phone` varchar(20) null comment '聯絡電話', `meeting_time` datetime null comment '集合時間', `meeting_location` varchar(200) null comment '集合地點', `created_at` timestamp not null default CURRENT_TIMESTAMP comment '建立時間', `updated_at` timestamp null on update CURRENT_TIMESTAMP comment '更新時間', `deleted_at` timestamp null comment '軟刪除') default character set utf8mb4 collate 'utf8mb4_unicode_ci'
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `ski_trips` add index `ski_trips_status_start_date_index`(`status`, `start_date`)
  [執行超過10秒:0.02]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `ski_trips` add index `ski_trips_difficulty_level_is_featured_index`(`difficulty_level`, `is_featured`)
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `ski_trips` add index `ski_trips_registration_deadline_status_index`(`registration_deadline`, `status`)
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `ski_trips` add constraint `ski_trips_organizer_id_foreign` foreign key (`organizer_id`) references `members` (`id`) on delete cascade
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `ski_trips` add index `ski_trips_status_index`(`status`)
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `ski_trips` add index `ski_trips_difficulty_level_index`(`difficulty_level`)
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `ski_trips` auto_increment = 10000
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
ALTER TABLE ski_trips COMMENT '滑雪團資料表'
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
create table `referrals` (`id` int unsigned not null auto_increment primary key comment '推薦記錄編號', `referrer_id` int unsigned not null comment '推薦人編號', `referred_id` int unsigned not null comment '被推薦人編號', `referral_code` varchar(20) not null comment '使用的推薦碼', `referral_source` varchar(50) null comment '推薦來源', `referrer_reward` decimal(8, 2) not null default '0' comment '推薦人獎勵', `referred_reward` decimal(8, 2) not null default '0' comment '被推薦人獎勵', `rewards_given` tinyint(1) not null default '0' comment '獎勵是否已發放', `rewards_given_at` timestamp null comment '獎勵發放時間', `first_purchase_completed` tinyint(1) not null default '0' comment '首次消費是否完成', `first_purchase_at` timestamp null comment '首次消費時間', `first_purchase_amount` decimal(8, 2) null comment '首次消費金額', `is_valid` tinyint(1) not null default '1' comment '推薦是否有效', `invalid_reason` mediumtext null comment '無效原因', `ip_address` varchar(45) null comment '註冊IP', `user_agent` varchar(255) null comment '瀏覽器資訊', `created_at` timestamp not null default CURRENT_TIMESTAMP comment '建立時間', `updated_at` timestamp null on update CURRENT_TIMESTAMP comment '更新時間', `deleted_at` timestamp null comment '軟刪除') default character set utf8mb4 collate 'utf8mb4_unicode_ci'
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `referrals` add index `referrals_referrer_id_rewards_given_index`(`referrer_id`, `rewards_given`)
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `referrals` add index `referrals_referred_id_is_valid_index`(`referred_id`, `is_valid`)
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `referrals` add index `referrals_referral_code_created_at_index`(`referral_code`, `created_at`)
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `referrals` add unique `referrals_referrer_id_referred_id_unique`(`referrer_id`, `referred_id`)
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `referrals` add constraint `referrals_referrer_id_foreign` foreign key (`referrer_id`) references `members` (`id`) on delete cascade
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `referrals` add constraint `referrals_referred_id_foreign` foreign key (`referred_id`) references `members` (`id`) on delete cascade
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
alter table `referrals` auto_increment = 10000
  [執行超過10秒:0.01]
  
[2025-06-30 20:51:44] dev.INFO: 
ALTER TABLE referrals COMMENT '推薦記錄表'
  [執行超過10秒:0.01]
  
[2025-06-30 20:52:01] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:15.51]
  
[2025-06-30 20:52:01] dev.INFO: 
select `migration` from `migrations`
 order by `batch` asc, `migration` asc
  [執行超過10秒:1.06]
  
[2025-06-30 20:52:01] dev.INFO: 
select `batch`, `migration` from `migrations`
 order by `batch` asc, `migration` asc
  [執行超過10秒:1.18]
  
[2025-06-30 20:52:15] dev.ERROR: The "--pretend" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--pretend\" option does not exist. at C:\\AppServ\\laravel\\coach\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\AppServ\\laravel\\coach\\vendor\\symfony\\console\\Input\\ArgvInput.php(152): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('pretend', NULL)
#1 C:\\AppServ\\laravel\\coach\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--pretend')
#2 C:\\AppServ\\laravel\\coach\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--pretend', true)
#3 C:\\AppServ\\laravel\\coach\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\AppServ\\laravel\\coach\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 C:\\AppServ\\laravel\\coach\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 C:\\AppServ\\laravel\\coach\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 C:\\AppServ\\laravel\\coach\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\RefreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 C:\\AppServ\\laravel\\coach\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 C:\\AppServ\\laravel\\coach\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\AppServ\\laravel\\coach\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-06-30 20:52:22] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:21.98]
  
[2025-06-30 20:52:22] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:1.6]
  
[2025-06-30 20:52:22] dev.INFO: 
select `migration` from `migrations`
 order by `batch` asc, `migration` asc
  [執行超過10秒:0.81]
  
[2025-06-30 20:52:22] dev.INFO: 
select `migration` from `migrations`
 order by `batch` asc, `migration` asc
  [執行超過10秒:0.98]
  
[2025-06-30 20:52:22] dev.INFO: 
select max(`batch`) as aggregate from `migrations`
  [執行超過10秒:0.78]
  
[2025-06-30 20:52:22] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:0.01]
  
[2025-06-30 20:52:22] dev.INFO: 
create table `coaches` (`id` int unsigned not null auto_increment primary key comment '教練編號', `member_id` int unsigned not null comment '會員編號', `name` varchar(50) not null comment '教練姓名', `description` mediumtext null comment '教練介紹', `avatar` varchar(190) null comment '教練照片', `experience_years` int not null default '0' comment '教學經驗年數', `specialties` json null comment '專長項目', `languages` json null comment '語言能力', `status` int not null default '0' comment '教練狀態', `hourly_rate` decimal(8, 2) not null default '0' comment '時薪', `rating` decimal(3, 2) not null default '0' comment '評分', `total_reviews` int not null default '0' comment '評價數量', `total_courses` int not null default '0' comment '授課次數', `availability` json null comment '可用時段', `is_available` tinyint(1) not null default '1' comment '是否接受預約', `certified_at` date null comment '認證通過日期', `certified_by` int unsigned null comment '認證人員', `created_at` timestamp not null default CURRENT_TIMESTAMP comment '建立時間', `updated_at` timestamp null on update CURRENT_TIMESTAMP comment '更新時間', `deleted_at` timestamp null comment '軟刪除') default character set utf8mb4 collate 'utf8mb4_unicode_ci'
  [執行超過10秒:0.01]
  
[2025-06-30 20:52:22] dev.INFO: 
alter table `coaches` add index `coaches_status_is_available_index`(`status`, `is_available`)
  [執行超過10秒:0.01]
  
[2025-06-30 20:52:22] dev.INFO: 
alter table `coaches` add constraint `coaches_member_id_foreign` foreign key (`member_id`) references `members` (`id`) on delete cascade
  [執行超過10秒:0]
  
[2025-06-30 20:52:22] dev.INFO: 
alter table `coaches` add constraint `coaches_certified_by_foreign` foreign key (`certified_by`) references `members` (`id`) on delete set null
  [執行超過10秒:0]
  
[2025-06-30 20:52:22] dev.INFO: 
alter table `coaches` add index `coaches_status_index`(`status`)
  [執行超過10秒:0]
  
[2025-06-30 20:52:22] dev.INFO: 
alter table `coaches` auto_increment = 10000
  [執行超過10秒:0]
  
[2025-06-30 20:52:22] dev.INFO: 
ALTER TABLE coaches COMMENT '教練資料表'
  [執行超過10秒:0]
  
[2025-06-30 20:52:29] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:27.25]
  
[2025-06-30 20:52:29] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:6.58]
  
[2025-06-30 20:52:29] dev.INFO: 
select `migration` from `migrations`
 order by `batch` asc, `migration` asc
  [執行超過10秒:1.06]
  
[2025-06-30 20:52:29] dev.INFO: 
select `migration` from `migrations`
 order by `batch` asc, `migration` asc
  [執行超過10秒:1.27]
  
[2025-06-30 20:52:29] dev.INFO: 
select max(`batch`) as aggregate from `migrations`
  [執行超過10秒:1.03]
  
[2025-06-30 20:52:30] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:2.33]
  
[2025-06-30 20:52:30] dev.INFO: 
insert into `migrations` (`migration`, `batch`) values ('2025_06_30_194739_create_members_table', '3')
  [執行超過10秒:3.87]
  
[2025-06-30 20:52:30] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:2.11]
  
[2025-06-30 20:52:30] dev.INFO: 
insert into `migrations` (`migration`, `batch`) values ('2025_06_30_194744_create_coaches_table', '3')
  [執行超過10秒:7.24]
  
[2025-06-30 20:52:30] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:2.6]
  
[2025-06-30 20:52:30] dev.INFO: 
insert into `migrations` (`migration`, `batch`) values ('2025_06_30_194746_create_coach_certifications_table', '3')
  [執行超過10秒:3.58]
  
[2025-06-30 20:52:30] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:2.29]
  
[2025-06-30 20:52:30] dev.INFO: 
insert into `migrations` (`migration`, `batch`) values ('2025_06_30_194747_create_courses_table', '3')
  [執行超過10秒:6.53]
  
[2025-06-30 20:52:30] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:1.82]
  
[2025-06-30 20:52:30] dev.INFO: 
insert into `migrations` (`migration`, `batch`) values ('2025_06_30_194749_create_bookings_table', '3')
  [執行超過10秒:3.71]
  
[2025-06-30 20:52:30] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:2.04]
  
[2025-06-30 20:52:30] dev.INFO: 
insert into `migrations` (`migration`, `batch`) values ('2025_06_30_194750_create_reviews_table', '3')
  [執行超過10秒:4.11]
  
[2025-06-30 20:52:30] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:2.79]
  
[2025-06-30 20:52:30] dev.INFO: 
insert into `migrations` (`migration`, `batch`) values ('2025_06_30_194757_create_snow_coin_transactions_table', '3')
  [執行超過10秒:3.52]
  
[2025-06-30 20:52:30] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:2.16]
  
[2025-06-30 20:52:30] dev.INFO: 
insert into `migrations` (`migration`, `batch`) values ('2025_06_30_194759_create_ski_trips_table', '3')
  [執行超過10秒:3.48]
  
[2025-06-30 20:52:30] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:1.88]
  
[2025-06-30 20:52:30] dev.INFO: 
insert into `migrations` (`migration`, `batch`) values ('2025_06_30_194801_create_referrals_table', '3')
  [執行超過10秒:6.81]
  
[2025-06-30 20:52:36] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:6.94]
  
[2025-06-30 20:52:36] dev.INFO: 
select `migration` from `migrations`
 order by `batch` asc, `migration` asc
  [執行超過10秒:0.92]
  
[2025-06-30 20:52:36] dev.INFO: 
select `batch`, `migration` from `migrations`
 order by `batch` asc, `migration` asc
  [執行超過10秒:0.63]
  
[2025-06-30 20:55:00] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:27.59]
  
[2025-06-30 20:55:00] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:4.24]
  
[2025-06-30 20:55:00] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:3.99]
  
[2025-06-30 20:55:07] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:26.72]
  
[2025-06-30 20:55:07] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:1.69]
  
[2025-06-30 20:55:07] dev.INFO: 
select `migration` from `migrations`
 order by `batch` asc, `migration` asc
  [執行超過10秒:0.59]
  
[2025-06-30 20:55:07] dev.INFO: 
select `migration` from `migrations`
 order by `batch` asc, `migration` asc
  [執行超過10秒:0.94]
  
[2025-06-30 20:55:13] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:22.61]
  
[2025-06-30 20:55:13] dev.INFO: 
select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables 
where
 table_schema = 'coach'
 and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')
 order by table_name
  [執行超過10秒:1.64]
  
[2025-06-30 20:55:13] dev.INFO: 
select `migration` from `migrations`
 order by `batch` asc, `migration` asc
  [執行超過10秒:0.89]
  
[2025-06-30 20:55:13] dev.INFO: 
select `migration` from `migrations`
 order by `batch` asc, `migration` asc
  [執行超過10秒:0.77]
  
[2025-06-30 20:56:03] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:27.01]
  
[2025-06-30 20:56:03] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:0.98]
  
[2025-06-30 20:56:03] dev.INFO: 
select * from `coach_certifications` limit 1
  [執行超過10秒:0.97]
  
[2025-06-30 20:56:03] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:1.36]
  
[2025-06-30 20:56:03] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:1.25]
  
[2025-06-30 20:56:03] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:1.43]
  
[2025-06-30 20:56:03] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.94]
  
[2025-06-30 20:56:03] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.86]
  
[2025-06-30 20:56:03] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:1]
  
[2025-06-30 20:56:03] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:1.09]
  
[2025-06-30 20:56:03] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:1.39]
  
[2025-06-30 20:56:03] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:1.22]
  
[2025-06-30 20:56:03] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:1.12]
  
[2025-06-30 20:56:03] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:1.64]
  
[2025-06-30 20:56:03] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:1.28]
  
[2025-06-30 20:56:03] dev.INFO: 
select * from `members` 
where
 `members`.`deleted_at` is null limit 1
  [執行超過10秒:1.04]
  
[2025-06-30 20:56:03] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:0.94]
  
[2025-06-30 20:56:03] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:1.07]
  
[2025-06-30 20:56:03] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:1.09]
  
[2025-06-30 20:56:44] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:22.2]
  
[2025-06-30 20:56:44] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:0.77]
  
[2025-06-30 20:56:44] dev.INFO: 
select * from `coach_certifications` limit 1
  [執行超過10秒:1.08]
  
[2025-06-30 20:56:44] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:0.89]
  
[2025-06-30 20:56:44] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:0.8]
  
[2025-06-30 20:56:45] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:0.92]
  
[2025-06-30 20:56:45] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.77]
  
[2025-06-30 20:56:45] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.82]
  
[2025-06-30 20:56:45] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:0.64]
  
[2025-06-30 20:56:45] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:0.57]
  
[2025-06-30 20:56:45] dev.INFO: 
select * from `coaches` 
where
 `coaches`.`deleted_at` is null limit 1
  [執行超過10秒:1.28]
  
[2025-06-30 20:56:45] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:0.63]
  
[2025-06-30 20:56:45] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:0.7]
  
[2025-06-30 20:56:45] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.69]
  
[2025-06-30 20:56:45] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:0.75]
  
[2025-06-30 20:56:45] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:1.01]
  
[2025-06-30 20:56:45] dev.INFO: 
select * from `members` 
where
 `members`.`deleted_at` is null limit 1
  [執行超過10秒:0.88]
  
[2025-06-30 20:56:45] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:0.82]
  
[2025-06-30 20:56:45] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.82]
  
[2025-06-30 20:56:45] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:0.62]
  
[2025-06-30 21:09:02] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:17.52]
  
[2025-06-30 21:09:02] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:1.34]
  
[2025-06-30 21:09:02] dev.INFO: 
select * from `coach_certifications` limit 1
  [執行超過10秒:0.83]
  
[2025-06-30 21:09:02] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:1.36]
  
[2025-06-30 21:09:02] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:1.05]
  
[2025-06-30 21:09:02] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:0.98]
  
[2025-06-30 21:09:02] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:1.02]
  
[2025-06-30 21:09:02] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:1.07]
  
[2025-06-30 21:09:02] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:1.16]
  
[2025-06-30 21:09:02] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:1.03]
  
[2025-06-30 21:09:02] dev.INFO: 
select * from `coaches` 
where
 `coaches`.`deleted_at` is null limit 1
  [執行超過10秒:1.07]
  
[2025-06-30 21:09:02] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:0.94]
  
[2025-06-30 21:09:02] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:0.86]
  
[2025-06-30 21:09:02] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.93]
  
[2025-06-30 21:09:02] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:1.01]
  
[2025-06-30 21:09:02] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:0.94]
  
[2025-06-30 21:09:02] dev.INFO: 
select * from `members` 
where
 `members`.`deleted_at` is null limit 1
  [執行超過10秒:1.23]
  
[2025-06-30 21:09:02] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:1.07]
  
[2025-06-30 21:09:02] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.81]
  
[2025-06-30 21:09:02] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:1.29]
  
[2025-06-30 21:09:16] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:21.34]
  
[2025-06-30 21:09:16] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:1]
  
[2025-06-30 21:09:16] dev.INFO: 
select * from `coach_certifications` limit 1
  [執行超過10秒:0.74]
  
[2025-06-30 21:09:16] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:0.92]
  
[2025-06-30 21:09:16] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:0.88]
  
[2025-06-30 21:09:16] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:0.93]
  
[2025-06-30 21:09:16] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.88]
  
[2025-06-30 21:09:16] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.74]
  
[2025-06-30 21:09:16] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:0.65]
  
[2025-06-30 21:09:16] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:0.67]
  
[2025-06-30 21:09:16] dev.INFO: 
select * from `coaches` 
where
 `coaches`.`deleted_at` is null limit 1
  [執行超過10秒:0.82]
  
[2025-06-30 21:09:16] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:0.78]
  
[2025-06-30 21:09:16] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:0.76]
  
[2025-06-30 21:09:16] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.59]
  
[2025-06-30 21:09:16] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:0.65]
  
[2025-06-30 21:09:16] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:0.64]
  
[2025-06-30 21:09:16] dev.INFO: 
select * from `members` 
where
 `members`.`deleted_at` is null limit 1
  [執行超過10秒:0.69]
  
[2025-06-30 21:09:16] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:0.55]
  
[2025-06-30 21:09:16] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.61]
  
[2025-06-30 21:09:16] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:0.59]
  
[2025-06-30 21:12:12] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:29.84]
  
[2025-06-30 21:12:12] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:0.8]
  
[2025-06-30 21:12:12] dev.INFO: 
select * from `coach_certifications` limit 1
  [執行超過10秒:0.82]
  
[2025-06-30 21:12:12] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:0.66]
  
[2025-06-30 21:12:12] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:0.98]
  
[2025-06-30 21:12:12] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:0.74]
  
[2025-06-30 21:12:12] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.65]
  
[2025-06-30 21:12:12] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.56]
  
[2025-06-30 21:12:12] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:0.7]
  
[2025-06-30 21:12:12] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:0.57]
  
[2025-06-30 21:12:12] dev.INFO: 
select * from `coaches` 
where
 `coaches`.`deleted_at` is null limit 1
  [執行超過10秒:0.7]
  
[2025-06-30 21:12:12] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:0.89]
  
[2025-06-30 21:12:12] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:0.79]
  
[2025-06-30 21:12:12] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.82]
  
[2025-06-30 21:12:12] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:0.69]
  
[2025-06-30 21:12:12] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:0.55]
  
[2025-06-30 21:12:12] dev.INFO: 
select * from `members` 
where
 `members`.`deleted_at` is null limit 1
  [執行超過10秒:1.06]
  
[2025-06-30 21:12:12] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:0.86]
  
[2025-06-30 21:12:12] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.79]
  
[2025-06-30 21:12:12] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:1]
  
[2025-06-30 21:17:39] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:26.4]
  
[2025-06-30 21:17:39] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:0.81]
  
[2025-06-30 21:17:39] dev.INFO: 
select * from `coach_certifications` limit 1
  [執行超過10秒:0.93]
  
[2025-06-30 21:17:39] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:0.84]
  
[2025-06-30 21:17:39] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:0.6]
  
[2025-06-30 21:17:39] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:0.83]
  
[2025-06-30 21:17:39] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.63]
  
[2025-06-30 21:17:40] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.95]
  
[2025-06-30 21:17:40] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:0.72]
  
[2025-06-30 21:17:40] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:0.72]
  
[2025-06-30 21:17:40] dev.INFO: 
select * from `coaches` 
where
 `coaches`.`deleted_at` is null limit 1
  [執行超過10秒:0.92]
  
[2025-06-30 21:17:40] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:0.76]
  
[2025-06-30 21:17:40] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:0.71]
  
[2025-06-30 21:17:40] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.69]
  
[2025-06-30 21:17:40] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:0.77]
  
[2025-06-30 21:17:40] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:0.51]
  
[2025-06-30 21:17:40] dev.INFO: 
select * from `members` 
where
 `members`.`deleted_at` is null limit 1
  [執行超過10秒:0.69]
  
[2025-06-30 21:17:40] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:0.54]
  
[2025-06-30 21:17:40] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.58]
  
[2025-06-30 21:17:40] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:0.83]
  
[2025-06-30 21:18:10] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:16.42]
  
[2025-06-30 21:18:10] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:1.29]
  
[2025-06-30 21:18:10] dev.INFO: 
select * from `coach_certifications` limit 1
  [執行超過10秒:0.91]
  
[2025-06-30 21:18:10] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:1.35]
  
[2025-06-30 21:18:10] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:1.08]
  
[2025-06-30 21:18:10] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:1.05]
  
[2025-06-30 21:18:10] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.94]
  
[2025-06-30 21:18:10] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.91]
  
[2025-06-30 21:18:10] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:0.91]
  
[2025-06-30 21:18:10] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:0.97]
  
[2025-06-30 21:18:10] dev.INFO: 
select * from `coaches` 
where
 `coaches`.`deleted_at` is null limit 1
  [執行超過10秒:1.07]
  
[2025-06-30 21:18:10] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:0.77]
  
[2025-06-30 21:18:10] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:0.73]
  
[2025-06-30 21:18:10] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.57]
  
[2025-06-30 21:18:10] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:0.83]
  
[2025-06-30 21:18:10] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:0.7]
  
[2025-06-30 21:18:10] dev.INFO: 
select * from `members` 
where
 `members`.`deleted_at` is null limit 1
  [執行超過10秒:3.37]
  
[2025-06-30 21:18:10] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:1.62]
  
[2025-06-30 21:18:10] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:1.43]
  
[2025-06-30 21:18:10] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:1.65]
  
[2025-06-30 21:29:55] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:14.78]
  
[2025-06-30 21:29:55] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:0.75]
  
[2025-06-30 21:29:55] dev.INFO: 
select * from `coach_certifications` limit 1
  [執行超過10秒:0.68]
  
[2025-06-30 21:29:55] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:0.75]
  
[2025-06-30 21:29:55] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:0.8]
  
[2025-06-30 21:29:55] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:0.79]
  
[2025-06-30 21:29:55] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.78]
  
[2025-06-30 21:29:55] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.7]
  
[2025-06-30 21:29:55] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:0.64]
  
[2025-06-30 21:29:55] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:0.75]
  
[2025-06-30 21:29:55] dev.INFO: 
select * from `coaches` 
where
 `coaches`.`deleted_at` is null limit 1
  [執行超過10秒:0.71]
  
[2025-06-30 21:29:55] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:0.56]
  
[2025-06-30 21:29:55] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:0.48]
  
[2025-06-30 21:29:55] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.53]
  
[2025-06-30 21:29:55] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:0.59]
  
[2025-06-30 21:29:55] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:0.52]
  
[2025-06-30 21:29:55] dev.INFO: 
select * from `members` 
where
 `members`.`deleted_at` is null limit 1
  [執行超過10秒:1.6]
  
[2025-06-30 21:29:55] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:0.65]
  
[2025-06-30 21:29:55] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.55]
  
[2025-06-30 21:29:55] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:0.46]
  
[2025-06-30 21:30:13] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:18.29]
  
[2025-06-30 21:30:13] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:0.6]
  
[2025-06-30 21:30:13] dev.INFO: 
select * from `coach_certifications` limit 1
  [執行超過10秒:0.64]
  
[2025-06-30 21:30:13] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:0.36]
  
[2025-06-30 21:30:13] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:0.51]
  
[2025-06-30 21:30:13] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:0.38]
  
[2025-06-30 21:30:13] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.57]
  
[2025-06-30 21:30:13] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.68]
  
[2025-06-30 21:30:13] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:0.3]
  
[2025-06-30 21:30:13] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:0.56]
  
[2025-06-30 21:30:13] dev.INFO: 
select * from `coaches` 
where
 `coaches`.`deleted_at` is null limit 1
  [執行超過10秒:0.49]
  
[2025-06-30 21:30:13] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:0.48]
  
[2025-06-30 21:30:13] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:0.82]
  
[2025-06-30 21:30:13] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.36]
  
[2025-06-30 21:30:13] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:0.41]
  
[2025-06-30 21:30:13] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:0.62]
  
[2025-06-30 21:30:13] dev.INFO: 
select * from `members` 
where
 `members`.`deleted_at` is null limit 1
  [執行超過10秒:0.55]
  
[2025-06-30 21:30:13] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:0.59]
  
[2025-06-30 21:30:13] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.54]
  
[2025-06-30 21:30:13] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:0.63]
  
[2025-06-30 21:30:31] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:15.33]
  
[2025-06-30 21:30:31] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:0.66]
  
[2025-06-30 21:30:31] dev.INFO: 
select * from `coach_certifications` limit 1
  [執行超過10秒:0.46]
  
[2025-06-30 21:30:31] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:0.63]
  
[2025-06-30 21:30:31] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:0.55]
  
[2025-06-30 21:30:31] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:0.64]
  
[2025-06-30 21:30:31] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.52]
  
[2025-06-30 21:30:31] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.37]
  
[2025-06-30 21:30:31] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:0.48]
  
[2025-06-30 21:30:31] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:0.49]
  
[2025-06-30 21:30:31] dev.INFO: 
select * from `coaches` 
where
 `coaches`.`deleted_at` is null limit 1
  [執行超過10秒:0.53]
  
[2025-06-30 21:30:31] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:0.32]
  
[2025-06-30 21:30:31] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:0.49]
  
[2025-06-30 21:30:31] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.49]
  
[2025-06-30 21:30:31] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:0.54]
  
[2025-06-30 21:30:31] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:0.53]
  
[2025-06-30 21:30:31] dev.INFO: 
select * from `members` 
where
 `members`.`deleted_at` is null limit 1
  [執行超過10秒:0.68]
  
[2025-06-30 21:30:31] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:0.77]
  
[2025-06-30 21:30:31] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.55]
  
[2025-06-30 21:30:31] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:0.25]
  
[2025-06-30 21:30:56] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:29.23]
  
[2025-06-30 21:30:56] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:0.82]
  
[2025-06-30 21:30:56] dev.INFO: 
select * from `coach_certifications` limit 1
  [執行超過10秒:1.38]
  
[2025-06-30 21:30:56] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:1.76]
  
[2025-06-30 21:30:56] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:0.81]
  
[2025-06-30 21:30:56] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:1.78]
  
[2025-06-30 21:30:56] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:1.14]
  
[2025-06-30 21:30:56] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.75]
  
[2025-06-30 21:30:56] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:1]
  
[2025-06-30 21:30:56] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:0.77]
  
[2025-06-30 21:30:56] dev.INFO: 
select * from `coaches` 
where
 `coaches`.`deleted_at` is null limit 1
  [執行超過10秒:0.88]
  
[2025-06-30 21:30:56] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:1]
  
[2025-06-30 21:30:56] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:0.74]
  
[2025-06-30 21:30:56] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.73]
  
[2025-06-30 21:30:56] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:0.83]
  
[2025-06-30 21:30:56] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:0.86]
  
[2025-06-30 21:30:56] dev.INFO: 
select * from `members` 
where
 `members`.`deleted_at` is null limit 1
  [執行超過10秒:0.65]
  
[2025-06-30 21:30:56] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:0.79]
  
[2025-06-30 21:30:56] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.57]
  
[2025-06-30 21:30:56] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:0.6]
  
[2025-06-30 21:31:06] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:24.33]
  
[2025-06-30 21:31:06] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:1.47]
  
[2025-06-30 21:31:06] dev.INFO: 
select * from `coach_certifications` limit 1
  [執行超過10秒:0.72]
  
[2025-06-30 21:31:06] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:0.9]
  
[2025-06-30 21:31:06] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:0.56]
  
[2025-06-30 21:31:06] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:0.74]
  
[2025-06-30 21:31:06] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.81]
  
[2025-06-30 21:31:06] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.97]
  
[2025-06-30 21:31:06] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:0.87]
  
[2025-06-30 21:31:06] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:0.56]
  
[2025-06-30 21:31:06] dev.INFO: 
select * from `coaches` 
where
 `coaches`.`deleted_at` is null limit 1
  [執行超過10秒:0.88]
  
[2025-06-30 21:31:06] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:0.83]
  
[2025-06-30 21:31:06] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:0.73]
  
[2025-06-30 21:31:06] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.9]
  
[2025-06-30 21:31:06] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:0.79]
  
[2025-06-30 21:31:07] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:0.73]
  
[2025-06-30 21:31:07] dev.INFO: 
select * from `members` 
where
 `members`.`deleted_at` is null limit 1
  [執行超過10秒:0.79]
  
[2025-06-30 21:31:07] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:0.65]
  
[2025-06-30 21:31:07] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:1.08]
  
[2025-06-30 21:31:07] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:0.75]
  
[2025-06-30 21:31:16] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:29.46]
  
[2025-06-30 21:31:16] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:1.57]
  
[2025-06-30 21:31:16] dev.INFO: 
select * from `coach_certifications` limit 1
  [執行超過10秒:1.64]
  
[2025-06-30 21:31:16] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:2.19]
  
[2025-06-30 21:31:16] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:1.23]
  
[2025-06-30 21:31:16] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:1.78]
  
[2025-06-30 21:31:16] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:1.74]
  
[2025-06-30 21:31:16] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:1.31]
  
[2025-06-30 21:31:16] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:1.37]
  
[2025-06-30 21:31:16] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:1.19]
  
[2025-06-30 21:31:17] dev.INFO: 
select * from `coaches` 
where
 `coaches`.`deleted_at` is null limit 1
  [執行超過10秒:1.15]
  
[2025-06-30 21:31:17] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:1.46]
  
[2025-06-30 21:31:17] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:0.98]
  
[2025-06-30 21:31:17] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:1.53]
  
[2025-06-30 21:31:17] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:1.11]
  
[2025-06-30 21:31:17] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:1.43]
  
[2025-06-30 21:31:17] dev.INFO: 
select * from `members` 
where
 `members`.`deleted_at` is null limit 1
  [執行超過10秒:1.39]
  
[2025-06-30 21:31:17] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:0.89]
  
[2025-06-30 21:31:17] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:1.39]
  
[2025-06-30 21:31:17] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:1.11]
  
[2025-06-30 21:32:18] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:28.24]
  
[2025-06-30 21:32:18] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:1.04]
  
[2025-06-30 21:32:18] dev.INFO: 
select * from `coach_certifications` limit 1
  [執行超過10秒:0.6]
  
[2025-06-30 21:32:18] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:0.76]
  
[2025-06-30 21:32:18] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:0.88]
  
[2025-06-30 21:32:19] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:0.66]
  
[2025-06-30 21:32:19] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.89]
  
[2025-06-30 21:32:19] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.7]
  
[2025-06-30 21:32:19] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:0.59]
  
[2025-06-30 21:32:19] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:0.58]
  
[2025-06-30 21:32:19] dev.INFO: 
select * from `coaches` 
where
 `coaches`.`deleted_at` is null limit 1
  [執行超過10秒:0.78]
  
[2025-06-30 21:32:19] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:0.65]
  
[2025-06-30 21:32:19] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:0.51]
  
[2025-06-30 21:32:19] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.57]
  
[2025-06-30 21:32:19] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:0.51]
  
[2025-06-30 21:32:19] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:0.9]
  
[2025-06-30 21:32:19] dev.INFO: 
select * from `members` 
where
 `members`.`deleted_at` is null limit 1
  [執行超過10秒:1.31]
  
[2025-06-30 21:32:19] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:0.95]
  
[2025-06-30 21:32:19] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.75]
  
[2025-06-30 21:32:19] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:0.9]
  
[2025-06-30 21:32:31] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:27.71]
  
[2025-06-30 21:32:31] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:0.86]
  
[2025-06-30 21:32:31] dev.INFO: 
select * from `coach_certifications` limit 1
  [執行超過10秒:0.56]
  
[2025-06-30 21:32:31] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:0.87]
  
[2025-06-30 21:32:31] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:0.81]
  
[2025-06-30 21:32:31] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:0.85]
  
[2025-06-30 21:32:31] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.81]
  
[2025-06-30 21:32:31] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.54]
  
[2025-06-30 21:32:31] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:0.9]
  
[2025-06-30 21:32:31] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:0.66]
  
[2025-06-30 21:32:31] dev.INFO: 
select * from `coaches` 
where
 `coaches`.`deleted_at` is null limit 1
  [執行超過10秒:0.57]
  
[2025-06-30 21:32:31] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:0.54]
  
[2025-06-30 21:32:31] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:0.77]
  
[2025-06-30 21:32:31] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.53]
  
[2025-06-30 21:32:31] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:0.74]
  
[2025-06-30 21:32:31] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:0.55]
  
[2025-06-30 21:32:31] dev.INFO: 
select * from `members` 
where
 `members`.`deleted_at` is null limit 1
  [執行超過10秒:0.86]
  
[2025-06-30 21:32:31] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:0.61]
  
[2025-06-30 21:32:31] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.93]
  
[2025-06-30 21:32:31] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:0.54]
  
[2025-06-30 21:32:39] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:26.3]
  
[2025-06-30 21:32:39] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:0.65]
  
[2025-06-30 21:32:39] dev.INFO: 
select * from `coach_certifications` limit 1
  [執行超過10秒:0.81]
  
[2025-06-30 21:32:39] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:0.48]
  
[2025-06-30 21:32:39] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:0.42]
  
[2025-06-30 21:32:39] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:0.51]
  
[2025-06-30 21:32:39] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.42]
  
[2025-06-30 21:32:39] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.67]
  
[2025-06-30 21:32:39] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:0.61]
  
[2025-06-30 21:32:39] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:0.51]
  
[2025-06-30 21:32:39] dev.INFO: 
select * from `coaches` 
where
 `coaches`.`deleted_at` is null limit 1
  [執行超過10秒:0.49]
  
[2025-06-30 21:32:39] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:0.59]
  
[2025-06-30 21:32:39] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:0.58]
  
[2025-06-30 21:32:39] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.69]
  
[2025-06-30 21:32:39] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:0.55]
  
[2025-06-30 21:32:39] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:0.69]
  
[2025-06-30 21:32:39] dev.INFO: 
select * from `members` 
where
 `members`.`deleted_at` is null limit 1
  [執行超過10秒:0.75]
  
[2025-06-30 21:32:39] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:0.75]
  
[2025-06-30 21:32:39] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.5]
  
[2025-06-30 21:32:39] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:0.61]
  
[2025-06-30 21:33:41] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:23.56]
  
[2025-06-30 21:33:41] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:0.79]
  
[2025-06-30 21:33:41] dev.INFO: 
select * from `coach_certifications` limit 1
  [執行超過10秒:0.83]
  
[2025-06-30 21:33:41] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:0.99]
  
[2025-06-30 21:33:41] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:0.84]
  
[2025-06-30 21:33:41] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:1.18]
  
[2025-06-30 21:33:41] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.97]
  
[2025-06-30 21:33:41] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.91]
  
[2025-06-30 21:33:41] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:1.04]
  
[2025-06-30 21:33:41] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:0.95]
  
[2025-06-30 21:33:41] dev.INFO: 
select * from `coaches` 
where
 `coaches`.`deleted_at` is null limit 1
  [執行超過10秒:1.16]
  
[2025-06-30 21:33:41] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:0.9]
  
[2025-06-30 21:33:41] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:0.77]
  
[2025-06-30 21:33:41] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.67]
  
[2025-06-30 21:33:41] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:0.56]
  
[2025-06-30 21:33:41] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:0.97]
  
[2025-06-30 21:33:41] dev.INFO: 
select * from `members` limit 1
  [執行超過10秒:0.98]
  
[2025-06-30 21:33:41] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:0.79]
  
[2025-06-30 21:33:41] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.78]
  
[2025-06-30 21:33:41] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:0.83]
  
[2025-06-30 21:34:31] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:3.67]
  
[2025-06-30 21:34:31] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:0.91]
  
[2025-06-30 21:34:31] dev.INFO: 
select * from `coach_certifications` limit 1
  [執行超過10秒:0.46]
  
[2025-06-30 21:34:31] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:0.63]
  
[2025-06-30 21:34:31] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:0.4]
  
[2025-06-30 21:34:31] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:0.49]
  
[2025-06-30 21:34:31] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.63]
  
[2025-06-30 21:34:31] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.4]
  
[2025-06-30 21:34:31] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:0.63]
  
[2025-06-30 21:34:31] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:0.53]
  
[2025-06-30 21:34:31] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:0.3]
  
[2025-06-30 21:34:31] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:0.43]
  
[2025-06-30 21:34:31] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:0.43]
  
[2025-06-30 21:34:31] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.89]
  
[2025-06-30 21:34:31] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:0.67]
  
[2025-06-30 21:34:31] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:0.52]
  
[2025-06-30 21:34:31] dev.INFO: 
select * from `members` limit 1
  [執行超過10秒:0.3]
  
[2025-06-30 21:34:31] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:0.56]
  
[2025-06-30 21:34:31] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.58]
  
[2025-06-30 21:34:31] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:0.55]
  
[2025-06-30 21:36:14] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:4.07]
  
[2025-06-30 21:36:14] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:0.66]
  
[2025-06-30 21:36:14] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.56]
  
[2025-06-30 21:36:14] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.62]
  
[2025-06-30 21:36:14] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:0.49]
  
[2025-06-30 21:36:14] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:0.57]
  
[2025-06-30 21:36:14] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:0.77]
  
[2025-06-30 21:36:14] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:0.57]
  
[2025-06-30 21:36:14] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:0.51]
  
[2025-06-30 21:36:14] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.79]
  
[2025-06-30 21:36:14] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:0.6]
  
[2025-06-30 21:36:14] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:0.56]
  
[2025-06-30 21:36:14] dev.INFO: 
select * from `members` limit 1
  [執行超過10秒:0.72]
  
[2025-06-30 21:36:14] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:0.9]
  
[2025-06-30 21:36:14] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.65]
  
[2025-06-30 21:36:14] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:0.41]
  
[2025-06-30 21:40:03] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:26.71]
  
[2025-06-30 21:40:03] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:0.39]
  
[2025-06-30 21:40:03] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.68]
  
[2025-06-30 21:40:03] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.54]
  
[2025-06-30 21:40:03] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:0.49]
  
[2025-06-30 21:40:03] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:0.5]
  
[2025-06-30 21:40:03] dev.INFO: 
select * from `coach_certifications` limit 1
  [執行超過10秒:0.41]
  
[2025-06-30 21:40:03] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:0.44]
  
[2025-06-30 21:40:03] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:0.7]
  
[2025-06-30 21:40:03] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:0.45]
  
[2025-06-30 21:40:03] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:0.64]
  
[2025-06-30 21:40:03] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.39]
  
[2025-06-30 21:40:03] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:0.7]
  
[2025-06-30 21:40:03] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:0.67]
  
[2025-06-30 21:40:03] dev.INFO: 
select * from `members` limit 1
  [執行超過10秒:0.67]
  
[2025-06-30 21:40:03] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:0.65]
  
[2025-06-30 21:40:03] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.64]
  
[2025-06-30 21:40:03] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:0.42]
  
[2025-06-30 21:41:48] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:24.86]
  
[2025-06-30 21:41:48] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:0.7]
  
[2025-06-30 21:41:48] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.88]
  
[2025-06-30 21:41:48] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.45]
  
[2025-06-30 21:41:48] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:0.78]
  
[2025-06-30 21:41:48] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:0.53]
  
[2025-06-30 21:41:48] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:0.62]
  
[2025-06-30 21:41:48] dev.INFO: 
select * from `coach_certifications` limit 1
  [執行超過10秒:0.72]
  
[2025-06-30 21:41:48] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:0.6]
  
[2025-06-30 21:41:48] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:0.88]
  
[2025-06-30 21:41:48] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:0.48]
  
[2025-06-30 21:41:48] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:1.01]
  
[2025-06-30 21:41:48] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.74]
  
[2025-06-30 21:41:48] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:0.7]
  
[2025-06-30 21:41:48] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:0.55]
  
[2025-06-30 21:41:48] dev.INFO: 
select * from `members` limit 1
  [執行超過10秒:0.91]
  
[2025-06-30 21:41:48] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:0.67]
  
[2025-06-30 21:41:48] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:1.07]
  
[2025-06-30 21:41:48] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:0.56]
  
[2025-06-30 21:42:43] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:25.2]
  
[2025-06-30 21:42:43] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:0.92]
  
[2025-06-30 21:42:43] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.55]
  
[2025-06-30 21:42:43] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.81]
  
[2025-06-30 21:42:43] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:0.53]
  
[2025-06-30 21:42:43] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:0.35]
  
[2025-06-30 21:42:43] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:0.58]
  
[2025-06-30 21:42:43] dev.INFO: 
select * from `coach_certifications` limit 1
  [執行超過10秒:0.44]
  
[2025-06-30 21:42:43] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:0.55]
  
[2025-06-30 21:42:43] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:0.59]
  
[2025-06-30 21:42:43] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:0.64]
  
[2025-06-30 21:42:43] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:0.77]
  
[2025-06-30 21:42:43] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.31]
  
[2025-06-30 21:42:43] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:0.8]
  
[2025-06-30 21:42:43] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:0.48]
  
[2025-06-30 21:42:43] dev.INFO: 
select * from `members` limit 1
  [執行超過10秒:0.38]
  
[2025-06-30 21:42:43] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:0.63]
  
[2025-06-30 21:42:43] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.49]
  
[2025-06-30 21:42:43] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:0.73]
  
[2025-06-30 21:43:19] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:4.83]
  
[2025-06-30 21:43:19] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:0.93]
  
[2025-06-30 21:43:19] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.51]
  
[2025-06-30 21:43:19] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.7]
  
[2025-06-30 21:43:19] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:0.79]
  
[2025-06-30 21:43:19] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:0.51]
  
[2025-06-30 21:43:19] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:0.92]
  
[2025-06-30 21:43:19] dev.INFO: 
select * from `coach_certifications` limit 1
  [執行超過10秒:0.62]
  
[2025-06-30 21:43:19] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:0.6]
  
[2025-06-30 21:43:19] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:0.79]
  
[2025-06-30 21:43:19] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:0.62]
  
[2025-06-30 21:43:19] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:0.68]
  
[2025-06-30 21:43:19] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.69]
  
[2025-06-30 21:43:19] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:0.6]
  
[2025-06-30 21:43:19] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:0.55]
  
[2025-06-30 21:43:19] dev.INFO: 
select * from `members` limit 1
  [執行超過10秒:0.66]
  
[2025-06-30 21:43:19] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:0.39]
  
[2025-06-30 21:43:19] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.53]
  
[2025-06-30 21:43:19] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:0.7]
  
[2025-06-30 21:44:27] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:21.58]
  
[2025-06-30 21:44:27] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:0.8]
  
[2025-06-30 21:44:27] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:1]
  
[2025-06-30 21:44:27] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.79]
  
[2025-06-30 21:44:27] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:0.95]
  
[2025-06-30 21:44:27] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:0.76]
  
[2025-06-30 21:44:27] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:1.86]
  
[2025-06-30 21:44:27] dev.INFO: 
select * from `coach_certifications` limit 1
  [執行超過10秒:0.75]
  
[2025-06-30 21:44:27] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:0.9]
  
[2025-06-30 21:44:27] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:0.83]
  
[2025-06-30 21:44:27] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:1]
  
[2025-06-30 21:44:27] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:0.96]
  
[2025-06-30 21:44:27] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.8]
  
[2025-06-30 21:44:27] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:0.82]
  
[2025-06-30 21:44:27] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:0.86]
  
[2025-06-30 21:44:27] dev.INFO: 
select * from `members` limit 1
  [執行超過10秒:1.18]
  
[2025-06-30 21:44:27] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:0.72]
  
[2025-06-30 21:44:27] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.76]
  
[2025-06-30 21:44:27] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:0.56]
  
[2025-06-30 21:44:27] dev.INFO: 
select * from `reviews` limit 1
  [執行超過10秒:2.76]
  
[2025-06-30 21:45:26] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:20.52]
  
[2025-06-30 21:45:26] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:0.85]
  
[2025-06-30 21:45:26] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.85]
  
[2025-06-30 21:45:26] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.9]
  
[2025-06-30 21:45:27] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:0.99]
  
[2025-06-30 21:45:27] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:0.82]
  
[2025-06-30 21:45:27] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:0.87]
  
[2025-06-30 21:45:27] dev.INFO: 
select * from `coach_certifications` limit 1
  [執行超過10秒:0.83]
  
[2025-06-30 21:45:27] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:0.89]
  
[2025-06-30 21:45:27] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:1.05]
  
[2025-06-30 21:45:27] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:0.87]
  
[2025-06-30 21:45:27] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:0.93]
  
[2025-06-30 21:45:27] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.95]
  
[2025-06-30 21:45:27] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:0.97]
  
[2025-06-30 21:45:27] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:0.88]
  
[2025-06-30 21:45:27] dev.INFO: 
select * from `members` limit 1
  [執行超過10秒:0.93]
  
[2025-06-30 21:45:27] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:0.95]
  
[2025-06-30 21:45:27] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.8]
  
[2025-06-30 21:45:27] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:0.52]
  
[2025-06-30 21:45:27] dev.INFO: 
select * from `reviews` limit 1
  [執行超過10秒:0.7]
  
[2025-06-30 21:45:27] dev.INFO: 
select * from `snow_coin_transactions` limit 1
  [執行超過10秒:0.78]
  
[2025-06-30 21:45:31] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:23.82]
  
[2025-06-30 21:45:31] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:0.91]
  
[2025-06-30 21:45:31] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.75]
  
[2025-06-30 21:45:31] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.66]
  
[2025-06-30 21:45:31] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:0.57]
  
[2025-06-30 21:45:31] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:0.83]
  
[2025-06-30 21:45:31] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:0.73]
  
[2025-06-30 21:45:31] dev.INFO: 
select * from `coach_certifications` limit 1
  [執行超過10秒:0.67]
  
[2025-06-30 21:45:31] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:0.65]
  
[2025-06-30 21:45:31] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:1.06]
  
[2025-06-30 21:45:31] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:0.93]
  
[2025-06-30 21:45:31] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:0.58]
  
[2025-06-30 21:45:31] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.59]
  
[2025-06-30 21:45:31] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:0.93]
  
[2025-06-30 21:45:31] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:0.55]
  
[2025-06-30 21:45:31] dev.INFO: 
select * from `members` limit 1
  [執行超過10秒:0.71]
  
[2025-06-30 21:45:31] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:0.67]
  
[2025-06-30 21:45:31] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.87]
  
[2025-06-30 21:45:31] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:0.62]
  
[2025-06-30 21:45:31] dev.INFO: 
select * from `reviews` limit 1
  [執行超過10秒:0.6]
  
[2025-06-30 21:45:31] dev.INFO: 
select * from `snow_coin_transactions` limit 1
  [執行超過10秒:0.58]
  
[2025-06-30 21:46:29] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:27.4]
  
[2025-06-30 21:46:29] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:0.83]
  
[2025-06-30 21:46:29] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.63]
  
[2025-06-30 21:46:29] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:1.24]
  
[2025-06-30 21:46:29] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:0.93]
  
[2025-06-30 21:46:29] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:0.7]
  
[2025-06-30 21:46:29] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:0.72]
  
[2025-06-30 21:46:29] dev.INFO: 
select * from `coach_certifications` limit 1
  [執行超過10秒:0.68]
  
[2025-06-30 21:46:29] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:0.76]
  
[2025-06-30 21:46:29] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:1.05]
  
[2025-06-30 21:46:29] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:1.22]
  
[2025-06-30 21:46:29] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:1.2]
  
[2025-06-30 21:46:29] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:1.66]
  
[2025-06-30 21:46:29] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:1.24]
  
[2025-06-30 21:46:29] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:1.1]
  
[2025-06-30 21:46:29] dev.INFO: 
select * from `members` limit 1
  [執行超過10秒:1.53]
  
[2025-06-30 21:46:29] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:1.45]
  
[2025-06-30 21:46:29] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:1.53]
  
[2025-06-30 21:46:29] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:1.43]
  
[2025-06-30 21:46:29] dev.INFO: 
select * from `reviews` limit 1
  [執行超過10秒:1.26]
  
[2025-06-30 21:46:29] dev.INFO: 
select * from `snow_coin_transactions` limit 1
  [執行超過10秒:1.44]
  
[2025-06-30 21:46:35] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:29.41]
  
[2025-06-30 21:46:35] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:1.09]
  
[2025-06-30 21:46:35] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.86]
  
[2025-06-30 21:46:35] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.91]
  
[2025-06-30 21:46:35] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:0.71]
  
[2025-06-30 21:46:35] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:0.75]
  
[2025-06-30 21:46:35] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:0.33]
  
[2025-06-30 21:46:35] dev.INFO: 
select * from `coach_certifications` limit 1
  [執行超過10秒:0.75]
  
[2025-06-30 21:46:35] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:1.05]
  
[2025-06-30 21:46:35] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:0.83]
  
[2025-06-30 21:46:35] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:1.35]
  
[2025-06-30 21:46:35] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:0.76]
  
[2025-06-30 21:46:35] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.84]
  
[2025-06-30 21:46:35] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:0.57]
  
[2025-06-30 21:46:35] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:0.59]
  
[2025-06-30 21:46:35] dev.INFO: 
select * from `members` limit 1
  [執行超過10秒:1.45]
  
[2025-06-30 21:46:35] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:0.9]
  
[2025-06-30 21:46:35] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.67]
  
[2025-06-30 21:46:35] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:0.94]
  
[2025-06-30 21:46:35] dev.INFO: 
select * from `reviews` limit 1
  [執行超過10秒:0.75]
  
[2025-06-30 21:46:35] dev.INFO: 
select * from `ski_trips` limit 1
  [執行超過10秒:0.89]
  
[2025-06-30 21:46:35] dev.INFO: 
select * from `snow_coin_transactions` limit 1
  [執行超過10秒:0.58]
  
[2025-06-30 21:47:32] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:16.17]
  
[2025-06-30 21:47:32] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:1.98]
  
[2025-06-30 21:47:32] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:1.57]
  
[2025-06-30 21:47:32] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:2.01]
  
[2025-06-30 21:47:33] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:1.12]
  
[2025-06-30 21:47:33] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:1.87]
  
[2025-06-30 21:47:33] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:1.1]
  
[2025-06-30 21:47:33] dev.INFO: 
select * from `coach_certifications` limit 1
  [執行超過10秒:1.72]
  
[2025-06-30 21:47:33] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:2.06]
  
[2025-06-30 21:47:33] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:1.26]
  
[2025-06-30 21:47:33] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:1.23]
  
[2025-06-30 21:47:33] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:1.48]
  
[2025-06-30 21:47:33] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:1.85]
  
[2025-06-30 21:47:33] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:1.17]
  
[2025-06-30 21:47:33] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:1.31]
  
[2025-06-30 21:47:33] dev.INFO: 
select * from `members` limit 1
  [執行超過10秒:1.34]
  
[2025-06-30 21:47:33] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:1.43]
  
[2025-06-30 21:47:33] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:1.01]
  
[2025-06-30 21:47:33] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:1.16]
  
[2025-06-30 21:47:33] dev.INFO: 
select * from `reviews` limit 1
  [執行超過10秒:1.58]
  
[2025-06-30 21:47:33] dev.INFO: 
select * from `ski_trips` limit 1
  [執行超過10秒:1.05]
  
[2025-06-30 21:47:33] dev.INFO: 
select * from `snow_coin_transactions` limit 1
  [執行超過10秒:1.61]
  
[2025-06-30 21:47:38] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:24.27]
  
[2025-06-30 21:47:38] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:0.42]
  
[2025-06-30 21:47:38] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.42]
  
[2025-06-30 21:47:38] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:1.1]
  
[2025-06-30 21:47:38] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:0.65]
  
[2025-06-30 21:47:38] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:0.56]
  
[2025-06-30 21:47:38] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:0.49]
  
[2025-06-30 21:47:38] dev.INFO: 
select * from `coach_certifications` limit 1
  [執行超過10秒:0.77]
  
[2025-06-30 21:47:38] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:0.78]
  
[2025-06-30 21:47:38] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:0.67]
  
[2025-06-30 21:47:38] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:0.72]
  
[2025-06-30 21:47:38] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:0.82]
  
[2025-06-30 21:47:38] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.65]
  
[2025-06-30 21:47:38] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:0.9]
  
[2025-06-30 21:47:38] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:0.55]
  
[2025-06-30 21:47:38] dev.INFO: 
select * from `members` limit 1
  [執行超過10秒:0.61]
  
[2025-06-30 21:47:38] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:0.77]
  
[2025-06-30 21:47:38] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.69]
  
[2025-06-30 21:47:38] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:0.8]
  
[2025-06-30 21:47:38] dev.INFO: 
select * from `referrals` limit 1
  [執行超過10秒:0.48]
  
[2025-06-30 21:47:38] dev.INFO: 
select * from `reviews` limit 1
  [執行超過10秒:0.6]
  
[2025-06-30 21:47:38] dev.INFO: 
select * from `ski_trips` limit 1
  [執行超過10秒:0.77]
  
[2025-06-30 21:47:38] dev.INFO: 
select * from `snow_coin_transactions` limit 1
  [執行超過10秒:0.68]
  
[2025-06-30 21:48:19] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:25.03]
  
[2025-06-30 21:48:19] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:0.71]
  
[2025-06-30 21:48:19] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.47]
  
[2025-06-30 21:48:19] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.38]
  
[2025-06-30 21:48:19] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:0.47]
  
[2025-06-30 21:48:19] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:0.56]
  
[2025-06-30 21:48:19] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:0.33]
  
[2025-06-30 21:48:19] dev.INFO: 
select * from `coach_certifications` limit 1
  [執行超過10秒:0.65]
  
[2025-06-30 21:48:19] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:0.64]
  
[2025-06-30 21:48:19] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:0.46]
  
[2025-06-30 21:48:19] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:0.47]
  
[2025-06-30 21:48:19] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:0.46]
  
[2025-06-30 21:48:19] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.69]
  
[2025-06-30 21:48:19] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:0.54]
  
[2025-06-30 21:48:19] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:0.56]
  
[2025-06-30 21:48:19] dev.INFO: 
select * from `members` limit 1
  [執行超過10秒:0.84]
  
[2025-06-30 21:48:19] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:0.64]
  
[2025-06-30 21:48:19] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.7]
  
[2025-06-30 21:48:19] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:0.28]
  
[2025-06-30 21:48:19] dev.INFO: 
select * from `referrals` limit 1
  [執行超過10秒:0.39]
  
[2025-06-30 21:48:19] dev.INFO: 
select * from `reviews` limit 1
  [執行超過10秒:0.49]
  
[2025-06-30 21:48:19] dev.INFO: 
select * from `ski_trips` limit 1
  [執行超過10秒:0.5]
  
[2025-06-30 21:48:19] dev.INFO: 
select * from `snow_coin_transactions` limit 1
  [執行超過10秒:0.58]
  
[2025-06-30 23:34:16] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:24.53]
  
[2025-06-30 23:34:16] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:0.46]
  
[2025-06-30 23:34:16] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.35]
  
[2025-06-30 23:34:16] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.54]
  
[2025-06-30 23:34:16] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:0.35]
  
[2025-06-30 23:34:16] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:0.44]
  
[2025-06-30 23:34:16] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:0.25]
  
[2025-06-30 23:34:16] dev.INFO: 
select * from `coach_certifications` limit 1
  [執行超過10秒:0.53]
  
[2025-06-30 23:34:16] dev.INFO: 
select * from `coaches` limit 1
  [執行超過10秒:0.77]
  
[2025-06-30 23:34:16] dev.INFO: 
select * from `courses` limit 1
  [執行超過10秒:0.93]
  
[2025-06-30 23:34:16] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:0.29]
  
[2025-06-30 23:34:16] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:0.39]
  
[2025-06-30 23:34:16] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.53]
  
[2025-06-30 23:34:16] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:0.38]
  
[2025-06-30 23:34:16] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:0.47]
  
[2025-06-30 23:34:16] dev.INFO: 
select * from `members` limit 1
  [執行超過10秒:0.5]
  
[2025-06-30 23:34:16] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:0.78]
  
[2025-06-30 23:34:16] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.6]
  
[2025-06-30 23:34:16] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:0.46]
  
[2025-06-30 23:34:16] dev.INFO: 
select * from `referrals` limit 1
  [執行超過10秒:0.51]
  
[2025-06-30 23:34:16] dev.INFO: 
select * from `reviews` limit 1
  [執行超過10秒:0.47]
  
[2025-06-30 23:34:16] dev.INFO: 
select * from `ski_trips` limit 1
  [執行超過10秒:0.46]
  
[2025-06-30 23:34:16] dev.INFO: 
select * from `snow_coin_transactions` limit 1
  [執行超過10秒:0.46]
  
[2025-06-30 23:34:37] dev.INFO: 
select * from `adminuser` limit 1
  [執行超過10秒:26.65]
  
[2025-06-30 23:34:37] dev.INFO: 
select * from `adminuserloginlog` limit 1
  [執行超過10秒:0.51]
  
[2025-06-30 23:34:37] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.57]
  
[2025-06-30 23:34:37] dev.INFO: 
select * from `board` limit 1
  [執行超過10秒:0.42]
  
[2025-06-30 23:34:37] dev.INFO: 
select * from `bookings` limit 1
  [執行超過10秒:0.67]
  
[2025-06-30 23:34:37] dev.INFO: 
select * from `city1` limit 1
  [執行超過10秒:0.49]
  
[2025-06-30 23:34:37] dev.INFO: 
select * from `city2` limit 1
  [執行超過10秒:0.4]
  
[2025-06-30 23:34:37] dev.INFO: 
select * from `epost` limit 1
  [執行超過10秒:0.55]
  
[2025-06-30 23:34:37] dev.INFO: 
select * from `failed_jobs` limit 1
  [執行超過10秒:0.61]
  
[2025-06-30 23:34:37] dev.INFO: 
select * from `formquerylog` limit 1
  [執行超過10秒:0.58]
  
[2025-06-30 23:34:37] dev.INFO: 
select * from `jobs` limit 1
  [執行超過10秒:0.34]
  
[2025-06-30 23:34:37] dev.INFO: 
select * from `kind` limit 1
  [執行超過10秒:0.26]
  
[2025-06-30 23:34:37] dev.INFO: 
select * from `migrations` limit 1
  [執行超過10秒:0.33]
  
[2025-06-30 23:34:37] dev.INFO: 
select * from `personal_access_tokens` limit 1
  [執行超過10秒:0.51]
  
