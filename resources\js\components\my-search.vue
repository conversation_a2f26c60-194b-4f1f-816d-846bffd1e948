<template>
    <div class="scroll-container">
        <el-row class="scroll-content">
            <div class="col-auto">
                <!-- 如果有 searchNames 且不為空，則顯示搜索相關的輸入框和下拉選單 -->
                <template v-if="searchNames && Object.keys(searchNames).length > 0">
                    <template v-if="searchSelects.length == 0">
                        <el-input placeholder="Search.." v-model="inputs.search" />
                    </template>

                    <template v-if="searchSelects.length > 0">
                        <el-select placeholder="Select" v-model="searchSelected">
                            <el-option value="">Select</el-option>
                            <el-option v-for="(rs, index) in searchSelects" :key="rs.id" :value="rs.value" :label="rs.label" />
                        </el-select>
                    </template>

                    <el-select placeholder="Select" v-model="inputs.searchname" @change="onChangeSearchName">
                        <el-option v-for="(label, key) in searchNames" :key="key" :value="key" :label="label" />
                    </el-select>

                    <button type="button" class="btn btn-primary btn-sm" @click="onSubmit">
                        <i class="fa fa-search"></i>
                    </button>
                </template>

                <!-- 如果有 searchDateNames 且不為空，則顯示日期選擇器 -->
                <template v-if="searchDateNames && Object.keys(searchDateNames).length > 0">
                    <el-select v-model="inputs.searchdatename" @change="onChangeSearchName">
                        <el-option v-for="(label, key) in searchDateNames" :key="key" :value="key" :label="label" />
                    </el-select>
                    <my-dateform v-model="inputs.searchstartdate" placeholder="Pick a day" type="date" />
                    ~
                    <my-dateform v-model="inputs.searchenddate" placeholder="Pick a day" type="date" />
                    <button type="button" class="btn btn-primary btn-sm" @click="onSubmit">
                        <i class="fa fa-search"></i>
                    </button>
                </template>

                <slot></slot>
            </div>
        </el-row>
    </div>
</template>

<script setup lang="ts">
// 引入 Vue 的 reactivity 相關函式
import { ref, reactive, computed, onMounted } from 'vue'
// 引入 HTTP 工具
import { createHttp } from '@/utils/http'
const http = createHttp()
// 使用資料存儲
import { useDataStore } from '@/stores'

// 初始化資料存儲
const store = useDataStore()

// 定義組件的屬性
const props = defineProps({
    searchNames: {
        type: Object, // 定義 searchNames 為物件
        required: false // 此屬性為選填
    },
    searchDateNames: {
        type: Object, // 定義 searchDateNames 為物件
        required: false // 此屬性為選填
    }
})

// 定義反應式變數
let searchSelects = ref([]) // 用來存儲下拉選項
const inputs = reactive({
    search: '',
    searchname: '',
    searchdatename: '',
    searchstartdate: '',
    searchenddate: ''
})

// 計算屬性，驅動下拉選單的選擇
const searchSelected = computed({
    get() {
        return inputs.search // 返回當前的搜索值
    },
    set(val) {
        inputs.search = val // 設定新的搜索值
    }
})

// 定義 emit 事件
const emit = defineEmits(['onSearch'])

// 提交函數
function onSubmit() {
    // 當搜索值不為空且搜索名稱為空時，則處理選中的名稱

    if (inputs.search !== '' && inputs.searchname === '') {
        let names = Object.keys(props.searchNames || []).join('^')
        names = names.replace(/^\^+|\^+$/g, '')

        inputs.searchname = names
    }

    // 發佈搜索事件
    emit('onSearch', inputs)

    // 如果包含多個名稱，則重置搜索名稱
    if (inputs.searchname.indexOf('^') > -1) {
        inputs.searchname = ''
    }
}

// 處理搜索名稱變更
const onChangeSearchName = async () => {
    try {
        if (!props.searchNames) {
            console.error('searchNames is undefined')
            return
        }

        const label = props.searchNames[inputs.searchname]
        let labels = []
        if (typeof store.setup[label] != 'undefined') {
            labels = store.setup[label]['KIND'] || []
        }

        inputs.search = ''
        searchSelects.value = [] // 清空下拉選項

        if (labels.length > 0) {
            // 若 labels 被定義，則填充下拉選項

            if (Array.isArray(labels)) {
                labels.forEach(rs => {
                    if (rs['傳回值'] === '') {
                        rs['value'] = rs['資料']
                    } else {
                        rs['value'] = rs['傳回值']
                    }
                    rs['label'] = rs['資料']
                })

                searchSelects.value = labels
            } else {
                console.error('labels is not an array')
            }
        } else {
            // 若 labels 未被定義，則發送 API 請求
            const response = await http.post('api/admin/dependentdropdown', { label }, false)

            if (response.resultcode == 0) {
                response.data.forEach(rs => {
                    searchSelects.value.push({
                        value: String(Object.values(rs)[0]),
                        label: String(Object.values(rs)[1] || Object.values(rs)[0]) // 提供默認標籤
                    })
                })
            } else {
                throw new Error(response.resultmessage)
            }
        }
    } catch (error) {
        console.error(error) // 日誌錯誤信息
    }
}

// 在組件掛載時初始化
onMounted(() => {
    if (props.searchDateNames) {
        inputs.searchdatename = Object.keys(props.searchDateNames).length > 0 ? Object.keys(props.searchDateNames)[0] : ''
    }
    if (props.searchNames) {
        inputs.searchname = Object.keys(props.searchNames).length > 0 ? Object.keys(props.searchNames)[0] : ''
    }
})

// 暴露 inputs，讓父組件可以使用
defineExpose({
    inputs
})
</script>

<style scoped>
.el-input,
.el-select {
    padding: 5px;
    max-width: 120px;
}
.bt {
    padding: 10px;
}
.el-date-editor.el-input,
.el-date-editor.el-input__wrapper {
    padding: 5px;
    max-width: 50px !important;
}

.scroll-container {
    min-height: 60px;
    padding-left: 10px;
    overflow-x: auto; /* 添加水平滾動條 */
    white-space: nowrap; /* 防止內容換行 */
}

.scroll-content {
    display: inline-block; /* 使內容水平排列 */
}
</style>
