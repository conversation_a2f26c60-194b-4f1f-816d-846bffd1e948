<?php

namespace Tests\Feature\api\admin;


/***
"功能名稱":"管理人員 單位測試",
"資料表":"adminuser",
"建立時間":"2025-02-10 16:06:25 ",
 ***/
//command "php artisan test --filter tests/Feature/admin/adminuserTest.php
class adminuserTest extends baseTest {
  public $adminuser;
  public function setUp(): void {
    parent::setUp();
  }
  /**
   * 測試資料列表功能.
   *
   * @return void
   */
  public function test_列表index管理人員() {


    $datas = [

      'header' =>
      array(
        'HTTP_Authorization' => 'Bearer ' . $this->adminuser->api_token,
        'CONTENT_TYPE' => 'application/json',
      ),
      'url' => '/api/admin/adminuser',
      'raw' =>
      array(
        'page' => 1,
        'pagesize' => 10,
        'searchname' =>  'account',
        'search' => $this->adminuser->account,
      ),
      'post' => NULL,

    ];

    $response = $this->withHeaders($datas['header'])
      ->json('POST', $datas['url'], $datas['raw']);
    // echo $response->getStatusCode();
    //echo "response" . $response->getContent();

    $this->checkJson($response);


    //檢查回傳資料是否存在
    $response
      ->assertStatus(200)
      ->assertJsonPath('data.data.0.account', $this->adminuser->account);
    $jsonArray = $response->json();
    $this->assertGreaterThan(1, count($jsonArray['data']));
  }
  public function test_新增store管理人員() {

    $datas = [

      'header' =>
      array(
        'HTTP_Authorization' => 'Bearer ' . $this->adminuser->api_token,
        'CONTENT_TYPE' => 'application/json',
      ),
      'url' => '/api/admin/adminuser/store',
      'raw' =>
      array(
        'account' => $this->myFaker->getAccount(), //帳號-
        'name' => $this->myFaker->getName(), //姓名-
        'password' => \Hash::make('a123456789'), //密碼-
        'email' => $this->myFaker->getEmail(), //返回一個隨機郵箱,//EMAIL-
        'role' => $this->myFaker->getXml('角色'), //角色-[999:管理者 ; ]
        'limits' => $this->faker->realText(20), //權限-
        'lastlogin_dt' => $this->myFaker->getDate(), //最後登入時間-
        'lastlogin_ip' => '127.0.0.1', //最後登入IP-
        'online' => $this->myFaker->getXml('是否核可'), //是否核可-[1:是 ; 0:否 ; ]
        'failcount' => $this->faker->randomDigit, //登入錯誤次數-
        'created_at' => now(), //建立日期-

      ),
      'post' => NULL,

    ];
    $response = $this->withHeaders($datas['header'])
      ->json('POST', $datas['url'], $datas['raw']);

    // echo $response->getStatusCode();
    //echo "response" . $response->getContent();

    $this->checkJson($response);
    //檢查資料表資料是否存在
    $this->assertDatabaseHas('adminuser', [
      'account'       => $datas['raw']['account'],
      'name'       => $datas['raw']['name'],

      'email'       => $datas['raw']['email'],


    ]);
  }
  /**
   * 測試資料新增編輯寫入功能.
   *
   * @return void
   */
  public function test_編輯store管理人員() {

    $datas = [

      'header' =>
      array(
        'HTTP_Authorization' => 'Bearer ' . $this->adminuser->api_token,
        'CONTENT_TYPE' => 'application/json',
      ),
      'url' => '/api/admin/adminuser/store',
      'raw' =>
      array(
        'account' => $this->myFaker->getAccount(), //帳號-
        'name' => $this->myFaker->getName(), //姓名-
        'password' => \Hash::make('a123456789'), //密碼-
        'email' => $this->myFaker->getEmail(), //返回一個隨機郵箱,//EMAIL-
        'role' => $this->myFaker->getXml('角色'), //角色-[999:管理者 ; ]
        'limits' => $this->faker->realText(20), //權限-
        'lastlogin_dt' => $this->myFaker->getDate(), //最後登入時間-
        'lastlogin_ip' => '127.0.0.1', //最後登入IP-
        'online' => $this->myFaker->getXml('是否核可'), //是否核可-[1:是 ; 0:否 ; ]
        'failcount' => $this->faker->randomDigit, //登入錯誤次數-
        'created_at' => now(), //建立日期-
        'id' => $this->adminuser->id,

      ),
      'post' => NULL,

    ];

    $response = $this->withHeaders($datas['header'])
      ->json('POST', $datas['url'], $datas['raw']);
    // echo $response->getStatusCode();
    //echo "response" . $response->getContent();

    $this->checkJson($response);
    //檢查資料表資料是否存在
    $this->assertDatabaseHas('adminuser', [
      'account'       => $datas['raw']['account'],
      'name'       => $datas['raw']['name'],

      'email'       => $datas['raw']['email'],


    ]);
  }
  /**
   * 測試資料刪除功能.
   *
   * @return void
   */
  public function test_刪除destroy管理人員() {


    $datas = [
      'header' =>
      array(
        'HTTP_Authorization' => 'Bearer ' . $this->adminuser->api_token,
        'CONTENT_TYPE' => 'application/json',
      ),
      'url' => '/api/admin/adminuser/destroy',
      'raw' =>
      array(
        'del' => $this->adminuser->id,
      ),
    ];
    $response = $this->withHeaders($datas['header'])
      ->json('POST', $datas['url'], $datas['raw']);

    // echo $response->getStatusCode();
    echo $response->getContent();

    $this->checkJson($response);
    $response->assertStatus(200)->assertJson([
      'resultcode' => 0,
      'resultmessage' => '刪除成功'
    ]);
    $this->assertDatabaseMissing('adminuser', [
      'id'       => $this->adminuser->id,
    ]);
  }
}
