<?php

namespace App\Models;

use PF;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Database\Eloquent\Model as Eloquent;
use Illuminate\Auth\Authenticatable as AuthenticableTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Laravel\Sanctum\HasApiTokens;
/*swagger api document start*/
/**
 * @OA\Schema(
 *   schema="adminuser",
 *      allOf={
 *         @OA\Schema( @OA\Property(property="id", type="integer",description="自動編號", example=""  )),
*         @OA\Schema( @OA\Property(property="account", type="string",description="帳號", example=""  )),
*         @OA\Schema( @OA\Property(property="name", type="string",description="姓名", example=""  )),
*         @OA\Schema( @OA\Property(property="password", type="string",description="密碼", example=""  )),
*         @OA\Schema( @OA\Property(property="email", type="string",description="EMAIL", example=""  )),
*         @OA\Schema( @OA\Property(property="role", type="integer",description="角色 //管理者[999],", example=""  )),
*         @OA\Schema( @OA\Property(property="limits", type="string",description="權限", example=""  )),
*         @OA\Schema( @OA\Property(property="lastlogin_dt", type="string",description="最後登入時間", example=""  )),
*         @OA\Schema( @OA\Property(property="lastlogin_ip", type="string",description="最後登入IP", example=""  )),
*         @OA\Schema( @OA\Property(property="online", type="integer",description="是否核可 //是[1],否[0],", example=""  )),
*         @OA\Schema( @OA\Property(property="failcount", type="integer",description="登入錯誤次數", example=""  )),
*         @OA\Schema( @OA\Property(property="created_at", type="string",description="建立時間", example=""  )),
*         @OA\Schema( @OA\Property(property="updated_at", type="string",description="編輯時間", example=""  )),

 *      }
 *)
 */
/*swagger api document end*/
class adminuser extends baseModel implements Authenticatable {

    use AuthenticableTrait;
    use HasApiTokens, HasFactory;

    public $tabletitle = '管理人員';
    public $table = 'adminuser';
    public $primaryKey = 'id';
    // public function getAuthPassword()
    // {
    //     return $this->password;
    // }

    //欄位必填
    public $rules = [
        'id' => 'required',
        'account' => 'required',
        //'password' => 'required',
        'status' => 'required',
        //'email' => 'required',
    ];
    public $fieldInfo = [
'id'=>['title'=>'自動編號','type'=>'int(10) unsigned'],//
'account'=>['title'=>'帳號','type'=>'varchar(50)'],//
'name'=>['title'=>'姓名','type'=>'varchar(50)'],//
'password'=>['title'=>'密碼','type'=>'varchar(100)'],//
'email'=>['title'=>'EMAIL','type'=>'varchar(255)'],//
'role'=>['title'=>'角色','type'=>'int(11)'],// //管理者[999],
'limits'=>['title'=>'權限','type'=>'varchar(190)'],//
'lastlogin_dt'=>['title'=>'最後登入時間','type'=>'datetime'],//
'lastlogin_ip'=>['title'=>'最後登入IP','type'=>'varchar(45)'],//
'online'=>['title'=>'是否核可','type'=>'tinyint(4)'],// //是[1],否[0],
'failcount'=>['title'=>'登入錯誤次數','type'=>'int(11)'],//
'created_at'=>['title'=>'建立時間','type'=>'timestamp'],//
'updated_at'=>['title'=>'編輯時間','type'=>'timestamp'],//
];

    // protected $hidden = [
    //                 'password',
    //             ];
    //讓 Model 連接指定的 connection
    //protected $connection = 'connection-name';

    //是否讓 Eloquent 來自動維護 created_at 和 updated_at 欄位
    //public $timestamps = false;
    public $timestamps = true;

    //日期欄位的儲存格式。'Y-m-d' or 'U' or ...
    //protected $dateFormat = 'Y-m-d';
    protected $dates = ['lastlogin_dt','created_at','updated_at'];

    //const UPDATED_AT = 'last_update';
    //需要被轉換成日期的屬性。(通常輔助 softdeletes 使用)
    //protected $dates = ['deleted_at'];

    //「連動」，資料更新時，可連帶更新上層 updated_at 欄位
    //此 Model 為 belongsTo 或 belongsToMany
    //protected $touches = ['post', 'model2', ...];

    //可以被大量賦值的屬性。

    protected $fillable = ['account','name','password','email','role','limits','lastlogin_dt','lastlogin_ip','online','failcount','created_at','updated_at']; //可充許傳入的欄位
    protected $guarded = [];   //拒絶修改的欄位(fillable,guarded都設已fillable為準)
    //不可以被大量賦值的屬性。
    //protected $guarded = ['_token', 'edit'];

    //被大量撈出時，不會顯示在物件中，要暫時可見需使用 makeVisible()
    //protected $hidden = ['password'];

    //應該在陣列中可見的屬性。(白名單)
    //protected $visible = ['first_name', 'last_name'];

    //把指定值附加到 Model 資料陣列中。需實作 get{fieldname}Attribute()
    //可參考 eloquent-serialization 官方文件
    //protected $appends = ['is_admin'];

    //輸出時，轉化格式
    //可參考 Eloquent: Mutators 官方文件
    //protected $casts = ['is_admin' => 'boolean','raw_json' => 'array',];
    // public function adminuserloginlog()
    // {
    //     return $this->hasMany('App\Models\adminuserloginlog');
    // }
    /*Relations start*/
    /*Relations end*/

    public static function boot() {
        parent::boot();

        static::creating(function ($model) {
            //$model->uuid = (string) Uuid::generate();
        });
        static::updating(function ($model) {
            // do some logging
        });
        static::deleted(function ($model) {
            /*Del Relations start*/
            /*Del Relations end*/
        });
    }

    // public function getRoleAttribute()
    // {
    //     return $this->status;
    // }
    public function getStatusAttribute() {
        return $this->role;
    }
    public function getLimitsAttribute() {
        //$items=explode("chr(13).chr(10)",$s);
        if ($this->limits = "") {
            return explode(',', $this->limits);
        }
    }

    public function can($role = null, $limit = null) {
        $adminrole = \Auth::guard('admin')->user()->role;

        if ('999' == $adminrole) {
            return true;
        }
        $iscan = true;

        if (null != $role && '' != $role && '0' != $role && 'ALL' != $role) {
            $iscan = in_array($adminrole, explode(',', $role));
        }
        if (false == $iscan) {
            return false;
        }
        if (false == PF::isEmpty($limit)) {
            $adminlimits = \Auth::guard('admin')->user()->limits;
            if ('' == $adminlimits) {
                $iscan = true; //如果要BY功能權限要設false
            } else {
                $iscan = in_array($limit, $adminlimits);
            }
        }

        return $iscan;
    }

    public function authorize($arr) {
        if (null == $arr['role'] && null != $arr['xmldoc'] && is_object($arr['xmldoc']) && null != $arr['controller']) {
            $objxml = $arr['xmldoc']->xpath("//參數設定檔/權限/選單/KIND[傳回值='" . $arr['controller'] . "']");
            if ('array' == gettype($objxml)) {
                $arr['role'] = strval($objxml[0]['角色']);
            } else {
                $objxml = $arr['xmldoc']->xpath("//參數設定檔/權限/選單/KIND/KIND[傳回值='" . $arr['controller'] . "']");
                if ('array' == gettype($objxml)) {
                    $arr['role'] = strval($objxml[0]['角色']);
                }
            }
        }
        $isuserlimit = $this->can($arr['role'], $arr['controller']);
        if (false == $isuserlimit) {
            throw new \CustomException('無此功能權限');
        }
    }
}
