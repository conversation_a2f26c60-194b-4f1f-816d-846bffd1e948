<?php

namespace Tests\Feature\api\admin;


/***
"功能名稱":"產品 單位測試",
"資料表":"product",
"建立時間":"2024-10-28 08:23:36 ",
 ***/
//command "php artisan test --filter tests/Feature/admin/productTest.php
class productTest extends baseTest {
  public $product;
  public function setUp(): void {
    parent::setUp();
    $this->kind = \App\Models\kind::factory()->create([
      'kind' => 'productkind',
    ]); //種類
    $this->product = \App\Models\product::factory()->create([
      'kind_id' => $this->kind->id,
    ]); //編輯日期
  }
  /**
   * 測試資料列表功能.
   *
   * @return void
   */
  public function test_列表index產品() {


    $datas = [

      'header' =>
      array(
        'HTTP_Authorization' => 'Bearer ' . $this->adminuser->api_token,
        'CONTENT_TYPE' => 'application/json',
      ),
      'url' => '/api/admin/product',
      'raw' =>
      array(
        'page' => 1,
        'pagesize' => 10,
        'searchname' =>  'kind_id',
        'search' => $this->product->kind_id,
      ),
      'post' => NULL,

    ];

    $response = $this->withHeaders($datas['header'])
      ->json('POST', $datas['url'], $datas['raw']);
    // echo $response->getStatusCode();
    //echo "response" . $response->getContent();

    $this->checkJson($response);

    //檢查資料表資料是否存在
    $this->assertDatabaseHas('product', [
      'id' => $this->product->id,
    ]);
    //檢查回傳資料是否存在
    $response
      ->assertStatus(200)
      ->assertJsonPath('data.data.0.kind_id', $this->product->kind_id);
    $jsonArray = $response->json();
    $this->assertGreaterThan(1, count($jsonArray['data']));
  }
  public function test_新增store產品() {
    //\PF::printr(["this->kind->id", $this->kind->id]);
    $datas = [

      'header' =>
      array(
        'HTTP_Authorization' => 'Bearer ' . $this->adminuser->api_token,
        'CONTENT_TYPE' => 'application/json',
      ),
      'url' => '/api/admin/product/store',
      'raw' =>
      array(
        'kind_id' => $this->kind->id,
        //種類-
        'number' =>  \Str::random(10), //編號-
        'title' => $this->faker->realText(20), //名稱型號-
        'feature' => $this->faker->realText(200), //特色-
        'img' => \Illuminate\Http\UploadedFile::fake()->image('test.png'),
        'location' => $this->myFaker->getXml('放置位置'), //放置位置-[home:首頁 ; ]
        'usehelp' => $this->faker->realText(200), //使用說明-
        'price' => $this->faker->randomDigit, //價格-
        'body' => $this->faker->realText(200), //其他-
        'begindate' => $this->myFaker->getDate(), //開始時間-
        'closedate' => $this->myFaker->getDate(), //結束時間-
        'hits' => $this->faker->randomDigit, //點率次數-
        'sortnum' => 1, //排序號碼-
        'adminuser_id' => '1', //編輯人員-
        'adminuser_name' => 'admin', //編輯人員-
        'created_at' => now(), //建立日期-

      ),
      'post' => NULL,

    ];
    $response = $this->withHeaders($datas['header'])
      ->json('POST', $datas['url'], $datas['raw']);

    // echo $response->getStatusCode();
    //echo "response" . $response->getContent();

    $this->checkJson($response);
    //檢查資料表資料是否存在
    $this->assertDatabaseHas('product', [
      'kind_id'       => $datas['raw']['kind_id'],
      'number'       => $datas['raw']['number']


    ]);
    //檢查檔案是否存在
    $rs = \App\Models\product::all()->last();
    $this->checkfile(public_path('images/product/' . $rs->img));
  }
  /**
   * 測試資料新增編輯寫入功能.
   *
   * @return void
   */
  public function test_編輯store產品() {

    $datas = [

      'header' =>
      array(
        'HTTP_Authorization' => 'Bearer ' . $this->adminuser->api_token,
        'CONTENT_TYPE' => 'application/json',
      ),
      'url' => '/api/admin/product/store',
      'raw' =>
      array(
        'kind_id' => $this->kind->id,
        'number' =>  \Str::random(10), //編號-
        'title' => $this->faker->realText(20), //名稱型號-
        'feature' => $this->faker->realText(200), //特色-
        'img' => \Illuminate\Http\UploadedFile::fake()->image('test.png'),
        'location' => $this->myFaker->getXml('放置位置'), //放置位置-[home:首頁 ; ]
        'usehelp' => $this->faker->realText(200), //使用說明-
        'price' => $this->faker->randomDigit, //價格-
        'body' => $this->faker->realText(200), //其他-
        'begindate' => $this->myFaker->getDate(), //開始時間-
        'closedate' => $this->myFaker->getDate(), //結束時間-
        'id' => $this->product->id,

      ),
      'post' => NULL,

    ];

    $response = $this->withHeaders($datas['header'])
      ->json('POST', $datas['url'], $datas['raw']);
    // echo $response->getStatusCode();
    //echo "response" . $response->getContent();

    $this->checkJson($response);
    //檢查資料表資料是否存在
    $this->assertDatabaseHas('product', [
      'kind_id'       => $datas['raw']['kind_id'],
      'number'       => $datas['raw']['number'],
      'title'       => $datas['raw']['title'],
      'feature'       => $datas['raw']['feature'],


    ]);
    //檢查檔案是否存在
    $rs = \App\Models\product::all()->last();
    $this->checkfile(public_path('images/product/' . $rs->img));
  }
  /**
   * 測試資料刪除功能.
   *
   * @return void
   */
  public function test_刪除destroy產品() {

    $this->product = \App\Models\product::factory()->create([
      'img' => $this->myFaker->getImage('images/product'),   //圖片

    ]);
    $datas = [
      'header' =>
      array(
        'HTTP_Authorization' => 'Bearer ' . $this->adminuser->api_token,
        'CONTENT_TYPE' => 'application/json',
      ),
      'url' => '/api/admin/product/destroy',
      'raw' =>
      array(
        'del' => $this->product->id,
      ),
    ];
    $response = $this->withHeaders($datas['header'])
      ->json('POST', $datas['url'], $datas['raw']);

    // echo $response->getStatusCode();
    echo $response->getContent();

    $this->checkJson($response);
    $response->assertStatus(200)->assertJson([
      'resultcode' => 0,
      'resultmessage' => '刪除成功'
    ]);
    $this->assertDatabaseMissing('product', [
      'id'       => $this->product->id,
    ]);
    $this->assertFileDoesNotExist(
      public_path('images/building/' . $this->product->img)
    );
  }
}
