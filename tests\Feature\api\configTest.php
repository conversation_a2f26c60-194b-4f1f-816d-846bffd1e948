<?php

namespace Tests\Feature\api;

use Illuminate\Foundation\Testing\RefreshDatabase;

/**
 * 系統參數控制器測試類
 */
class configTest extends baseTest {
    /**
     * 設定測試環境
     */
    public function setUp(): void {
        parent::setUp();
        
        // 不需要特別建立測試資料，因為系統參數是從config檔案讀取
    }
    
    /**
     * 測試取得系統參數設定_index
     */
    public function test_取得系統參數設定_index() {
        // 發送請求取得系統參數設定
        $response = $this->get('/api/config');
        
        // 檢查回應狀態碼
        $response->assertStatus(200);
        
        // 檢查JSON回應格式
        $this->checkJson($response);
        
        // 檢查回應中包含預期的資料結構
        $response->assertJsonPath('resultcode', 0);
        $response->assertJsonPath('resultmessage', '');
        
        // 檢查回應中包含config資料
        $response->assertJsonStructure([
            'resultcode',
            'resultmessage',
            'data' => [
                'config'
            ]
        ]);
        
        // 檢查回應中包含config.php中的特定設定值
        $response->assertJsonPath('data.config.name', \config('config.name'));
        $response->assertJsonPath('data.config.title', \config('config.title'));
        
        // 確認不包含upload相關的設定
        $responseData = $response->json();
        $this->assertArrayNotHasKey('uploadfilelimit', $responseData['data']['config']);
        $this->assertArrayNotHasKey('uploadfilesize', $responseData['data']['config']);
    }
}