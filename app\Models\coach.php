<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/*swagger api document start*/

/**
 * @OA\Schema(
 *   schema="coach",
 *      allOf={
 *         @OA\Schema( @OA\Property(property="id", type="integer", description="教練編號", example="10001")),
 *         @OA\Schema( @OA\Property(property="member_id", type="integer", description="會員編號", example="10001")),
 *         @OA\Schema( @OA\Property(property="license_number", type="string", description="證照號碼", example="SKI123456")),
 *         @OA\Schema( @OA\Property(property="certification_type", type="integer", description="認證類型", example="1")),
 *         @OA\Schema( @OA\Property(property="coach_status", type="integer", description="教練狀態", example="2")),
 *         @OA\Schema( @OA\Property(property="experience", type="string", description="教學經驗", example="擁有5年滑雪教學經驗")),
 *         @OA\Schema( @OA\Property(property="speciality", type="string", description="專長項目", example="基礎滑雪、進階技巧")),
 *         @OA\Schema( @OA\Property(property="hourly_rate", type="number", description="時薪", example="1500.00")),
 *         @OA\Schema( @OA\Property(property="years_experience", type="integer", description="教學年資", example="5")),
 *         @OA\Schema( @OA\Property(property="certification_documents", type="string", description="認證文件", example="cert1.pdf,cert2.pdf")),
 *         @OA\Schema( @OA\Property(property="certification_date", type="string", description="認證日期", example="2023-01-01")),
 *         @OA\Schema( @OA\Property(property="certification_expiry", type="string", description="認證到期日", example="2025-01-01")),
 *         @OA\Schema( @OA\Property(property="rating", type="number", description="評分", example="4.5")),
 *         @OA\Schema( @OA\Property(property="total_reviews", type="integer", description="評價總數", example="25")),
 *         @OA\Schema( @OA\Property(property="created_at", type="string", description="建立時間", example="2024-01-01 10:00:00")),
 *         @OA\Schema( @OA\Property(property="updated_at", type="string", description="更新時間", example="2024-01-01 10:00:00")),
 *      }
 *)
 */
/*swagger api document end*/

class coach extends Model {
    use HasFactory;

    /**
     * 資料表名稱
     */
    protected $table = 'coach';

    /**
     * 可批量賦值的屬性
     */
    protected $fillable = [
        'member_id',
        'license_number',
        'certification_type',
        'coach_status',
        'experience',
        'speciality',
        'hourly_rate',
        'years_experience',
        'certification_documents',
        'certification_date',
        'certification_expiry',
        'rating',
        'total_reviews',
    ];

    /**
     * 屬性轉換
     */
    protected $casts = [
        'certification_date' => 'date',
        'certification_expiry' => 'date',
        'hourly_rate' => 'decimal:2',
        'rating' => 'decimal:2',
    ];

    /*Relations start*/
    /**
     * 與 member 表的關聯 - 所屬會員
     */
    public function member() {
        return $this->belongsTo(member::class, 'member_id', 'id');
    }

    /**
     * 與 course 表的關聯 - 教練的課程
     */
    public function courses() {
        return $this->hasMany(course::class, 'coach_id', 'id');
    }

    /**
     * 與 booking 表的關聯 - 教練的預約記錄
     */
    public function bookings() {
        return $this->hasMany(booking::class, 'coach_id', 'id');
    }

    /**
     * 與 review 表的關聯 - 教練的評價記錄
     */
    public function reviews() {
        return $this->hasMany(review::class, 'coach_id', 'id');
    }
    /*Relations end*/

    /**
     * 模型事件
     */
    public static function boot() {
        parent::boot();

        static::creating(function ($model) {
            // 建立時的邏輯處理
        });

        static::updating(function ($model) {
            // 更新時的邏輯處理
        });

        static::deleted(function ($model) {
            /*Del Relations start*/
            // 刪除教練時，同時刪除相關資料
            $model->courses()->delete();
            $model->bookings()->update(['coach_id' => null]);
            $model->reviews()->delete();
            /*Del Relations end*/
        });
    }
}
