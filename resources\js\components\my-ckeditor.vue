<template>
    <textarea cols="80" :id="editorName" :name="editorName" rows="10" class="form-control">
        {{ modelValue }}
    </textarea>
</template>

<script setup lang="ts">
import { useRouter, useRoute } from 'vue-router'
const router = useRouter()
const route = useRoute()
import { onMounted, ref, watch, onUnmounted } from 'vue'
import { useDataStore } from '@/stores' //資料儲存套件
const store = useDataStore()

const props = defineProps({
    modelValue: String
})

const editorName = ref<string>('editor_' + Math.floor(Math.random() * 50)) //要加.value
const emits = defineEmits(['update:modelValue'])

// 에디터를 담을 변수 만들기
let ckEditor: any = null
// 에디터의 내용을 담을 변수
const param = ref({ contents: '' })

const createCkEditor = () => {
    try {
        ckEditor = window.CKEDITOR.replace(editorName.value, {
            toolbar_Full: [
                ['Source', '-', 'Save', 'NewPage', 'Preview', '-', 'Templates'],
                [
                    'Cut',
                    'Copy',
                    'Paste',
                    'PasteText',
                    'PasteFromWord',
                    '-',
                    'Print',
                    'SpellChecker',
                    'Scayt'
                ],
                ['Undo', 'Redo', '-', 'Find', 'Replace', '-', 'SelectAll', 'RemoveFormat'],

                '/',
                ['Bold', 'Italic', 'Underline', 'Strike', '-', 'Subscript', 'Superscript'],
                ['NumberedList', 'BulletedList', '-', 'Outdent', 'Indent', 'Blockquote'],
                ['JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock'],
                ['Link', 'Unlink', 'Anchor'],
                ['Image', 'Table', 'HorizontalRule', 'Smiley', 'SpecialChar', 'PageBreak'],
                '/',
                ['Styles', 'Format', 'Font', 'FontSize'],
                ['TextColor', 'BGColor'],
                ['Maximize', 'ShowBlocks', '-', 'About']
            ],

            //filebrowserUploadUrl: `/api/admin/upload/ckeditor`,
            // removePlugins:
            //     'form,checkbox,radio,TextField,Textarea,Select,Button,ImageButtonHiddenField,flash',
            uploadUrl: `${utils.getConfig('API_BASE')}api/admin/upload/ckeditor?token=${
                store.adminuser.api_token
            }`,
            //filebrowserBrowseUrl: `../api/admin/upload/ckeditor`,
            //filebrowserImageBrowseUrl: `/api/admin/upload/ckeditor`,
            filebrowserImageUploadUrl: `${utils.getConfig(
                'API_BASE'
            )}api/admin/upload/ckeditor?token=${store.adminuser.api_token}`,
            autoGrow_minHeight: 220,
            autoGrow_maxHeight: 600,
            width: '100%',
            language: 'zh-tw',
            toolbar: 'Full',
            height: 500,
            allowedContent: true,
            extraAllowedContent: 'i;span;ul;li;table;td;style;*[id];*(*);*{*}',
            ignoreEmptyParagraph: false,
            enterMode: window.CKEDITOR.ENTER_BR,
            shiftEnterMode: window.CKEDITOR.ENTER_P,
            entities: false,
            format_tags: 'p;h1;h2;h3;pre',
            fontSize_sizes:
                '8/8px;9/9px;10/10px;11/11px;12/12px;13/13px;14/14px;16/16px;18/18px;20/20px;22/22px;24/24px;26/26px;28/28px;36/36px;48/48px;72/72px',

            entities_processNumerical: true,
            font_names:
                '細明體;微軟正黑體;Arial;Helvetica;sans-serif;Comic Sans MS;cursive;Courier New; Courier;monospace;Georgia;serif;Lucida Sans Unicode; Lucida Grande, sans-serif;Tahoma; Geneva; sans-serif;Times New Roman;Times;serif;Trebuchet MS; Helvetica, sans-serif;Verdana; sans-serif'
        })

        ckEditor.on('fileUploadRequest', evt => {
            const xhr = evt.data.fileLoader.xhr
            xhr.setRequestHeader('Authorization', `Bearer ${store.adminuser.api_token}`)
            xhr.setRequestHeader('enctype', `multipart/form-data; charset=utf-8`)
        })

        ckEditor.on('fileUploadResponse', evt => {
            //console.clear();
            console.log(['fileUploadResponse'])
            evt.stop()
            const data = evt.data,
                xhr = data.fileLoader.xhr,
                response = xhr.responseText.split('|')

            if (response[1]) {
                data.message = response[1]
                evt.cancel()
            } else {
                try {
                    const jsonObject = JSON.parse(response[0])
                    const fileName = jsonObject.resultData.fileName
                    data.url =
                        import.meta.env.VITE_APP_API_SERVER_URL + '/file/image/download/' + fileName
                } catch (error) {
                    console.error(error)
                    evt.cancel()
                }
            }
        })
        ckEditor.on('fileUploadRequest', function (evt) {
            //console.clear();
            console.log(['fileUploadRequest'])
            var xhr = evt.data.fileLoader.xhr

            xhr.setRequestHeader('Cache-Control', 'no-cache')
            xhr.setRequestHeader('X-File-Name', fileName)
            xhr.setRequestHeader('X-File-Size', total)
            xhr.send(file)

            // Prevented the default behavior.
            evt.stop()
        })

        // 에디터의 내용이 변결될때 마다 param.contents에 값이 할당되도록 설정

        ckEditor.on('instanceReady', () => {
            //console.clear();
            //console.log(['instanceReady'])
            ckEditor.on('blur', function (event) {
                // console.log('編輯器失去焦點')
                emits('update:modelValue', ckEditor.getData())
            })
            ckEditor.on('change', () => {
                // console.log('change')
                emits('update:modelValue', ckEditor.getData())
            })
            ckEditor.on('key', () => {
                console.log('key')
                isload.value = true
                emits('update:modelValue', ckEditor.getData())
            })
            //ckEditor.fire('saveSnapshot')
        })
    } catch (error) {
        console.error(error)
    }
}

const beforeDestroy = async (): Promise<void> => {
    if (ckEditor) {
        ckEditor.destroy()
        ckEditor = null // 防止其他地方引用到了已销毁的实例
    }
}
let isload = ref<boolean>(false) //要加.value
watch(
    () => props.modelValue,
    async newValue => {
        if (ckEditor) {
            console.log(['newValue', newValue])
            // 确保 editor 已经初始化
            if (newValue !== undefined && newValue !== null) {
                try {
                    if (isload.value == false) {
                        ckEditor.setData(newValue)
                    }
                    // }
                } catch (error) {
                    console.error('设置 editor 数据时出错:', error)
                }
            }
        }
    },
    { deep: true, immediate: true }
)

onMounted(async () => {
    setTimeout(
        function () {
            createCkEditor()
        }.bind(this),
        1000
    )

    //     setInterval(
    //         function () {
    //             console.log(['window.CKEDITOR', window.CKEDITOR])
    //         }.bind(this),
    //         3000
    //     )
})
const destroyCkEditor = async () => {
    if (ckEditor) {
        await ckEditor.destroy()
        ckEditor = null // 避免重複引用
    }
}
watch(
    () => route.fullPath,
    async () => {
        //ckEditor.setData('')
        isload.value = false
        // await destroyCkEditor() // 銷毀 CKEditor
        // createCkEditor() // 重新初始化
    }
)
onUnmounted(() => {
    destroyCkEditor()
})
</script>

<style scoped></style>
