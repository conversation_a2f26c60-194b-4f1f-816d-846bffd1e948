<template>
    <div class="common-layout">
        <el-container>
            <el-aside width="200px" v-show="!isCollapse">
                <el-menu
                    :background-color="state.bgcolor"
                    text-color="#FFFFFF"
                    active-text-color="#409eff"
                    class="el-menu-vertical-demo"
                    default-active="2"
                    v-loading="http.getLoading()"
                >
                    <el-menu-item index="1">
                        <template #title> <span class="menu-title"> 管理介面</span></template>
                    </el-menu-item>

                    <el-sub-menu :index="String(index)" v-for="(rs, index) in data.data">
                        <template #title>
                            <span class="menu-title">{{ rs.title }}</span>
                        </template>
                        <template v-for="(rs2, index2) in rs.data">
                            <template v-if="rs2.data && rs2.data.length > 0">
                                <el-sub-menu :index="String(index + '-' + index2)">
                                    <template #title
                                        ><span class="menu-title">{{ rs2.title }}</span></template
                                    >
                                    <el-menu-item
                                        :index="String(index + '-' + index2 + '-' + index3)"
                                        v-for="(rs3, index3) in rs2.data"
                                        @click="navigateToUrl(rs3.url)"
                                    >
                                        <span class="menu-title">{{ rs3.title }}</span></el-menu-item
                                    >
                                </el-sub-menu>
                            </template>

                            <template v-else>
                                <el-menu-item :index="index + '-' + index2" @click="navigateToUrl(rs2.url)">
                                    <span class="menu-title">{{ rs2.title }}</span>
                                </el-menu-item>
                            </template>
                        </template>
                    </el-sub-menu>
                </el-menu>
            </el-aside>
            <el-container>
                <div v-if="showOverlay" class="overlay" @click="closeMenu"></div>
                <el-header class="header-content">
                    <div class="row">
                        <div class="col-9 text-start">
                            <el-button @click.prevent="toggleCollapse" class="toggle-collapse-btn">
                                <el-icon :size="20"><component :is="isCollapse ? 'Expand' : 'Fold'" /></el-icon>
                            </el-button>

                            {{ store.adminuser.name }}(
                            {{ utils.getXmlSearch('角色', store.adminuser?.role) }}
                            )
                        </div>
                        <div class="col-3 ms-auto text-end">
                            <el-button @click="logout" class="logout-btn">
                                <el-icon :size="20"><SwitchButton /></el-icon>
                            </el-button>
                        </div>
                    </div>
                </el-header>

                <el-main class="content p-3">
                    <div class="card card-success">
                        <div class="card-body">
                            <NuxtLoadingIndicator />
                            <slot />
                        </div>
                    </div>
                </el-main>
            </el-container>
        </el-container>
    </div>
</template>
<script setup lang="ts">
useHead({
    title: '管理介面',

    script: [
        {
            src: '/ckeditor/ckeditor.js',
            crossorigin: 'anonymous', // Optional: Add crossorigin attribute
            defer: true,
            async: true
        }
    ]
})

import { ref, reactive, getCurrentInstance, onMounted, computed, nextTick, watch } from 'vue'
const http = createHttp()
const http1 = createHttp()
const store = useDataStore()
const router = useRouter()
//console.log(["store.setup", store.setup]);
//console.log([store.config.name]);
// const props = defineProps({
//     edit: {
//         required: true,
//         type: String,
//     },
// });/

// 使用computed來監聽Vuex狀態
const state = reactive({
    //不用加.value
    bgcolor: '#304156'
    //data: [] as any[],
})
// 使用 reactive 定義對象
const data = reactive({
    //不用加.value
})
const inputs = reactive({
    // account: "",
    // password: "",
})

const getData = async () => {
    try {
        let rep = await http1.post('api/admin/main', inputs)
        if (rep.resultcode == '0') {
            Object.assign(data, rep.data)
            store.adminuser.navs = rep.data.navs
            //console.clear();
            //console.log(['data', data])
        } else {
            throw new Error(rep.resultmessage)
        }
    } catch (error) {
        utils.formElError(error)

        console.error(error)
    } finally {
    }
}

const nav = ref('')
// function updateNavTitle(newValue) {
//     nav.value = newValue;
// }
onMounted(() => {
    //store.getConfigSetup();
    getData()
    checkMobile()
    window.addEventListener('resize', checkMobile)
})

const isCollapse = ref<Boolean>(false)
const isMobile = ref<Boolean>(false)
const handleOpen = (key: string, keyPath: string[]) => {
    console.log(key, keyPath)
}
const handleClose = (key: string, keyPath: string[]) => {
    console.log(key, keyPath)
}
const navigateToUrl = url => {
    // 获取查询参数并转换为对象

    const queryParams = parseUrlParams(url)

    // // console.log(["queryParams", queryParams]);
    // url = url.split('?')[0]
    // //console.clear();
    // console.log(['url', url])
    // //console.clear();
    // console.log(['queryParams', queryParams])
    //console.log(["'/admin/' + url", '/admin/' + url])
    navigateTo({ path: '/admin/' + url, query: queryParams }, { force: true })
    //router.push({ path: "/admin/viewfile/edit", query: queryParams });
    //router.push({ path: "/admin/" + url });
    closeMenu()
    // root.$router.push(url);
}
const parseUrlParams = url => {
    const queryString = url.split('?')[1]
    const searchParams = new URLSearchParams(queryString)
    const queryParams = {}
    for (const [key, value] of searchParams) {
        queryParams[key] = value
    }
    return queryParams
}
const showOverlay = computed(() => {
    return isMobile.value && !isCollapse.value
})
// 計算側邊欄寬度
// 檢查是否為移動設備
const checkMobile = () => {
    const wasMobile = isMobile.value
    isMobile.value = window.innerWidth < 768

    // 只在從非移動切換到移動狀態時折疊菜單
    if (!wasMobile && isMobile.value) {
        isCollapse.value = true
    } else {
        isCollapse.value = false
    }
}

// // 計算側邊欄寬度
// const sideWidth = computed(() => {
//     if (isMobile.value) {
//         return isCollapse.value ? "0" : "80%";
//     }

//     return isCollapse.value ? "2px" : "200px";
// });

const toggleCollapse = () => {
    isCollapse.value = !isCollapse.value // 相互取反，点击true，在点击false
}
const closeMenu = async () => {
    if (isMobile.value) {
        await nextTick()
        isCollapse.value = true
    }
}
const logout = async (): Promise<void> => {
    try {
        let rep = await http1.post('api/admin/logout', {})

        if (rep.resultcode == '0') {
            let account = ''
            if (store.adminuser.iskeep) {
                account = store.adminuser.account
            }
            utils.mapClear(store.adminuser)
            store.adminuser.account = account
        } else {
            throw new Error(rep.resultmessage)
        }
    } catch (error: any) {
        utils.formElError(error)
        console.error(error)
    }
    router.push('/admin')
    utils.toast('登出成功')

    // Implement your logout logic here
}
watch(
    () => store.adminuser.api_token,
    async newValue => {
        if (newValue == '') {
            //router.replace("/admin/login");
        }
    },

    { deep: true } //, immediate: true
)
</script>
<style scoped>
.el-container,
.el-menu,
.el-main {
    height: calc(100vh);
    --el-main-padding: 0px;
}
.el-menu-container:not(.el-menu--collapse) {
    width: 200px;
    height: calc(100vh);
}
.el-menu--collapse {
    width: 64px;
}

.el-aside {
    transition: width 0.3s;
    z-index: 1001;
    left: 0;
    top: 0;
    bottom: 0;
    overflow-x: hidden; /* 防止內容溢出 */
}
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9;
}
.el-menu {
    transition: width 0.3s ease-in-out;
    border-right: none;
}

@media (max-width: 768px) {
    .el-main {
        padding-left: 0;
    }
    .el-aside {
        position: fixed;

        left: 0;
        top: 0;
        bottom: 0;
    }
}

.el-header {
    padding-left: 10px; /* Reduce left padding */
    padding-top: 10px; /* Add some top padding */
}

.header-content {
    height: 50px;

    top: 0;
    width: 100%;
    z-index: 8;

    background-color: #fff; /* 根据需要调整背景颜色 */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* 可选：添加阴影效果 */
}

.el-button.toggle-collapse-btn {
    border: none;
    background: transparent;
    padding: 0;
    margin: 0;
    display: inline-block;
    vertical-align: middle;
}
.logout-btn {
    border: none;
    background: transparent;
    padding: 0;
    margin: 0;
    text-align: right;
}
.header-icon {
    font-size: 24px; /* Increase this value to make icons larger */
    width: 1em;
    height: 1em;
}
.menu-title {
    font-size: 16px; /* Adjust this value to make the text larger */
    font-weight: 500;
}

.slide-enter-active,
.slide-leave-active {
    transition: width 0.3s;
}
.right {
    float: right;
    height: 100%;
}
.el-main {
    background: #f6f6f6;
}
.el-aside,
.el-sub-menu,
.el-sub-menu__title {
    background-color: #304156 !important;
}
.el-card {
    width: 100%;
    padding: 0px;
}
</style>
